<!DOCTYPE html>
<html>
<head>
    <title>Canvas Test</title>
    <style>
        body { margin: 0; background: #222; }
        canvas { border: 1px solid #fff; }
    </style>
</head>
<body>
    <canvas id="canvas" width="800" height="600"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        console.log('Canvas test starting...');
        
        // Test basic drawing
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(100, 100, 50, 50);
        
        ctx.fillStyle = '#00ff00';
        ctx.beginPath();
        ctx.arc(200, 200, 25, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '20px Arial';
        ctx.fillText('Canvas Test Working!', 50, 50);
        
        console.log('Canvas test complete');
    </script>
</body>
</html>