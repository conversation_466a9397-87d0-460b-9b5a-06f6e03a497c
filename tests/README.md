# Tests

This directory contains test files for the Snakepit game project.

## Test Files

### Game Logic Tests
- `test-friendly-fire.js` - Tests for friendly fire mechanics
- `test-simple-shoot.js` - Basic shooting functionality tests
- `test-warfare-simple.js` - Simple warfare mechanics tests
- `test-warfare.js` - Advanced warfare mechanics tests

### Server Tests
- `test-server.js` - Server functionality and API tests

### HTML Test Files
- `test.html` - General HTML test page
- `readability-test.html` - UI readability and accessibility tests

## Running Tests

To run the JavaScript test files:
```bash
node tests/test-filename.js
```

To view HTML test files, open them in a web browser:
```bash
open tests/test.html
open tests/readability-test.html
```

## Test Organization

Tests are organized by functionality:
- **Game mechanics**: Combat, shooting, warfare systems
- **Server functionality**: API endpoints, real-time communication
- **UI/UX**: Visual and accessibility testing
