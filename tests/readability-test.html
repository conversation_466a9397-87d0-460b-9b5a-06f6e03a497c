<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnakePit Theme - Readability Test</title>
    <link rel="stylesheet" href="../src/snakepit-theme.css">
    <style>
        body {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 6px;
        }
        
        .before {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid rgba(255, 0, 0, 0.3);
        }
        
        .after {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        
        .old-style {
            text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 20px currentColor;
        }
        
        .new-style {
            text-shadow: var(--glow-text) currentColor;
        }
    </style>
</head>
<body>
    <h1 class="neon-text" style="color: var(--neon-green); text-align: center;">
        SnakePit Theme Readability Improvements
    </h1>
    
    <div class="test-section">
        <h2 style="color: var(--neon-cyan);">Text Shadow Comparison</h2>
        <div class="comparison">
            <div class="before">
                <h3>Before (Heavy Glow)</h3>
                <p class="old-style" style="color: var(--neon-green);">
                    This text has multiple heavy text shadows that can make it hard to read, especially on different backgrounds.
                </p>
                <p class="old-style" style="color: var(--neon-cyan);">
                    The excessive glow can cause eye strain and reduce readability.
                </p>
            </div>
            <div class="after">
                <h3>After (Subtle Glow)</h3>
                <p class="new-style" style="color: var(--neon-green);">
                    This text uses reduced glow effects that maintain the neon aesthetic while being much easier to read.
                </p>
                <p class="new-style" style="color: var(--neon-cyan);">
                    The subtle glow preserves the theme but improves readability significantly.
                </p>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2 style="color: var(--neon-yellow);">Enhanced Readability Classes</h2>
        
        <div style="margin: 20px 0;">
            <h3>High Contrast Text</h3>
            <p class="high-contrast-text">
                This text uses a dark background overlay to ensure maximum readability while maintaining the theme.
            </p>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>Game UI Text</h3>
            <p class="game-ui-text" style="color: var(--neon-green);">
                Perfect for game interface elements that need to be clearly readable.
            </p>
        </div>
        
        <div style="margin: 20px 0;">
            <h3>Important Text</h3>
            <p class="important-text">
                Critical information that needs maximum visibility and contrast.
            </p>
        </div>
    </div>
    
    <div class="test-section">
        <h2 style="color: var(--neon-pink);">UI Panel Example</h2>
        <div class="ui-panel" style="padding: 20px; margin: 20px 0;">
            <h3 style="color: var(--neon-cyan); margin-top: 0;">Game Statistics</h3>
            <div class="final-stat">Score: 15,420</div>
            <div class="final-stat">Level: 12</div>
            <div class="final-stat">Time: 5:23</div>
        </div>
    </div>
    
    <div class="test-section">
        <h2 style="color: var(--neon-orange);">Balance Display</h2>
        <div style="text-align: center; padding: 20px;">
            <div class="balance-amount" style="color: var(--neon-yellow);">
                $1,234.56
            </div>
            <p style="color: var(--text-secondary);">Current Balance</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2 style="color: var(--neon-purple);">Form Elements</h2>
        <div style="max-width: 400px;">
            <label style="color: var(--text-secondary); display: block; margin-bottom: 8px;">Username:</label>
            <input type="text" class="form-element" placeholder="Enter username" style="width: 100%; margin-bottom: 15px;">
            
            <label style="color: var(--text-secondary); display: block; margin-bottom: 8px;">Email:</label>
            <input type="email" class="form-element" placeholder="Enter email" style="width: 100%; margin-bottom: 15px;">
            
            <button class="neon-button tone-down-glow" style="color: var(--neon-green); padding: 10px 20px;">
                Submit
            </button>
        </div>
    </div>
    
    <div class="test-section">
        <h2 style="color: var(--neon-red);">Key Improvements</h2>
        <ul style="color: var(--text-secondary); line-height: 1.6;">
            <li><strong>Reduced Glow Intensity:</strong> Text shadows and box shadows are now more subtle</li>
            <li><strong>Better Contrast:</strong> Added background overlays for critical text elements</li>
            <li><strong>Enhanced Variables:</strong> New CSS variables for subtle effects</li>
            <li><strong>Readability Classes:</strong> New utility classes for different text needs</li>
            <li><strong>Improved Form Elements:</strong> Better contrast and focus states</li>
            <li><strong>Maintained Aesthetic:</strong> Preserved the neon theme while improving usability</li>
        </ul>
    </div>
</body>
</html>
