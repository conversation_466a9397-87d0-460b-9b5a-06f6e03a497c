const path = require('path');

// Custom plugin to suppress specific warnings
class SuppressWarningsPlugin {
  apply(compiler) {
    compiler.hooks.afterEmit.tap('SuppressWarningsPlugin', (compilation) => {
      compilation.warnings = compilation.warnings.filter(warning => {
        const message = warning.message || warning.toString();
        return !(
          message.includes('Failed to parse source map') ||
          message.includes('@solana/buffer-layout') ||
          message.includes('superstruct') ||
          message.includes('source-map-loader')
        );
      });
    });
  }
}

module.exports = {
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      // Add custom plugin to suppress warnings
      webpackConfig.plugins.push(new SuppressWarningsPlugin());

      // Disable source map warnings for problematic packages
      webpackConfig.module.rules.forEach(rule => {
        if (rule.use && rule.use.some(use => use.loader && use.loader.includes('source-map-loader'))) {
          rule.exclude = [
            /node_modules\/@solana\/buffer-layout/,
            /node_modules\/superstruct/,
            ...(rule.exclude ? [rule.exclude] : [])
          ];
        }
      });

      // Add ignore warnings for specific modules
      webpackConfig.ignoreWarnings = [
        /Failed to parse source map/,
        /source-map-loader/,
        function(warning) {
          return warning.message && warning.message.includes('@solana/buffer-layout');
        },
        function(warning) {
          return warning.message && warning.message.includes('superstruct');
        }
      ];

      // Disable source maps in development to avoid warnings
      if (env === 'development') {
        webpackConfig.devtool = false;
      }

      // Fix fs.F_OK deprecation warning by using fs.constants.F_OK
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        fs: false,
        path: require.resolve('path-browserify'),
        crypto: require.resolve('crypto-browserify'),
        stream: require.resolve('stream-browserify'),
        buffer: require.resolve('buffer'),
        util: require.resolve('util')
      };

      return webpackConfig;
    }
  },
  devServer: {
    // Use the new setupMiddlewares instead of deprecated options
    setupMiddlewares: (middlewares, devServer) => {
      // Custom middleware setup can go here
      return middlewares;
    },
    // Remove deprecated options
    onBeforeSetupMiddleware: undefined,
    onAfterSetupMiddleware: undefined,
    // Suppress deprecation warnings
    client: {
      logging: 'warn',
      overlay: {
        warnings: false,
        errors: true
      }
    }
  }
};
