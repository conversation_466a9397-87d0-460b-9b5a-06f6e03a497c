<!DOCTYPE html>
<html>
<head>
    <title>Debug URL Test</title>
</head>
<body>
    <h1>URL Debug Test</h1>
    <button onclick="testURL()">Test Server URL</button>
    <div id="results"></div>

    <script>
        function testURL() {
            const SERVER_URL = process?.env?.REACT_APP_SERVER_URL || 'http://localhost:3005';
            const url = `${SERVER_URL}/api/game-results/start-wager`;
            
            console.log('SERVER_URL:', SERVER_URL);
            console.log('Full URL:', url);
            console.log('Window location:', window.location.href);
            
            document.getElementById('results').innerHTML = `
                <p><strong>SERVER_URL:</strong> ${SERVER_URL}</p>
                <p><strong>Full URL:</strong> ${url}</p>
                <p><strong>Window location:</strong> ${window.location.href}</p>
            `;
            
            // Test the actual fetch
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    gameMode: 'warfare',
                    wagerAmount: 50,
                    userId: 'test-user'
                }),
            })
            .then(response => response.json())
            .then(data => {
                console.log('Success:', data);
                document.getElementById('results').innerHTML += `<p><strong>Response:</strong> ${JSON.stringify(data)}</p>`;
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('results').innerHTML += `<p><strong>Error:</strong> ${error.message}</p>`;
            });
        }
    </script>
</body>
</html>
