* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #4CAF50;
    --primary-dark: #45a049;
    --secondary-color: #2196F3;
    --accent-color: #FF9800;
    --danger-color: #f44336;
    --dark-bg: #1a1a1a;
    --darker-bg: #141414;
    --card-bg: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --border-color: rgba(255, 255, 255, 0.2);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), #2ecc71);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color), #03a9f4);
    --gradient-accent: linear-gradient(135deg, var(--accent-color), #ff5722);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --transition-fast: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--darker-bg);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Animated Background */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: 
        radial-gradient(circle at 10% 20%, rgba(76, 175, 80, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(33, 150, 243, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 152, 0, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: bgAnimation 20s ease infinite;
}

@keyframes bgAnimation {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Navigation */
.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.8rem;
    font-weight: 700;
    font-family: 'Orbitron', sans-serif;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    gap: 1rem;
}

.nav-btn {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.nav-btn:hover {
    background: var(--card-bg);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.nav-btn.primary-btn {
    background: var(--gradient-primary);
    border: none;
    color: white;
}

.nav-btn.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 6rem 2rem 2rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.glowing-text {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    animation: glow 2s ease-in-out infinite;
}

@keyframes glow {
    0%, 100% { text-shadow: 0 0 20px rgba(76, 175, 80, 0.3); }
    50% { text-shadow: 0 0 40px rgba(76, 175, 80, 0.5); }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-item i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Game Modes Section */
.game-modes {
    padding: 4rem 2rem;
    background: var(--dark-bg);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.mode-cards {
    display: flex;
    justify-content: center;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    flex-wrap: wrap;
}

.mode-card {
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    width: 400px;
    transition: all var(--transition-fast);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.mode-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-fast);
    z-index: 0;
}

.mode-card:hover::before {
    opacity: 0.05;
}

.mode-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.mode-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 1;
}

.mode-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.mode-card.warfare .mode-icon {
    background: var(--gradient-accent);
}

.mode-card h2 {
    font-size: 1.8rem;
    margin: 0;
}

.player-count {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.player-count i.pulse {
    color: var(--primary-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.mode-content {
    position: relative;
    z-index: 1;
}

.mode-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-secondary);
}

.feature-item i {
    color: var(--primary-color);
}

.mode-card.warfare .feature-item i {
    color: var(--accent-color);
}

.play-btn {
    width: 100%;
    padding: 1rem;
    background: var(--gradient-primary);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    z-index: 1;
}

.mode-card.warfare .play-btn {
    background: var(--gradient-accent);
}

.play-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.mode-card.warfare .play-btn:hover {
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

/* Features Section */
.features-section {
    padding: 4rem 2rem;
    background: var(--darker-bg);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.feature-card {
    background: rgba(26, 26, 26, 0.95);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all var(--transition-fast);
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-secondary);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
}

.modal:not(.hidden) {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--dark-bg);
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    transform: translateY(20px);
    transition: all var(--transition-fast);
}

.modal:not(.hidden) .modal-content {
    transform: translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modal-header h2 i {
    color: var(--primary-color);
}

.close-modal {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    transition: color var(--transition-fast);
}

.close-modal:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 1.5rem;
}

/* Controls Section in How to Play Modal */
.controls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.control-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    transition: all var(--transition-fast);
}

.control-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.control-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-secondary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.control-info h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.control-info p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Game Modes Info in How to Play Modal */
.game-modes-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.mode-info {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.mode-info h3 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.mode-info.warfare h3 {
    color: var(--accent-color);
}

.mode-info ul {
    list-style: none;
}

.mode-info li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
}

.mode-info li i {
    color: var(--primary-color);
}

.mode-info.warfare li i {
    color: var(--accent-color);
}

/* Settings Modal */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.setting-item label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.setting-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem;
    color: var(--text-primary);
    width: 100%;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    transition: var(--transition-fast);
    border-radius: 34px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: var(--transition-fast);
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* Leaderboard Modal */
.leaderboard-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.tab-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.tab-btn:hover:not(.active) {
    border-color: var(--primary-color);
    color: var(--text-primary);
}

/* Preserve game-specific styles */
#gameCanvas {
    cursor: crosshair;
    display: block;
    background: #0a0a0a;
    border: 2px solid #333;
}

#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#ui {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#ui div {
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    border: 1px solid #444;
}

#weapon, #cooldown {
    color: var(--accent-color);
}

#score {
    color: var(--primary-color);
}

#length {
    color: var(--secondary-color);
}

#boost {
    color: var(--accent-color);
}

#minimapContainer {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
    border: 2px solid #444;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.8);
}

#minimap {
    display: block;
    border-radius: 3px;
}

/* Game over screen */
#gameOver {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 20;
    background: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 10px;
    border: 2px solid #444;
    text-align: center;
    min-width: 300px;
}

#gameOver h2 {
    color: var(--danger-color);
    margin-bottom: 20px;
    font-size: 28px;
}

#gameOver p {
    margin: 10px 0;
    font-size: 18px;
}

#restartBtn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 20px;
    transition: background 0.3s;
}

#restartBtn:hover {
    background: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-nav {
        flex-direction: column;
        padding: 1rem;
    }

    .nav-logo {
        margin-bottom: 1rem;
    }

    .nav-links {
        width: 100%;
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero-section {
        padding-top: 8rem;
    }

    .glowing-text {
        font-size: 2.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 2rem;
    }

    .mode-card {
        width: 100%;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .controls-grid {
        grid-template-columns: 1fr;
    }

    .game-modes-info {
        grid-template-columns: 1fr;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }
}

.hidden {
    display: none !important;
}