# Tools

This directory contains utility tools, scripts, and standalone files for the Snakepit project.

## Directory Structure

### `/html`
Standalone HTML files and related assets:
- `index.html` - Standalone game page with landing interface
- `debug-url.html` - URL debugging utility
- `style.css` - Styles for standalone HTML pages

### `/scripts`
Utility scripts and batch files:
- `server.py` - Python server utility
- `setup-firewall.bat` - Windows firewall setup script

## Usage

### HTML Tools
The HTML files in `/html` are standalone utilities that can be opened directly in a browser:
```bash
open tools/html/index.html
open tools/html/debug-url.html
```

### Scripts
Run utility scripts as needed:
```bash
# Python server
python tools/scripts/server.py

# Windows firewall setup (Windows only)
tools/scripts/setup-firewall.bat
```

## Notes

- The standalone `index.html` is different from `public/index.html` (React app template)
- These tools are separate from the main React application
- Scripts may have platform-specific requirements
