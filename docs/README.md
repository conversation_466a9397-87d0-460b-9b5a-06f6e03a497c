# Documentation

This directory contains all project documentation organized by category.

## Directory Structure

### `/setup`
Setup and configuration guides:
- `SUPABASE_SETUP.md` - Supabase database setup instructions
- `WALLET_GENERATION.md` - Wallet generation procedures

### `/guides`
User and developer guides:
- `MULTIPLAYER_CONVERSION.md` - Guide for converting to multiplayer functionality
- `SOL_BALANCE_TESTING_GUIDE.md` - Testing guide for Solana balance functionality

### `/integration`
Integration documentation:
- `SOLANA_INTEGRATION.md` - Solana blockchain integration documentation

## Quick Links

- [Supabase Setup](./setup/SUPABASE_SETUP.md)
- [Wallet Generation](./setup/WALLET_GENERATION.md)
- [Multiplayer Conversion](./guides/MULTIPLAYER_CONVERSION.md)
- [SOL Balance Testing](./guides/SOL_BALANCE_TESTING_GUIDE.md)
- [Solana Integration](./integration/SOLANA_INTEGRATION.md)
