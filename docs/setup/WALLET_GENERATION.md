# Solana Wallet Generation for Existing Accounts

This document explains how to generate Solana wallets for existing user accounts in the SnakePit project.

## Overview

When migrating from SOL to Solana, existing user accounts may not have Solana wallets. This toolkit provides several ways to generate wallets for these accounts:

1. **React Component** - Admin UI for wallet generation
2. **Utility Functions** - Programmatic wallet generation
3. **Command Line Script** - Batch wallet generation

## Files Created

### Core Utilities
- `src/utils/WalletGenerator.ts` - Core wallet generation functions
- `src/components/WalletGenerator.tsx` - React admin interface
- `src/scripts/generateWalletsForExistingUsers.ts` - Command line script

## Usage Methods

### 1. React Admin Interface

Use the `WalletGenerator` component in your admin panel:

```tsx
import { WalletGenerator } from '../components/WalletGenerator';

// In your admin component
const [showWalletGenerator, setShowWalletGenerator] = useState(false);

return (
  <div>
    <button onClick={() => setShowWalletGenerator(true)}>
      Generate Wallets
    </button>
    
    {showWalletGenerator && (
      <WalletGenerator onClose={() => setShowWalletGenerator(false)} />
    )}
  </div>
);
```

The component provides:
- Check how many users need wallets
- Generate wallet for specific user ID
- Batch generate wallets for all users
- View detailed results

### 2. Programmatic Usage

```tsx
import {
  generateWalletForExistingUser,
  generateWalletsForAllExistingUsers,
  getUsersWithoutWallets
} from '../utils/WalletGenerator';

// Generate wallet for specific user
const result = await generateWalletForExistingUser('user-id-here');
if (result.success) {
  console.log('Wallet created:', result.publicKey);
}

// Check users without wallets
const usersResult = await getUsersWithoutWallets();
console.log(`${usersResult.count} users need wallets`);

// Generate wallets for all users
const batchResult = await generateWalletsForAllExistingUsers();
console.log(`Generated ${batchResult.successful.length} wallets`);
```

### 3. Command Line Script

```bash
# Install dependencies if needed
npm install

# Run the script
npx ts-node src/scripts/generateWalletsForExistingUsers.ts
```

The script will:
1. Check all user profiles
2. Identify users without wallets
3. Generate wallets for those users
4. Provide detailed progress and summary

## Safety Features

### Idempotent Operations
- Running wallet generation multiple times is safe
- Existing wallets are never overwritten
- Functions check for existing wallets before creating new ones

### Error Handling
- Comprehensive error handling and logging
- Batch operations continue even if individual wallets fail
- Detailed error reporting for troubleshooting

### Security
- Private keys are encrypted using the same encryption as new wallets
- Uses the `REACT_APP_WALLET_ENCRYPTION_KEY` environment variable
- Wallets are stored in the `solana_wallets` table with proper relationships

## Database Schema

The wallet generation uses the existing database schema:

```sql
-- Wallets are stored in the solana_wallets table
CREATE TABLE solana_wallets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  public_key TEXT NOT NULL,
  encrypted_private_key TEXT NOT NULL,
  wallet_type TEXT NOT NULL DEFAULT 'user',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Environment Variables

Ensure these environment variables are set:

```env
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
REACT_APP_WALLET_ENCRYPTION_KEY=your_encryption_key
```

⚠️ **Important**: Use a strong encryption key in production, not the default!

## Migration Workflow

### For Development/Testing
1. Use the React component to check how many users need wallets
2. Generate wallets for a few test users first
3. Verify wallets are created correctly
4. Run batch generation for all users

### For Production
1. **Backup your database first**
2. Test the script on a staging environment
3. Run during low-traffic hours
4. Monitor the process and logs
5. Verify wallet creation was successful
6. Notify users about their new wallets

## Monitoring and Verification

### Check Wallet Creation
```sql
-- Count users with wallets
SELECT COUNT(*) FROM solana_wallets WHERE wallet_type = 'user' AND is_active = true;

-- Count total users
SELECT COUNT(*) FROM user_profiles;

-- Find users without wallets
SELECT up.id, up.username 
FROM user_profiles up
LEFT JOIN solana_wallets sw ON up.id = sw.user_id AND sw.wallet_type = 'user' AND sw.is_active = true
WHERE sw.user_id IS NULL;
```

### Verify Wallet Functionality
- Test wallet balance retrieval
- Test transaction creation
- Verify encryption/decryption works

## Troubleshooting

### Common Issues

1. **"Table 'solana_wallets' doesn't exist"**
   - Run the database migration first
   - Check `database/supabase-schema.sql`

2. **"Encryption key not set"**
   - Set `REACT_APP_WALLET_ENCRYPTION_KEY` environment variable
   - Don't use the default key in production

3. **"User ID mismatch"**
   - Ensure you're using the correct user ID format
   - Check the `user_profiles` table for valid IDs

4. **"Database timeout"**
   - Check your Supabase connection
   - Verify environment variables are correct

### Logs and Debugging

All functions provide detailed console logging:
- `🔄` - Processing
- `✅` - Success
- `❌` - Error
- `📊` - Statistics
- `⚠️` - Warning

## Best Practices

1. **Always test first** - Use a staging environment
2. **Monitor progress** - Watch logs during batch operations
3. **Backup data** - Before running in production
4. **Verify results** - Check wallet creation was successful
5. **Secure keys** - Use strong encryption keys
6. **User notification** - Inform users about their new wallets

## Support

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify your environment variables
3. Ensure database schema is up to date
4. Test with a single user first before batch operations

---

**Note**: This wallet generation system is designed to be safe and idempotent. However, always test thoroughly before using in production and ensure you have proper backups.