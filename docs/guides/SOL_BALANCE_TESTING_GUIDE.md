# SOL Balance Testing Guide

This guide provides comprehensive instructions for testing the SOL balance checking and updating functionality in the Snakepit application.

## 🚀 Features Implemented

### Core Components
- **WalletBalance Component**: Displays SOL balance with real-time updates
- **useWalletBalance Hook**: Manages balance state and monitoring
- **BalanceMonitorService**: Background service for real-time balance monitoring
- **BalanceNotification Component**: Shows balance change notifications

### Key Features
- ✅ Real-time balance monitoring (15-second intervals)
- ✅ Automatic database synchronization
- ✅ Live balance notifications
- ✅ Manual refresh capability
- ✅ Solana faucet integration
- ✅ Error handling and loading states
- ✅ Monitoring status indicators

## 🧪 Testing Scenarios

### 1. Initial Setup Testing

#### Prerequisites
- User account created and logged in
- Wallet generated for the user
- Application running on Solana devnet

#### Test Steps
1. **Login and Wallet Creation**
   ```bash
   # Start the application
   npm start
   ```
   - Login with existing user credentials
   - Verify wallet popup appears if no wallet exists
   - Check that wallet address is displayed
   - Confirm "Get Free SOL" button is visible

2. **Initial Balance Display**
   - Navigate to game interface
   - Locate SOL balance in CashTracker component
   - Verify balance shows "0.000000 SOL" for new wallets
   - Check for "🟢 Live" indicator (real-time monitoring)

### 2. Balance Funding Testing

#### Method 1: Using Built-in Faucet Button
1. Click "Get Free SOL" button in wallet popup
2. Complete faucet request on Solana faucet website
3. Wait 15-30 seconds for automatic balance update
4. Verify balance notification appears
5. Check balance updates in UI

#### Method 2: Direct Faucet Access
1. Copy wallet address from popup
2. Visit https://faucet.solana.com
3. Paste address and request SOL
4. Monitor for automatic balance update

#### Method 3: CLI Airdrop (for bulk testing)
```bash
# Install Solana CLI if not already installed
sh -c "$(curl -sSfL https://release.solana.com/v1.17.0/install)"

# Configure for devnet
solana config set --url https://api.devnet.solana.com

# Airdrop SOL to wallet
solana airdrop 1 <WALLET_PUBLIC_KEY>
```

### 3. Real-time Monitoring Testing

#### Test Real-time Updates
1. **Setup Monitoring**
   - Ensure user is logged in
   - Verify "🟢 Live" indicator is visible
   - Check browser console for monitoring logs

2. **Trigger Balance Changes**
   - Use faucet to add SOL
   - Send SOL from another wallet
   - Monitor for automatic updates (15-second intervals)

3. **Verify Notifications**
   - Check for balance change notifications in top-right corner
   - Verify notification shows correct amount change
   - Test notification dismissal

#### Expected Behavior
- Balance updates automatically within 15-30 seconds
- Notifications appear for changes > 0.000001 SOL
- Database synchronization occurs automatically
- Console logs show monitoring activity

### 4. Component Integration Testing

#### CashTracker Component
- **Location**: Main game interface
- **Features**: Displays SOL balance alongside game cash
- **Test**: Verify balance updates without page refresh

#### WalletPopup Component
- **Location**: Triggered from user profile or wallet creation
- **Features**: Shows balance with refresh button and faucet link
- **Test**: Manual refresh functionality and monitoring status

#### UserProfile Component
- **Location**: User profile overview tab
- **Features**: Displays stored balance from database
- **Test**: Verify balance synchronization with database

### 5. Error Handling Testing

#### Network Issues
1. **Disconnect Internet**
   - Verify error messages appear
   - Check graceful degradation
   - Test recovery when connection restored

2. **Invalid Wallet Address**
   - Manually corrupt wallet data in database
   - Verify error handling
   - Check fallback behavior

#### Rate Limiting
1. **Rapid Refresh Testing**
   - Click refresh button multiple times quickly
   - Verify rate limiting prevents spam
   - Check error messages for rate limits

### 6. Performance Testing

#### Memory Usage
1. **Monitor Service**
   - Open browser dev tools
   - Check memory usage over time
   - Verify no memory leaks from monitoring service

2. **Multiple Users**
   - Test with multiple browser tabs/users
   - Verify service handles multiple wallets
   - Check performance impact

#### Network Requests
1. **Monitor Network Tab**
   - Check frequency of balance requests
   - Verify efficient request patterns
   - Test request caching

## 🔧 Debugging Tools

### Browser Console Commands
```javascript
// Check monitoring service status
const monitor = window.BalanceMonitorService?.getInstance()
console.log(monitor?.getStatus())

// Force balance check
monitor?.forceCheck()

// Check current balance
const { getWalletBalance } = await import('./src/utils/SolanaWallet')
const balance = await getWalletBalance('YOUR_PUBLIC_KEY')
console.log('Current balance:', balance)
```

### Database Queries
```sql
-- Check user wallet data
SELECT id, username, solana_balance, wallet_public_key 
FROM user_profiles 
WHERE username = 'YOUR_USERNAME';

-- Check wallet records
SELECT user_id, public_key, created_at 
FROM solana_wallets 
WHERE user_id = 'USER_ID';
```

### Solana Explorer
- **Devnet Explorer**: https://explorer.solana.com/?cluster=devnet
- Search wallet address to verify transactions
- Check transaction history and confirmations

## 📊 Expected Test Results

### Successful Test Indicators
- ✅ Balance displays correctly (6 decimal places)
- ✅ Real-time updates work within 15-30 seconds
- ✅ Notifications appear for balance changes
- ✅ Manual refresh works instantly
- ✅ Database stays synchronized
- ✅ Error states display appropriately
- ✅ Monitoring status indicators work
- ✅ Faucet integration functions

### Performance Benchmarks
- Balance fetch time: < 2 seconds
- Update frequency: Every 15 seconds
- Notification delay: < 1 second after detection
- Memory usage: Stable over time
- Network requests: Efficient and cached

## 🐛 Common Issues and Solutions

### Issue: Balance Not Updating
**Symptoms**: Balance shows 0 or outdated value
**Solutions**:
1. Check network connection
2. Verify wallet address is correct
3. Confirm devnet configuration
4. Check browser console for errors
5. Try manual refresh

### Issue: Monitoring Not Active
**Symptoms**: No "🟢 Live" indicator
**Solutions**:
1. Check user authentication
2. Verify wallet exists in database
3. Check console for service errors
4. Restart application

### Issue: Notifications Not Appearing
**Symptoms**: Balance changes but no notifications
**Solutions**:
1. Check notification permissions
2. Verify change amount > minimum threshold
3. Check console for callback errors
4. Test with larger balance changes

### Issue: Database Sync Problems
**Symptoms**: UI balance differs from database
**Solutions**:
1. Check Supabase connection
2. Verify user permissions
3. Check RLS policies
4. Force manual sync

## 🚀 Next Steps After Testing

1. **Production Deployment**
   - Switch to Solana mainnet
   - Update RPC endpoints
   - Configure production database
   - Set up monitoring alerts

2. **Feature Enhancements**
   - Add transaction history
   - Implement SOL to game currency conversion
   - Add withdrawal functionality
   - Create balance alerts/thresholds

3. **Optimization**
   - Implement request caching
   - Add connection pooling
   - Optimize update intervals
   - Add performance monitoring

## 📞 Support

If you encounter issues during testing:
1. Check browser console for errors
2. Verify network connectivity
3. Confirm Solana devnet status
4. Review component props and configuration
5. Test with different browsers/devices

For additional support, refer to:
- Solana Documentation: https://docs.solana.com
- Supabase Documentation: https://supabase.com/docs
- React Documentation: https://react.dev