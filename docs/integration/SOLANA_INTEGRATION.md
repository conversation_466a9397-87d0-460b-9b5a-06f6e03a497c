# SnakePit Solana Integration

This document describes the complete Solana blockchain integration for the SnakePit game, providing real-money wagering and rewards using Solana's devnet/testnet.

## 🏗️ Architecture Overview

The Solana integration consists of several key components:

### 1. **Wallet System**
- **User Wallets**: Automatically created for each new user account
- **Room Escrow Wallets**: Created for each game room to hold wagers
- **Admin Wallet**: Collects fees from destroyed rooms

### 2. **Game Flow**
1. User signs up → Solana wallet automatically created
2. User funds wallet → Can join games
3. User joins game → Wager transferred to room escrow
4. User cashes out → Funds transferred from escrow to user wallet
5. Room destroyed → Remaining funds transferred to admin wallet

### 3. **Supabase Functions**
- `createUserWallet` - Creates wallets for new users
- `createRoomWallet` - Creates escrow wallets for game rooms
- `createAdminWallet` - Creates the admin wallet (run once)
- `validateGameEntry` - Validates user balance before game join
- `transferToRoom` - Transfers wager to room escrow
- `cashout` - Processes cashouts from room to user
- `cashoutFromRoom` - Transfers SOL from room to user
- `transferToAdmin` - Transfers remaining funds to admin
- `roomToAdmin` - Handles room destruction and fund transfer

## 🚀 Setup Instructions

### 1. Environment Variables

Add these to your `.env` file:

```bash
# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Wallet Encryption
WALLET_ENCRYPTION_KEY=your_secure_encryption_key_change_in_production

# React App Environment
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
REACT_APP_WALLET_ENCRYPTION_KEY=your_secure_encryption_key_change_in_production
```

### 1.1. Real-Time Price Integration

The system uses **real-time SOL prices** from Crypto.com API:
- No mock rates - all conversions use live market data
- 1-minute price caching for performance
- Automatic fallback to reasonable default if API fails
- Consistent pricing across all functions and UI components

### 2. Database Setup

Run the database schema and functions:

```bash
# Run in Supabase SQL Editor
psql -f database/supabase-schema.sql
psql -f database/supabase-functions.sql
```

### 3. Deploy Supabase Functions

```bash
# Deploy all Supabase functions
supabase functions deploy createUserWallet
supabase functions deploy createRoomWallet
supabase functions deploy createAdminWallet
supabase functions deploy validateGameEntry
supabase functions deploy transferToRoom
supabase functions deploy cashout
supabase functions deploy cashoutFromRoom
supabase functions deploy transferToAdmin
supabase functions deploy roomToAdmin
```

### 4. Initialize Admin Wallet

```bash
# Run once to create the admin wallet
node scripts/initializeAdminWallet.js
```

## 💰 Financial Flow

### User Registration
1. User creates account with Supabase Auth
2. `createUserWallet` function automatically called
3. User receives popup with wallet address and funding instructions
4. User funds wallet using Solana faucet (testnet) or real SOL (mainnet)

### Game Entry
1. User selects wager amount
2. `validateGameEntry` checks if user has sufficient balance
3. If valid, user can join game
4. `transferToRoom` moves wager from user wallet to room escrow

### Cashout
1. User cashes out in-game
2. Server calls `cashout` function
3. `cashoutFromRoom` transfers winnings from escrow to user wallet
4. Transaction recorded in database

### Room Cleanup
1. When room is destroyed (empty, timeout, etc.)
2. `roomToAdmin` function called
3. Any remaining escrow funds transferred to admin wallet
4. Room wallet marked as inactive

## 🔒 Security Features

### Wallet Encryption
- Private keys encrypted with AES before database storage
- Encryption key stored in environment variables
- Never expose private keys to client-side code

### Transaction Validation
- All transactions verified on-chain
- Balance checks before allowing game entry
- Rate limiting on financial operations
- Server-side validation of all amounts

### Access Control
- Row Level Security (RLS) enabled on all tables
- Users can only access their own wallet data
- Service role required for admin operations
- Proper CORS configuration for Supabase functions

## 🧪 Testing

### Testnet Setup
- Uses Solana devnet for testing
- Free SOL available from Solana faucet
- All transactions are test transactions

### Test Flow
1. Create test account
2. Fund wallet with faucet SOL
3. Join game with small wager
4. Play and cash out
5. Verify balance updates

## 📊 Monitoring

### Transaction Logging
- All transactions logged in `transactions` table
- Includes Solana signatures for verification
- Status tracking (pending, confirmed, failed)

### Balance Reconciliation
- Real-time balance monitoring
- Automatic sync between database and blockchain
- Alerts for discrepancies

## 🔧 Maintenance

### Regular Tasks
- Monitor admin wallet balance
- Check for failed transactions
- Clean up old room wallets
- Update SOL/USD conversion rates

### Troubleshooting
- Check Supabase function logs
- Verify environment variables
- Test network connectivity
- Validate wallet balances

## 🚨 Production Considerations

### Mainnet Deployment
- Change network from devnet to mainnet
- Update environment variables
- Test thoroughly with small amounts
- Implement proper monitoring

### Security Hardening
- Use hardware security modules for admin keys
- Implement multi-signature wallets
- Regular security audits
- Backup and recovery procedures

## 📈 Future Enhancements

- Multi-token support (USDC, other SPL tokens)
- Staking rewards for players
- NFT integration for achievements
- Cross-chain bridge support
- Advanced DeFi features

---

For technical support or questions, please refer to the main README or create an issue in the repository.
