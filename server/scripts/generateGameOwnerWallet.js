const { Keypair } = require('@solana/web3.js');
const bs58 = require('bs58');
const fs = require('fs');
const path = require('path');

/**
 * Generate a new game owner wallet and update the .env file
 */
function generateGameOwnerWallet() {
  console.log('🏦 Generating new Game Owner Wallet...');
  
  // Generate new keypair
  const keypair = Keypair.generate();
  const publicKey = keypair.publicKey.toBase58();
  const privateKey = bs58.default.encode(keypair.secretKey);
  
  console.log('✅ Generated new wallet:');
  console.log('Public Key:', publicKey);
  console.log('Private Key:', privateKey);
  
  // Update .env file
  const envPath = path.join(__dirname, '../.env');
  
  try {
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Replace or add the GAME_OWNER_PRIVATE_KEY line
    if (envContent.includes('GAME_OWNER_PRIVATE_KEY=')) {
      envContent = envContent.replace(
        /GAME_OWNER_PRIVATE_KEY=.*/,
        `GAME_OWNER_PRIVATE_KEY=${privateKey}`
      );
    } else {
      envContent += `\nGAME_OWNER_PRIVATE_KEY=${privateKey}\n`;
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log('✅ Updated .env file with new private key');
    
  } catch (error) {
    console.error('❌ Error updating .env file:', error);
    console.log('⚠️  Please manually add this line to your server/.env file:');
    console.log(`GAME_OWNER_PRIVATE_KEY=${privateKey}`);
  }
  
  console.log('\n🔐 IMPORTANT: Save this information securely!');
  console.log('📝 Game Owner Wallet Details:');
  console.log('   Public Key (share this):', publicKey);
  console.log('   Private Key (keep secret):', privateKey);
  console.log('\n⚠️  The private key has been added to your .env file.');
  console.log('   Make sure to backup this key securely!');
  console.log('\n💰 For development on devnet, you can request an airdrop:');
  console.log(`   solana airdrop 10 ${publicKey} --url devnet`);
  
  return { publicKey, privateKey };
}

// Run if called directly
if (require.main === module) {
  generateGameOwnerWallet();
}

module.exports = { generateGameOwnerWallet };
