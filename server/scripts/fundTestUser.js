const { Connection, Keypair, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL, sendAndConfirmTransaction } = require('@solana/web3.js');
const bs58 = require('bs58');
require('dotenv').config({ path: './server/.env' });

/**
 * Transfer SOL from game owner to test user for testing
 */
async function fundTestUser() {
  try {
    console.log('💰 Funding test user from game owner wallet...');
    
    // Initialize connection
    const connection = new Connection('https://api.devnet.solana.com', 'confirmed');
    
    // Get game owner keypair
    const gameOwnerPrivateKey = process.env.GAME_OWNER_PRIVATE_KEY;
    if (!gameOwnerPrivateKey) {
      throw new Error('GAME_OWNER_PRIVATE_KEY not found in environment');
    }
    
    const secretKey = bs58.default.decode(gameOwnerPrivateKey);
    const gameOwnerKeypair = Keypair.fromSecretKey(secretKey);
    
    // Test user wallet (from our test)
    const testUserPublicKey = new PublicKey('5o6GRHFr7WoEYPuiJ8UVRnV8QAbc7hZ9RK8WUaJ4EqhE');
    
    console.log('🏦 Game Owner:', gameOwnerKeypair.publicKey.toBase58());
    console.log('👤 Test User:', testUserPublicKey.toBase58());
    
    // Check game owner balance
    const gameOwnerBalance = await connection.getBalance(gameOwnerKeypair.publicKey);
    console.log('💰 Game Owner Balance:', gameOwnerBalance / LAMPORTS_PER_SOL, 'SOL');
    
    if (gameOwnerBalance < 0.1 * LAMPORTS_PER_SOL) {
      throw new Error('Game owner has insufficient balance');
    }
    
    // Transfer 0.5 SOL to test user
    const transferAmount = 0.5;
    const lamports = Math.floor(transferAmount * LAMPORTS_PER_SOL);
    
    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: gameOwnerKeypair.publicKey,
        toPubkey: testUserPublicKey,
        lamports
      })
    );
    
    console.log(`📤 Transferring ${transferAmount} SOL...`);
    
    const signature = await sendAndConfirmTransaction(
      connection,
      transaction,
      [gameOwnerKeypair],
      { commitment: 'confirmed' }
    );
    
    console.log('✅ Transfer successful!');
    console.log('🔗 Transaction:', signature);
    
    // Check balances after transfer
    const newGameOwnerBalance = await connection.getBalance(gameOwnerKeypair.publicKey);
    const testUserBalance = await connection.getBalance(testUserPublicKey);
    
    console.log('\n📊 Final Balances:');
    console.log('🏦 Game Owner:', newGameOwnerBalance / LAMPORTS_PER_SOL, 'SOL');
    console.log('👤 Test User:', testUserBalance / LAMPORTS_PER_SOL, 'SOL');
    
    console.log('\n🎉 Test user is now funded! You can run the full test again.');
    
  } catch (error) {
    console.error('❌ Error funding test user:', error.message);
  }
}

// Run if called directly
if (require.main === module) {
  fundTestUser();
}

module.exports = { fundTestUser };
