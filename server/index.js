const dotenv = require('dotenv');
const path = require('path');
dotenv.config({ path: path.join(__dirname, '.env') });

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const RoomManager = require('./game/RoomManager');
const SolanaGameService = require('./services/SolanaGameService');
const { validatePlayerInput, sanitizePlayerData } = require('./utils/validation');
const pythPriceService = require('./services/PythPriceService');

/**
 * Get real-time SOL price using Pyth Network with Crypto.com fallback
 */
async function getSolanaPrice() {
  return await pythPriceService.getSolanaPrice();
}

/**
 * Convert USD to SOL using real-time price from Pyth Network
 */
async function convertUsdToSol(usdAmount) {
  const price = await getSolanaPrice();
  return pythPriceService.convertUsdToSol(usdAmount, price);
}

const app = express();
const server = http.createServer(app);

// Configure CORS for Socket.IO
const io = socketIo(server, {
  cors: {
    origin: [
      "http://localhost:3000",
      "http://localhost:3002",
      "http://************:3000", // LAN access
      "http://************:3002", // LAN access
      "http://127.0.0.1:3000",
      "http://127.0.0.1:3002",
      "https://work-1-kwbmsnzhausxctuw.prod-runtime.all-hands.dev"
    ],
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// Middleware
app.use(cors());
app.use(express.json());

// API Routes
const gameResultsRouter = require('./routes/gameResults');
const solanaRouter = require('./routes/solana');
app.use('/api/game-results', gameResultsRouter);
app.use('/api/solana', solanaRouter);

// Initialize room manager and services
const roomManager = new RoomManager();
const solanaService = new SolanaGameService();

// Configuration for AI - can be controlled via environment variable
const AI_ENABLED = process.env.ENABLE_AI === 'true' || process.env.ENABLE_AI === '1';
console.log(`🤖 AI Configuration: ${AI_ENABLED ? 'ENABLED' : 'DISABLED'} (set ENABLE_AI=true to enable)`);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    rooms: roomManager.getRoomStats()
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`🔌 Player connected: ${socket.id}`);

  // Handle player joining a game
  socket.on('joinGame', async (data) => {
    try {
      const { gameMode, username, wager, color, userId } = data;

      // Validate input data
      if (!validatePlayerInput(data)) {
        socket.emit('error', { message: 'Invalid player data' });
        return;
      }

      // Sanitize player data
      const playerData = sanitizePlayerData({
        id: socket.id,
        username,
        wager: wager || 50, // Let sanitizePlayerData handle wager validation
        color: color || '#FFD700',
        gameMode: gameMode || 'classic',
        userId: userId // Add userId for Solana integration
      });

      console.log(`🎮 Player ${username} joining ${gameMode} game with $${wager} wager`);

      // Validate game entry with Solana balance check if userId provided
      if (userId) {
        try {
          const wagerAmountSol = await convertUsdToSol(playerData.wager);
          const validationResult = await solanaService.validateGameEntry(userId, wagerAmountSol);

          if (!validationResult.success || !validationResult.canJoin) {
            socket.emit('error', {
              message: `Cannot join game: ${validationResult.message || validationResult.error}`
            });
            return;
          }

          console.log(`✅ Game entry validated for ${username}: ${validationResult.message}`);
        } catch (validationError) {
          console.error('❌ Error validating game entry:', validationError);
          socket.emit('error', {
            message: 'Failed to validate game entry. Please check your balance and try again.'
          });
          return;
        }
      }

      // Find or create appropriate room
      console.log(`🤖 AI is ${AI_ENABLED ? 'ENABLED' : 'DISABLED'} for this room`);
      const room = await roomManager.findOrCreateRoom(gameMode, { enableAI: AI_ENABLED });

      if (!room) {
        socket.emit('error', { message: 'Unable to find or create game room' });
        return;
      }

      // Create room wallet if it doesn't exist and transfer wager if userId provided
      if (userId) {
        try {
          console.log(`🏦 Setting up room wallet and transferring wager for ${username}`);

          // First, ensure room has a wallet
          const roomWalletResult = await solanaService.createRoomWallet(room.id);
          if (!roomWalletResult.success) {
            socket.emit('error', {
              message: `Room wallet creation failed: ${roomWalletResult.error}. Please try again.`
            });
            return;
          }

          // Transfer wager to room
          const wagerAmountSol = await convertUsdToSol(playerData.wager);
          const transferResult = await solanaService.transferWagerToRoom(userId, room.id, wagerAmountSol);

          if (!transferResult.success) {
            socket.emit('error', {
              message: `Wager transfer failed: ${transferResult.error}. Please check your balance and try again.`
            });
            return;
          }

          console.log(`✅ Wager transferred to room escrow: ${transferResult.signature}`);
        } catch (transferError) {
          console.error('❌ Error transferring wager:', transferError);
          socket.emit('error', {
            message: 'Failed to transfer wager to escrow. Please check your connection and try again.'
          });
          return;
        }
      }

      // Add player to room
      const success = await room.addPlayer(socket, playerData);

      if (!success) {
        socket.emit('error', { message: 'Failed to join game room' });
        return;
      }

      // Store room reference on socket for cleanup
      socket.roomId = room.id;
      socket.gameMode = gameMode;
      socket.playerData = playerData;

      console.log(`✅ Player ${username} joined room ${room.id} (${room.getPlayerCount()}/100 players)`);

    } catch (error) {
      console.error('Error handling joinGame:', error);
      socket.emit('error', { message: 'Internal server error' });
    }
  });

  // Handle player input
  socket.on('playerInput', (inputData) => {
    try {
      if (!socket.roomId) return;

      const room = roomManager.getRoom(socket.roomId);
      if (room) {
        room.handlePlayerInput(socket.id, inputData);
      }
    } catch (error) {
      console.error('Error handling player input:', error);
    }
  });

  // Handle player shooting (warfare mode)
  socket.on('playerShoot', (shootData) => {
    try {
      if (!socket.roomId || socket.gameMode !== 'warfare') return;

      const room = roomManager.getRoom(socket.roomId);
      if (room) {
        room.handlePlayerShoot(socket.id, shootData);
      }
    } catch (error) {
      console.error('Error handling player shoot:', error);
    }
  });

  // Handle weapon switching (warfare mode)
  socket.on('switchWeapon', (weaponData) => {
    try {
      if (!socket.roomId || socket.gameMode !== 'warfare') return;

      const room = roomManager.getRoom(socket.roomId);
      if (room) {
        room.handleWeaponSwitch(socket.id, weaponData);
      }
    } catch (error) {
      console.error('Error handling weapon switch:', error);
    }
  });

  // Handle player cashout (both modes) - Integrated with Solana
  socket.on('playerCashOut', async (cashoutData) => {
    try {
      if (!socket.roomId) return;

      const room = roomManager.getRoom(socket.roomId);
      if (!room) {
        socket.emit('cashoutResult', { success: false, reason: 'Room not found' });
        return;
      }

      // Get game result from room
      const gameResult = room.handlePlayerCashout(socket.id, cashoutData);

      if (!gameResult.success) {
        socket.emit('cashoutResult', gameResult);
        return;
      }

      // Get player data for Solana integration
      const playerData = room.players.get(socket.id);
      if (!playerData || !playerData.userId) {
        // Fallback for non-authenticated players
        socket.emit('cashoutResult', gameResult);
        console.log(`💰 Player ${socket.id} cashed out $${gameResult.profit} profit (no Solana integration)`);
        return;
      }

      try {
        // Process Solana cashout
        console.log(`🏦 Processing Solana cashout for user ${playerData.userId}`);

        // Convert profit to SOL
        const cashoutAmountSol = await convertUsdToSol(gameResult.profit || 0);
        const solanaResult = await solanaService.processCashout(playerData.userId, room.id, cashoutAmountSol);

        if (solanaResult.success) {
          console.log(`✅ Solana cashout successful for ${playerData.username}: ${solanaResult.signature}`);

          // Send enhanced result back to player
          socket.emit('cashoutResult', {
            ...gameResult,
            solana_transaction: solanaResult.signature,
            winnings: cashoutAmountSol,
            final_balance: solanaResult.newBalance
          });
        } else {
          console.error(`❌ Solana cashout failed for ${playerData.username}:`, solanaResult.error);

          // Send game result but note Solana failure
          socket.emit('cashoutResult', {
            ...gameResult,
            solana_error: solanaResult.error
          });
        }
      } catch (solanaError) {
        console.error('❌ Error processing Solana cashout:', solanaError);

        // Send game result but note Solana failure
        socket.emit('cashoutResult', {
          ...gameResult,
          solana_error: 'Failed to process blockchain transaction'
        });
      }
    } catch (error) {
      console.error('Error handling player cashout:', error);
      socket.emit('cashoutResult', { success: false, reason: 'Internal server error' });
    }
  });

  // Handle player respawn request (ORIGINAL BEHAVIOR: Manual respawn)
  socket.on('playerRespawn', (respawnData) => {
    try {
      if (!socket.roomId) return;

      const room = roomManager.getRoom(socket.roomId);
      if (room) {
        room.game.handlePlayerRespawnRequest(socket.id);
        console.log(`🔄 Respawn request from ${socket.username || socket.id}`);
      }
    } catch (error) {
      console.error('Error handling player respawn:', error);
    }
  });

  // Handle chat messages
  socket.on('chatMessage', (messageData) => {
    try {
      if (!socket.roomId) return;

      const room = roomManager.getRoom(socket.roomId);
      if (room) {
        room.handleChatMessage(socket.id, messageData);
      }
    } catch (error) {
      console.error('Error handling chat message:', error);
    }
  });

  // Handle ping for latency measurement
  socket.on('ping', (timestamp) => {
    socket.emit('pong', timestamp);
  });

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    if (socket.roomId) {
      const room = roomManager.getRoom(socket.roomId);
      if (room) {
        room.removePlayer(socket.id);
      }
    }
  });

  // Handle connection errors
  socket.on('error', (error) => {
    console.error(`Socket error for ${socket.id}:`, error);
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Server shutting down gracefully...');
  roomManager.shutdown();
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 Server shutting down gracefully...');
  roomManager.shutdown();
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

const PORT = process.env.PORT || 3005;
const HOST = process.env.HOST || '0.0.0.0'; // Listen on all network interfaces

server.listen(PORT, HOST, () => {
  console.log(`🚀 SnakePit multiplayer server running on ${HOST}:${PORT}`);
  console.log(`🎮 Ready to accept connections...`);
  console.log(`🌐 LAN Access: http://************:${PORT}`);
  console.log(`🏠 Local Access: http://localhost:${PORT}`);
});

module.exports = { app, server, io };
