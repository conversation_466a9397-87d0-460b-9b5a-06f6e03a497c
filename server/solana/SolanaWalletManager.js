const { Keypair, Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL, sendAndConfirmTransaction } = require('@solana/web3.js');
const CryptoJS = require('crypto-js');
const bs58 = require('bs58');

class SolanaWalletManager {
  constructor() {
    // Initialize Solana connection
    this.connection = new Connection(
      process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com',
      'confirmed'
    );
    
    // Encryption key for private keys
    this.encryptionKey = process.env.WALLET_ENCRYPTION_KEY || 'default-key-change-in-production';
    
    // Game owner wallet (admin wallet)
    this.gameOwnerKeypair = null;
    this.initializeGameOwnerWallet();
    
    console.log('🔐 Solana Wallet Manager initialized');
    console.log('🌐 Network:', process.env.SOLANA_NETWORK || 'devnet');
    console.log('🔗 RPC URL:', process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com');
  }

  /**
   * Initialize the game owner wallet from environment variable
   */
  initializeGameOwnerWallet() {
    try {
      if (process.env.GAME_OWNER_PRIVATE_KEY && process.env.GAME_OWNER_PRIVATE_KEY !== 'your-game-owner-private-key-here') {
        const secretKey = bs58.default.decode(process.env.GAME_OWNER_PRIVATE_KEY);
        this.gameOwnerKeypair = Keypair.fromSecretKey(secretKey);
        console.log('🏦 Game Owner Wallet loaded:', this.gameOwnerKeypair.publicKey.toBase58());
      } else {
        // Generate a new game owner wallet for development
        this.gameOwnerKeypair = Keypair.generate();
        console.log('🏦 Generated new Game Owner Wallet:', this.gameOwnerKeypair.publicKey.toBase58());
        console.log('🔑 Private Key (save this!):', bs58.default.encode(this.gameOwnerKeypair.secretKey));
        console.log('⚠️  Add this private key to your .env file as GAME_OWNER_PRIVATE_KEY');
      }
    } catch (error) {
      console.error('❌ Error initializing game owner wallet:', error);
      // Fallback to generating a new wallet
      this.gameOwnerKeypair = Keypair.generate();
      console.log('🏦 Fallback: Generated new Game Owner Wallet:', this.gameOwnerKeypair.publicKey.toBase58());
    }
  }

  /**
   * Generate a new Solana wallet
   */
  generateWallet() {
    const keypair = Keypair.generate();
    const publicKey = keypair.publicKey.toBase58();
    const privateKey = bs58.default.encode(keypair.secretKey);
    const encryptedPrivateKey = this.encryptPrivateKey(privateKey);
    
    return {
      publicKey,
      privateKey,
      encryptedPrivateKey
    };
  }

  /**
   * Encrypt a private key for secure storage
   */
  encryptPrivateKey(privateKey) {
    return CryptoJS.AES.encrypt(privateKey, this.encryptionKey).toString();
  }

  /**
   * Decrypt a private key from storage
   */
  decryptPrivateKey(encryptedPrivateKey) {
    const bytes = CryptoJS.AES.decrypt(encryptedPrivateKey, this.encryptionKey);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  /**
   * Get wallet keypair from encrypted private key
   */
  getKeypairFromEncrypted(encryptedPrivateKey) {
    const privateKey = this.decryptPrivateKey(encryptedPrivateKey);
    const secretKey = bs58.default.decode(privateKey);
    return Keypair.fromSecretKey(secretKey);
  }

  /**
   * Get wallet balance in SOL
   */
  async getWalletBalance(publicKey) {
    try {
      const pubKey = new PublicKey(publicKey);
      const balance = await this.connection.getBalance(pubKey);
      return balance / LAMPORTS_PER_SOL;
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      return 0;
    }
  }

  /**
   * Transfer SOL between wallets
   */
  async transferSOL(fromEncryptedPrivateKey, toPublicKey, amountSOL) {
    try {
      const fromKeypair = this.getKeypairFromEncrypted(fromEncryptedPrivateKey);
      const toPubKey = new PublicKey(toPublicKey);
      const lamports = Math.floor(amountSOL * LAMPORTS_PER_SOL);
      
      // Check if sender has enough balance
      const senderBalance = await this.getWalletBalance(fromKeypair.publicKey.toBase58());
      if (senderBalance < amountSOL) {
        throw new Error(`Insufficient balance. Required: ${amountSOL} SOL, Available: ${senderBalance} SOL`);
      }
      
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: fromKeypair.publicKey,
          toPubkey: toPubKey,
          lamports
        })
      );
      
      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [fromKeypair],
        { commitment: 'confirmed' }
      );
      
      return {
        success: true,
        signature,
        amount: amountSOL
      };
    } catch (error) {
      console.error('Error transferring SOL:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Transfer SOL using keypair directly (for server operations)
   */
  async transferSOLWithKeypair(fromKeypair, toPublicKey, amountSOL) {
    try {
      const toPubKey = new PublicKey(toPublicKey);
      const lamports = Math.floor(amountSOL * LAMPORTS_PER_SOL);
      
      // Check if sender has enough balance
      const senderBalance = await this.getWalletBalance(fromKeypair.publicKey.toBase58());
      if (senderBalance < amountSOL) {
        throw new Error(`Insufficient balance. Required: ${amountSOL} SOL, Available: ${senderBalance} SOL`);
      }
      
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: fromKeypair.publicKey,
          toPubkey: toPubKey,
          lamports
        })
      );
      
      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [fromKeypair],
        { commitment: 'confirmed' }
      );
      
      return {
        success: true,
        signature,
        amount: amountSOL
      };
    } catch (error) {
      console.error('Error transferring SOL with keypair:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get the game owner's public key
   */
  getGameOwnerPublicKey() {
    return this.gameOwnerKeypair.publicKey.toBase58();
  }

  /**
   * Get the game owner's balance
   */
  async getGameOwnerBalance() {
    return await this.getWalletBalance(this.gameOwnerKeypair.publicKey.toBase58());
  }

  /**
   * Request airdrop for development (devnet only)
   */
  async requestAirdrop(publicKey, amountSOL = 1) {
    try {
      if (process.env.SOLANA_NETWORK !== 'devnet') {
        throw new Error('Airdrop only available on devnet');
      }
      
      const pubKey = new PublicKey(publicKey);
      const lamports = amountSOL * LAMPORTS_PER_SOL;
      
      const signature = await this.connection.requestAirdrop(pubKey, lamports);
      await this.connection.confirmTransaction(signature);
      
      return {
        success: true,
        signature,
        amount: amountSOL
      };
    } catch (error) {
      console.error('Error requesting airdrop:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate a Solana public key
   */
  isValidPublicKey(publicKey) {
    try {
      new PublicKey(publicKey);
      return true;
    } catch {
      return false;
    }
  }
}

module.exports = SolanaWalletManager;
