const { createClient } = require('@supabase/supabase-js');

class DatabaseService {
  constructor() {
    this.supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY
    );
    
    console.log('🗄️ Database Service initialized');
  }

  /**
   * Create a user wallet in the database
   */
  async createUserWallet(userId, publicKey, encryptedPrivateKey) {
    try {
      // Check if user already has a wallet
      const { data: existingWallet } = await this.supabase
        .from('solana_wallets')
        .select('*')
        .eq('user_id', userId)
        .eq('wallet_type', 'user')
        .eq('is_active', true)
        .single();

      if (existingWallet) {
        return {
          success: true,
          wallet: existingWallet,
          message: 'User already has an active wallet'
        };
      }

      // Create new wallet
      const { data, error } = await this.supabase
        .from('solana_wallets')
        .insert({
          user_id: userId,
          public_key: publicKey,
          encrypted_private_key: encryptedPrivateKey,
          wallet_type: 'user',
          is_active: true
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        wallet: data,
        message: 'Wallet created successfully'
      };
    } catch (error) {
      console.error('Error creating user wallet:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user wallet from database
   */
  async getUserWallet(userId) {
    try {
      const { data, error } = await this.supabase
        .from('solana_wallets')
        .select('*')
        .eq('user_id', userId)
        .eq('wallet_type', 'user')
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = not found
        throw error;
      }

      return {
        success: true,
        wallet: data
      };
    } catch (error) {
      console.error('Error getting user wallet:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a room wallet in the database
   */
  async createRoomWallet(roomId, publicKey, encryptedPrivateKey) {
    try {
      const { data, error } = await this.supabase
        .from('room_wallets')
        .insert({
          room_id: roomId,
          public_key: publicKey,
          encrypted_private_key: encryptedPrivateKey,
          current_balance: 0,
          total_wagered: 0,
          total_paid_out: 0,
          is_active: true
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        wallet: data,
        message: 'Room wallet created successfully'
      };
    } catch (error) {
      console.error('Error creating room wallet:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get room wallet from database
   */
  async getRoomWallet(roomId) {
    try {
      const { data, error } = await this.supabase
        .from('room_wallets')
        .select('*')
        .eq('room_id', roomId)
        .eq('is_active', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return {
        success: true,
        wallet: data
      };
    } catch (error) {
      console.error('Error getting room wallet:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update room wallet balance
   */
  async updateRoomWalletBalance(roomId, balanceChange, isWager = false) {
    try {
      const updateData = {
        current_balance: this.supabase.sql`current_balance + ${balanceChange}`
      };

      if (isWager) {
        updateData.total_wagered = this.supabase.sql`total_wagered + ${Math.abs(balanceChange)}`;
      } else if (balanceChange < 0) {
        updateData.total_paid_out = this.supabase.sql`total_paid_out + ${Math.abs(balanceChange)}`;
      }

      const { data, error } = await this.supabase
        .from('room_wallets')
        .update(updateData)
        .eq('room_id', roomId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        wallet: data
      };
    } catch (error) {
      console.error('Error updating room wallet balance:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a transaction record
   */
  async createTransaction(transactionData) {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .insert({
          user_id: transactionData.userId,
          type: transactionData.type,
          amount: transactionData.amount,
          from_wallet: transactionData.fromWallet,
          to_wallet: transactionData.toWallet,
          room_id: transactionData.roomId,
          status: transactionData.status || 'pending',
          description: transactionData.description,
          solana_signature: transactionData.signature
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        transaction: data
      };
    } catch (error) {
      console.error('Error creating transaction:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update transaction status
   */
  async updateTransactionStatus(transactionId, status, signature = null) {
    try {
      const updateData = {
        status,
        confirmed_at: status === 'confirmed' ? new Date().toISOString() : null
      };

      if (signature) {
        updateData.solana_signature = signature;
      }

      const { data, error } = await this.supabase
        .from('transactions')
        .update(updateData)
        .eq('id', transactionId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        transaction: data
      };
    } catch (error) {
      console.error('Error updating transaction status:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update user profile balance
   */
  async updateUserBalance(userId, newBalance) {
    try {
      const { data, error } = await this.supabase
        .from('user_profiles')
        .update({
          solana_balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return {
        success: true,
        profile: data
      };
    } catch (error) {
      console.error('Error updating user balance:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get user profile
   */
  async getUserProfile(userId) {
    try {
      const { data, error } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      return {
        success: true,
        profile: data
      };
    } catch (error) {
      console.error('Error getting user profile:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = DatabaseService;
