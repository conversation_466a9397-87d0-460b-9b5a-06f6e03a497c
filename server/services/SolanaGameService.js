const SolanaWalletManager = require('../solana/SolanaWalletManager');
const DatabaseService = require('./DatabaseService');

class SolanaGameService {
  constructor() {
    this.walletManager = new SolanaWalletManager();
    this.database = new DatabaseService();
    
    console.log('🎮 Solana Game Service initialized');
  }

  /**
   * Create wallet for new user
   */
  async createUserWallet(userId) {
    try {
      console.log(`🔐 Creating wallet for user: ${userId}`);

      // Check if user already has a wallet
      const existingWalletResult = await this.database.getUserWallet(userId);
      if (existingWalletResult.success && existingWalletResult.wallet) {
        console.log(`✅ User ${userId} already has wallet: ${existingWalletResult.wallet.public_key}`);
        return {
          success: true,
          publicKey: existingWalletResult.wallet.public_key,
          message: 'User already has an active wallet'
        };
      }

      // Generate new wallet
      const walletInfo = this.walletManager.generateWallet();

      // Store in database
      const dbResult = await this.database.createUserWallet(
        userId,
        walletInfo.publicKey,
        walletInfo.encryptedPrivateKey
      );

      if (!dbResult.success) {
        throw new Error(dbResult.error);
      }

      console.log(`✅ Created wallet for user ${userId}: ${walletInfo.publicKey}`);

      // Request airdrop for development (devnet only)
      if (process.env.SOLANA_NETWORK === 'devnet') {
        console.log(`💰 Requesting airdrop for new wallet: ${walletInfo.publicKey}`);
        const airdropResult = await this.walletManager.requestAirdrop(walletInfo.publicKey, 10);
        if (airdropResult.success) {
          console.log(`✅ Airdrop successful: ${airdropResult.signature}`);
        } else {
          console.log(`⚠️ Airdrop failed: ${airdropResult.error}`);
        }
      }

      return {
        success: true,
        publicKey: walletInfo.publicKey,
        message: 'Wallet created successfully'
      };
    } catch (error) {
      console.error('❌ Error creating user wallet:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create wallet for game room
   */
  async createRoomWallet(roomId) {
    try {
      console.log(`🏠 Creating room wallet for: ${roomId}`);

      // Check if room already has a wallet
      const existingWalletResult = await this.database.getRoomWallet(roomId);
      if (existingWalletResult.success && existingWalletResult.wallet) {
        console.log(`✅ Room ${roomId} already has wallet: ${existingWalletResult.wallet.public_key}`);
        return {
          success: true,
          publicKey: existingWalletResult.wallet.public_key,
          message: 'Room already has an active wallet'
        };
      }

      // Generate new wallet
      const walletInfo = this.walletManager.generateWallet();

      // Store in database
      const dbResult = await this.database.createRoomWallet(
        roomId,
        walletInfo.publicKey,
        walletInfo.encryptedPrivateKey
      );

      if (!dbResult.success) {
        throw new Error(dbResult.error);
      }

      console.log(`✅ Created room wallet for ${roomId}: ${walletInfo.publicKey}`);

      return {
        success: true,
        publicKey: walletInfo.publicKey,
        message: 'Room wallet created successfully'
      };
    } catch (error) {
      console.error('❌ Error creating room wallet:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate if user can join game (has sufficient balance)
   */
  async validateGameEntry(userId, wagerAmount) {
    try {
      console.log(`🔍 Validating game entry for user ${userId}, wager: ${wagerAmount} SOL`);

      // Get user wallet
      const walletResult = await this.database.getUserWallet(userId);
      if (!walletResult.success || !walletResult.wallet) {
        return {
          success: false,
          canJoin: false,
          message: 'User wallet not found'
        };
      }

      // Check wallet balance
      const balance = await this.walletManager.getWalletBalance(walletResult.wallet.public_key);
      
      if (balance < wagerAmount) {
        return {
          success: true,
          canJoin: false,
          message: `Insufficient balance. Required: ${wagerAmount} SOL, Available: ${balance} SOL`
        };
      }

      return {
        success: true,
        canJoin: true,
        balance: balance,
        message: 'User can join game'
      };
    } catch (error) {
      console.error('❌ Error validating game entry:', error);
      return {
        success: false,
        canJoin: false,
        error: error.message
      };
    }
  }

  /**
   * Transfer wager from user to room
   */
  async transferWagerToRoom(userId, roomId, wagerAmount) {
    try {
      console.log(`💸 Transferring wager: ${wagerAmount} SOL from user ${userId} to room ${roomId}`);

      // Get user wallet
      const userWalletResult = await this.database.getUserWallet(userId);
      if (!userWalletResult.success || !userWalletResult.wallet) {
        throw new Error('User wallet not found');
      }

      // Get room wallet
      const roomWalletResult = await this.database.getRoomWallet(roomId);
      if (!roomWalletResult.success || !roomWalletResult.wallet) {
        throw new Error('Room wallet not found');
      }

      // Create transaction record
      const transactionResult = await this.database.createTransaction({
        userId: userId,
        type: 'wager',
        amount: wagerAmount,
        fromWallet: userWalletResult.wallet.public_key,
        toWallet: roomWalletResult.wallet.public_key,
        roomId: roomId,
        status: 'pending',
        description: `Wager transfer to room ${roomId}`
      });

      if (!transactionResult.success) {
        throw new Error('Failed to create transaction record');
      }

      // Execute the transfer
      const transferResult = await this.walletManager.transferSOL(
        userWalletResult.wallet.encrypted_private_key,
        roomWalletResult.wallet.public_key,
        wagerAmount
      );

      if (!transferResult.success) {
        // Update transaction as failed
        await this.database.updateTransactionStatus(
          transactionResult.transaction.id,
          'failed'
        );
        throw new Error(transferResult.error);
      }

      // Update transaction as confirmed
      await this.database.updateTransactionStatus(
        transactionResult.transaction.id,
        'confirmed',
        transferResult.signature
      );

      // Update room wallet balance
      await this.database.updateRoomWalletBalance(roomId, wagerAmount, true);

      // Update user balance in profile
      const newUserBalance = await this.walletManager.getWalletBalance(userWalletResult.wallet.public_key);
      await this.database.updateUserBalance(userId, newUserBalance);

      console.log(`✅ Wager transfer successful: ${transferResult.signature}`);

      return {
        success: true,
        signature: transferResult.signature,
        transactionId: transactionResult.transaction.id,
        message: 'Wager transferred successfully'
      };
    } catch (error) {
      console.error('❌ Error transferring wager to room:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process cashout from room to user
   */
  async processCashout(userId, roomId, cashoutAmount) {
    try {
      console.log(`💰 Processing cashout: ${cashoutAmount} SOL from room ${roomId} to user ${userId}`);

      // Get user wallet
      const userWalletResult = await this.database.getUserWallet(userId);
      if (!userWalletResult.success || !userWalletResult.wallet) {
        throw new Error('User wallet not found');
      }

      // Get room wallet
      const roomWalletResult = await this.database.getRoomWallet(roomId);
      if (!roomWalletResult.success || !roomWalletResult.wallet) {
        throw new Error('Room wallet not found');
      }

      // Check if room has sufficient balance
      const roomBalance = await this.walletManager.getWalletBalance(roomWalletResult.wallet.public_key);
      if (roomBalance < cashoutAmount) {
        throw new Error(`Insufficient room balance. Required: ${cashoutAmount} SOL, Available: ${roomBalance} SOL`);
      }

      // Create transaction record
      const transactionResult = await this.database.createTransaction({
        userId: userId,
        type: 'cashout',
        amount: cashoutAmount,
        fromWallet: roomWalletResult.wallet.public_key,
        toWallet: userWalletResult.wallet.public_key,
        roomId: roomId,
        status: 'pending',
        description: `Cashout from room ${roomId}`
      });

      if (!transactionResult.success) {
        throw new Error('Failed to create transaction record');
      }

      // Execute the transfer
      const transferResult = await this.walletManager.transferSOL(
        roomWalletResult.wallet.encrypted_private_key,
        userWalletResult.wallet.public_key,
        cashoutAmount
      );

      if (!transferResult.success) {
        // Update transaction as failed
        await this.database.updateTransactionStatus(
          transactionResult.transaction.id,
          'failed'
        );
        throw new Error(transferResult.error);
      }

      // Update transaction as confirmed
      await this.database.updateTransactionStatus(
        transactionResult.transaction.id,
        'confirmed',
        transferResult.signature
      );

      // Update room wallet balance (negative for cashout)
      await this.database.updateRoomWalletBalance(roomId, -cashoutAmount, false);

      // Update user balance in profile
      const newUserBalance = await this.walletManager.getWalletBalance(userWalletResult.wallet.public_key);
      await this.database.updateUserBalance(userId, newUserBalance);

      console.log(`✅ Cashout successful: ${transferResult.signature}`);

      return {
        success: true,
        signature: transferResult.signature,
        transactionId: transactionResult.transaction.id,
        newBalance: newUserBalance,
        message: 'Cashout processed successfully'
      };
    } catch (error) {
      console.error('❌ Error processing cashout:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process shop purchase (transfer from user to game owner)
   */
  async processShopPurchase(userId, itemPrice, itemName) {
    try {
      console.log(`🛒 Processing shop purchase: ${itemName} for ${itemPrice} SOL by user ${userId}`);

      // Get user wallet
      const userWalletResult = await this.database.getUserWallet(userId);
      if (!userWalletResult.success || !userWalletResult.wallet) {
        throw new Error('User wallet not found');
      }

      // Check if user has sufficient balance
      const userBalance = await this.walletManager.getWalletBalance(userWalletResult.wallet.public_key);
      if (userBalance < itemPrice) {
        throw new Error(`Insufficient balance. Required: ${itemPrice} SOL, Available: ${userBalance} SOL`);
      }

      // Create transaction record
      const transactionResult = await this.database.createTransaction({
        userId: userId,
        type: 'shop_purchase',
        amount: itemPrice,
        fromWallet: userWalletResult.wallet.public_key,
        toWallet: this.walletManager.getGameOwnerPublicKey(),
        status: 'pending',
        description: `Shop purchase: ${itemName}`
      });

      if (!transactionResult.success) {
        throw new Error('Failed to create transaction record');
      }

      // Execute the transfer
      const transferResult = await this.walletManager.transferSOL(
        userWalletResult.wallet.encrypted_private_key,
        this.walletManager.getGameOwnerPublicKey(),
        itemPrice
      );

      if (!transferResult.success) {
        // Update transaction as failed
        await this.database.updateTransactionStatus(
          transactionResult.transaction.id,
          'failed'
        );
        throw new Error(transferResult.error);
      }

      // Update transaction as confirmed
      await this.database.updateTransactionStatus(
        transactionResult.transaction.id,
        'confirmed',
        transferResult.signature
      );

      // Update user balance in profile
      const newUserBalance = await this.walletManager.getWalletBalance(userWalletResult.wallet.public_key);
      await this.database.updateUserBalance(userId, newUserBalance);

      console.log(`✅ Shop purchase successful: ${transferResult.signature}`);

      return {
        success: true,
        signature: transferResult.signature,
        transactionId: transactionResult.transaction.id,
        newBalance: newUserBalance,
        message: 'Shop purchase completed successfully'
      };
    } catch (error) {
      console.error('❌ Error processing shop purchase:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Transfer SOL from game wallet to external wallet
   */
  async transferToExternalWallet(userId, externalWalletAddress, amount) {
    try {
      console.log(`📤 Transferring ${amount} SOL from user ${userId} to external wallet ${externalWalletAddress}`);

      // Validate external wallet address
      if (!this.walletManager.isValidPublicKey(externalWalletAddress)) {
        throw new Error('Invalid external wallet address');
      }

      // Get user wallet
      const userWalletResult = await this.database.getUserWallet(userId);
      if (!userWalletResult.success || !userWalletResult.wallet) {
        throw new Error('User wallet not found');
      }

      // Check if user has sufficient balance
      const userBalance = await this.walletManager.getWalletBalance(userWalletResult.wallet.public_key);
      if (userBalance < amount) {
        throw new Error(`Insufficient balance. Required: ${amount} SOL, Available: ${userBalance} SOL`);
      }

      // Create transaction record
      const transactionResult = await this.database.createTransaction({
        userId: userId,
        type: 'external_transfer',
        amount: amount,
        fromWallet: userWalletResult.wallet.public_key,
        toWallet: externalWalletAddress,
        status: 'pending',
        description: `Transfer to external wallet: ${externalWalletAddress}`
      });

      if (!transactionResult.success) {
        throw new Error('Failed to create transaction record');
      }

      // Execute the transfer
      const transferResult = await this.walletManager.transferSOL(
        userWalletResult.wallet.encrypted_private_key,
        externalWalletAddress,
        amount
      );

      if (!transferResult.success) {
        // Update transaction as failed
        await this.database.updateTransactionStatus(
          transactionResult.transaction.id,
          'failed'
        );
        throw new Error(transferResult.error);
      }

      // Update transaction as confirmed
      await this.database.updateTransactionStatus(
        transactionResult.transaction.id,
        'confirmed',
        transferResult.signature
      );

      // Update user balance in profile
      const newUserBalance = await this.walletManager.getWalletBalance(userWalletResult.wallet.public_key);
      await this.database.updateUserBalance(userId, newUserBalance);

      console.log(`✅ External transfer successful: ${transferResult.signature}`);

      return {
        success: true,
        signature: transferResult.signature,
        transactionId: transactionResult.transaction.id,
        newBalance: newUserBalance,
        message: 'Transfer to external wallet completed successfully'
      };
    } catch (error) {
      console.error('❌ Error transferring to external wallet:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Transfer remaining room funds to game owner when room is destroyed
   */
  async transferRoomToGameOwner(roomId) {
    try {
      console.log(`🏦 Transferring remaining room funds to game owner for room: ${roomId}`);

      // Get room wallet
      const roomWalletResult = await this.database.getRoomWallet(roomId);
      if (!roomWalletResult.success || !roomWalletResult.wallet) {
        throw new Error('Room wallet not found');
      }

      // Check room balance
      const roomBalance = await this.walletManager.getWalletBalance(roomWalletResult.wallet.public_key);

      // Only transfer if there's a meaningful balance (more than 0.001 SOL)
      if (roomBalance <= 0.001) {
        console.log(`💰 Room ${roomId} has minimal balance (${roomBalance} SOL), skipping transfer`);
        return {
          success: true,
          message: 'No significant balance to transfer'
        };
      }

      // Create transaction record
      const transactionResult = await this.database.createTransaction({
        userId: null, // System transaction
        type: 'room_to_admin',
        amount: roomBalance,
        fromWallet: roomWalletResult.wallet.public_key,
        toWallet: this.walletManager.getGameOwnerPublicKey(),
        roomId: roomId,
        status: 'pending',
        description: `Room destruction transfer: ${roomId}`
      });

      if (!transactionResult.success) {
        throw new Error('Failed to create transaction record');
      }

      // Execute the transfer
      const transferResult = await this.walletManager.transferSOL(
        roomWalletResult.wallet.encrypted_private_key,
        this.walletManager.getGameOwnerPublicKey(),
        roomBalance
      );

      if (!transferResult.success) {
        // Update transaction as failed
        await this.database.updateTransactionStatus(
          transactionResult.transaction.id,
          'failed'
        );
        throw new Error(transferResult.error);
      }

      // Update transaction as confirmed
      await this.database.updateTransactionStatus(
        transactionResult.transaction.id,
        'confirmed',
        transferResult.signature
      );

      console.log(`✅ Room to game owner transfer successful: ${transferResult.signature}`);

      return {
        success: true,
        signature: transferResult.signature,
        amount: roomBalance,
        message: 'Room funds transferred to game owner successfully'
      };
    } catch (error) {
      console.error('❌ Error transferring room to game owner:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get wallet balance for a user
   */
  async getUserBalance(userId) {
    try {
      const userWalletResult = await this.database.getUserWallet(userId);
      if (!userWalletResult.success || !userWalletResult.wallet) {
        return {
          success: false,
          error: 'User wallet not found'
        };
      }

      const balance = await this.walletManager.getWalletBalance(userWalletResult.wallet.public_key);

      return {
        success: true,
        balance: balance,
        publicKey: userWalletResult.wallet.public_key
      };
    } catch (error) {
      console.error('❌ Error getting user balance:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = SolanaGameService;
