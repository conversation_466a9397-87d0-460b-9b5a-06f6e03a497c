const express = require('express');
const SolanaGameService = require('../services/SolanaGameService');

const router = express.Router();
const solanaService = new SolanaGameService();

/**
 * Create wallet for new user
 * POST /api/solana/create-user-wallet
 */
router.post('/create-user-wallet', async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    const result = await solanaService.createUserWallet(userId);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in create-user-wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Create wallet for game room
 * POST /api/solana/create-room-wallet
 */
router.post('/create-room-wallet', async (req, res) => {
  try {
    const { roomId } = req.body;

    if (!roomId) {
      return res.status(400).json({
        success: false,
        error: 'Room ID is required'
      });
    }

    const result = await solanaService.createRoomWallet(roomId);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in create-room-wallet:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Validate if user can join game
 * POST /api/solana/validate-game-entry
 */
router.post('/validate-game-entry', async (req, res) => {
  try {
    const { userId, wagerAmount, gameMode } = req.body;

    if (!userId || !wagerAmount) {
      return res.status(400).json({
        success: false,
        error: 'User ID and wager amount are required'
      });
    }

    // Validate game mode if provided
    if (gameMode && !['classic', 'warfare', 'speed'].includes(gameMode)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid game mode'
      });
    }

    const result = await solanaService.validateGameEntry(userId, wagerAmount);
    res.json(result);
  } catch (error) {
    console.error('Error in validate-game-entry:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Transfer wager from user to room
 * POST /api/solana/transfer-wager
 */
router.post('/transfer-wager', async (req, res) => {
  try {
    const { userId, roomId, wagerAmount } = req.body;

    if (!userId || !roomId || !wagerAmount) {
      return res.status(400).json({
        success: false,
        error: 'User ID, room ID, and wager amount are required'
      });
    }

    const result = await solanaService.transferWagerToRoom(userId, roomId, wagerAmount);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in transfer-wager:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Process cashout from room to user
 * POST /api/solana/cashout
 */
router.post('/cashout', async (req, res) => {
  try {
    const { userId, roomId, cashoutAmount } = req.body;

    if (!userId || !roomId || !cashoutAmount) {
      return res.status(400).json({
        success: false,
        error: 'User ID, room ID, and cashout amount are required'
      });
    }

    const result = await solanaService.processCashout(userId, roomId, cashoutAmount);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in cashout:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Process shop purchase
 * POST /api/solana/shop-purchase
 */
router.post('/shop-purchase', async (req, res) => {
  try {
    const { userId, itemPrice, itemName } = req.body;

    if (!userId || !itemPrice || !itemName) {
      return res.status(400).json({
        success: false,
        error: 'User ID, item price, and item name are required'
      });
    }

    const result = await solanaService.processShopPurchase(userId, itemPrice, itemName);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in shop-purchase:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Transfer to external wallet
 * POST /api/solana/transfer-external
 */
router.post('/transfer-external', async (req, res) => {
  try {
    const { userId, externalWalletAddress, amount } = req.body;

    if (!userId || !externalWalletAddress || !amount) {
      return res.status(400).json({
        success: false,
        error: 'User ID, external wallet address, and amount are required'
      });
    }

    const result = await solanaService.transferToExternalWallet(userId, externalWalletAddress, amount);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in transfer-external:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Get user wallet balance
 * GET /api/solana/balance/:userId
 */
router.get('/balance/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }

    const result = await solanaService.getUserBalance(userId);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in get balance:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Transfer room funds to game owner (called when room is destroyed)
 * POST /api/solana/room-to-owner
 */
router.post('/room-to-owner', async (req, res) => {
  try {
    const { roomId } = req.body;

    if (!roomId) {
      return res.status(400).json({
        success: false,
        error: 'Room ID is required'
      });
    }

    const result = await solanaService.transferRoomToGameOwner(roomId);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(400).json(result);
    }
  } catch (error) {
    console.error('Error in room-to-owner:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Health check endpoint
 * GET /api/solana/health
 */
router.get('/health', async (req, res) => {
  try {
    const gameOwnerBalance = await solanaService.walletManager.getGameOwnerBalance();
    
    res.json({
      success: true,
      status: 'healthy',
      network: process.env.SOLANA_NETWORK || 'devnet',
      gameOwnerWallet: solanaService.walletManager.getGameOwnerPublicKey(),
      gameOwnerBalance: gameOwnerBalance,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in health check:', error);
    res.status(500).json({
      success: false,
      error: 'Health check failed'
    });
  }
});

module.exports = router;
