import { supabase } from '../lib/supabase';
import { generateWallet } from './SolanaWallet';

export interface WalletGenerationResult {
  success: boolean;
  publicKey?: string;
  message: string;
  error?: string;
}

/**
 * Generate a Solana wallet for an existing user account
 * This function checks if the user already has a wallet and creates one if they don't
 */
export async function generateWalletForExistingUser(userId: string): Promise<WalletGenerationResult> {
  try {
    // Check if user already has an active wallet
    const { data: existingWallet, error: checkError } = await supabase
      .from('solana_wallets')
      .select('public_key, is_active')
      .eq('user_id', userId)
      .eq('wallet_type', 'user')
      .eq('is_active', true)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      // PGRST116 means no rows found, which is expected for users without wallets
      throw new Error(`Error checking existing wallet: ${checkError.message}`);
    }

    if (existingWallet) {
      return {
        success: true,
        publicKey: existingWallet.public_key,
        message: 'User already has an active wallet'
      };
    }

    // Generate new wallet
    const walletInfo = generateWallet();

    // Store wallet in database
    const { error: insertError } = await supabase
      .from('solana_wallets')
      .insert({
        user_id: userId,
        public_key: walletInfo.publicKey,
        encrypted_private_key: walletInfo.encryptedPrivateKey,
        wallet_type: 'user',
        is_active: true
      });

    if (insertError) {
      throw new Error(`Error storing wallet: ${insertError.message}`);
    }

    console.log(`✅ Generated Solana wallet for existing user ${userId}: ${walletInfo.publicKey}`);

    return {
      success: true,
      publicKey: walletInfo.publicKey,
      message: 'Wallet generated successfully'
    };

  } catch (error) {
    console.error('❌ Error generating wallet for existing user:', error);
    return {
      success: false,
      message: 'Failed to generate wallet',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Generate wallets for multiple existing users in batch
 * This is useful for migrating existing accounts to have Solana wallets
 */
export async function generateWalletsForMultipleUsers(userIds: string[]): Promise<{
  successful: string[];
  failed: { userId: string; error: string }[];
  summary: string;
}> {
  const successful: string[] = [];
  const failed: { userId: string; error: string }[] = [];

  console.log(`🔄 Starting batch wallet generation for ${userIds.length} users`);

  for (const userId of userIds) {
    try {
      const result = await generateWalletForExistingUser(userId);
      
      if (result.success) {
        successful.push(userId);
        console.log(`✅ Wallet generated for user ${userId}`);
      } else {
        failed.push({ userId, error: result.error || result.message });
        console.log(`❌ Failed to generate wallet for user ${userId}: ${result.error || result.message}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      failed.push({ userId, error: errorMessage });
      console.log(`❌ Exception generating wallet for user ${userId}: ${errorMessage}`);
    }

    // Add a small delay to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  const summary = `Batch wallet generation completed: ${successful.length} successful, ${failed.length} failed`;
  console.log(`📊 ${summary}`);

  return {
    successful,
    failed,
    summary
  };
}

/**
 * Get all users who don't have Solana wallets yet
 * This helps identify which existing accounts need wallet generation
 */
export async function getUsersWithoutWallets(): Promise<{
  success: boolean;
  userIds: string[];
  count: number;
  error?: string;
}> {
  try {
    // Get all user profiles
    const { data: allUsers, error: usersError } = await supabase
      .from('user_profiles')
      .select('id, username');

    if (usersError) {
      throw new Error(`Error fetching users: ${usersError.message}`);
    }

    if (!allUsers || allUsers.length === 0) {
      return {
        success: true,
        userIds: [],
        count: 0
      };
    }

    // Get all users who have active wallets
    const { data: usersWithWallets, error: walletsError } = await supabase
      .from('solana_wallets')
      .select('user_id')
      .eq('wallet_type', 'user')
      .eq('is_active', true);

    if (walletsError) {
      throw new Error(`Error fetching wallets: ${walletsError.message}`);
    }

    const userIdsWithWallets = new Set(usersWithWallets?.map(w => w.user_id) || []);
    const usersWithoutWallets = allUsers.filter(user => !userIdsWithWallets.has(user.id));

    console.log(`📊 Found ${usersWithoutWallets.length} users without wallets out of ${allUsers.length} total users`);

    return {
      success: true,
      userIds: usersWithoutWallets.map(user => user.id),
      count: usersWithoutWallets.length
    };

  } catch (error) {
    console.error('❌ Error finding users without wallets:', error);
    return {
      success: false,
      userIds: [],
      count: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Convenience function to generate wallets for all existing users who don't have them
 */
export async function generateWalletsForAllExistingUsers(): Promise<{
  successful: string[];
  failed: { userId: string; error: string }[];
  summary: string;
}> {
  console.log('🔄 Starting wallet generation for all existing users without wallets');
  
  const usersResult = await getUsersWithoutWallets();
  
  if (!usersResult.success) {
    return {
      successful: [],
      failed: [{ userId: 'system', error: usersResult.error || 'Failed to fetch users' }],
      summary: 'Failed to fetch users without wallets'
    };
  }

  if (usersResult.count === 0) {
    return {
      successful: [],
      failed: [],
      summary: 'All existing users already have wallets'
    };
  }

  console.log(`📊 Found ${usersResult.count} users without wallets, generating wallets...`);
  
  return await generateWalletsForMultipleUsers(usersResult.userIds);
}