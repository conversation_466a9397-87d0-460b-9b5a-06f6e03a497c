import * as twgl from 'twgl.js';

// This renderer uses twgl.js, a tiny WebGL helper library.
// Make sure to include it in your project!
// https://twgljs.org/

// Vertex shader for most instanced objects (quads)
const instancedVertexShader = `
  attribute vec4 position;
  attribute vec2 texcoord;

  attribute vec2 instanceWorldPosition;
  attribute vec2 instanceSize;
  attribute vec4 instanceColor;
  attribute vec4 instanceTexcoordRect;

  uniform mat4 u_matrix;

  varying vec2 v_texcoord;
  varying vec4 v_color;
  varying vec4 v_texcoordRect;

  void main() {
    v_texcoord = texcoord;
    v_color = instanceColor;
    v_texcoordRect = instanceTexcoordRect;
    
    vec2 pos = position.xy * instanceSize + instanceWorldPosition;
    gl_Position = u_matrix * vec4(pos, 0, 1);
  }
`;

// Fragment shader for simple, colored circles (food)
const circleFragmentShader = `
  precision mediump float;
  varying vec4 v_color;
  varying vec2 v_texcoord;

  void main() {
    float dist = distance(v_texcoord, vec2(0.5, 0.5));
    if (dist > 0.5) {
      discard;
    }
    gl_FragColor = v_color;
  }
`;

// Fragment shader for textured items (icons)
const texturedFragmentShader = `
  precision mediump float;
  uniform sampler2D u_texture;

  varying vec2 v_texcoord;
  varying vec4 v_color;
  varying vec4 v_texcoordRect;

  void main() {
    // Map unit quad texcoord to the correct region in the texture atlas
    vec2 uv = v_texcoordRect.xy + v_texcoord * v_texcoordRect.zw;
    gl_FragColor = texture2D(u_texture, uv) * v_color;
  }
`;

// Vertex shader for the snake's body mesh
const snakeBodyVertexShader = `
  attribute vec2 a_position;
  attribute float a_progress;
  
  uniform mat4 u_matrix;
  varying float v_progress;

  void main() {
    v_progress = a_progress;
    gl_Position = u_matrix * vec4(a_position, 0.0, 1.0);
  }
`;

// Fragment shader to procedurally draw the complex snake body
const snakeBodyFragmentShader = `
  precision mediump float;

  uniform vec4 u_snakeColor;
  uniform float u_time;
  uniform float u_headSize;
  
  varying float v_progress; // 0 at head, 1 at tail

  vec3 hsv2rgb(vec3 c) {
    vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
    vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
    return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
  }

  void main() {
    // This is a simplified procedural snake shader.
    // It combines layers to create the final look.

    // Aura effect
    float aura = smoothstep(1.0, 0.2, gl_PointCoord.y) * 0.3;
    aura *= (0.8 + sin(u_time * 5.0 + v_progress * 20.0) * 0.2);

    // Main body color - this would need more complex logic for the black/gold style
    vec3 bodyColor = u_snakeColor.rgb;
    
    // Fake "scales" pattern
    float pattern = sin(v_progress * 100.0 + u_time * 2.0) * 0.5 + 0.5;
    bodyColor = mix(bodyColor, bodyColor * 0.8, pattern);

    // Add a center line
    float centerLine = 1.0 - smoothstep(0.0, 0.05, abs(gl_PointCoord.x - 0.5));
    bodyColor = mix(bodyColor, u_snakeColor.rgb * 1.5, centerLine);

    gl_FragColor = vec4(bodyColor, u_snakeColor.a);
  }
`;


class WebGLRenderer {
  constructor(canvas) {
    this.canvas = canvas;
    this.gl = canvas.getContext('webgl', { antialias: true, premultipliedAlpha: false });
    if (!this.gl) {
      throw new Error("WebGL not supported");
    }

    twgl.addExtensionsToContext(this.gl);
    this.init();
  }

  init() {
    const gl = this.gl;
    
    // --- Shaders and Programs ---
    this.programs = {
      circle: twgl.createProgramInfo(gl, [instancedVertexShader, circleFragmentShader]),
      texture: twgl.createProgramInfo(gl, [instancedVertexShader, texturedFragmentShader]),
      snake: twgl.createProgramInfo(gl, [snakeBodyVertexShader, snakeBodyFragmentShader])
    };

    // --- Buffers and VAOs ---
    // A standard quad for instancing everything
    this.quadBufferInfo = twgl.createBufferInfoFromArrays(gl, {
      position: { numComponents: 2, data: [-0.5, -0.5, 0.5, -0.5, -0.5, 0.5, -0.5, 0.5, 0.5, -0.5, 0.5, 0.5] },
      texcoord: { numComponents: 2, data: [0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1] },
    });

    this.iconAtlas = null;
    this.iconMap = new Map();
  }

  // Create a texture atlas from all the loaded icons for batching
  createIconAtlas(icons) {
    const gl = this.gl;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const atlasSize = 2048; // Power of 2 is good
    const iconSize = 128; // Standard size for icons in the atlas
    canvas.width = atlasSize;
    canvas.height = atlasSize;

    let x = 0;
    let y = 0;

    for (const [key, img] of icons.entries()) {
      if (img.complete && img.naturalWidth > 0) {
        if (x + iconSize > atlasSize) {
          x = 0;
          y += iconSize;
        }
        if (y + iconSize > atlasSize) {
          console.error("Icon atlas is full!");
          break;
        }
        ctx.drawImage(img, x, y, iconSize, iconSize);
        // Store UV coordinates: [x, y, width, height]
        this.iconMap.set(key, [x / atlasSize, y / atlasSize, iconSize / atlasSize, iconSize / atlasSize]);
        x += iconSize;
      }
    }
    
    this.iconAtlas = twgl.createTexture(gl, { 
      src: canvas, 
      mag: gl.LINEAR, 
      min: gl.LINEAR_MIPMAP_LINEAR,
      wrap: gl.CLAMP_TO_EDGE,
    });
    gl.generateMipmap(gl.TEXTURE_2D);
    console.log('✅ WebGL Icon Atlas created.');
  }

  beginFrame(camera) {
    const gl = this.gl;
    twgl.resizeCanvasToDisplaySize(gl.canvas);
    gl.viewport(0, 0, gl.canvas.width, gl.canvas.height);

    gl.clearColor(0.04, 0.04, 0.04, 1); // #0a0a0a
    gl.clear(gl.COLOR_BUFFER_BIT);

    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

    // Create the view-projection matrix
    this.viewMatrix = twgl.m4.identity();
    twgl.m4.ortho(0, gl.canvas.width, gl.canvas.height, 0, -1, 1, this.viewMatrix);
    twgl.m4.scale(this.viewMatrix, [camera.zoom, camera.zoom, 1], this.viewMatrix);
    twgl.m4.translate(this.viewMatrix, [-camera.x, -camera.y, 0], this.viewMatrix);
  }

  // --- DRAWING METHODS ---

  drawGrid(worldWidth, worldHeight) {
    // This is complex to do efficiently in WebGL. A common approach is a full-screen quad
    // with a fragment shader that calculates and draws the grid lines based on world coordinates.
    // For simplicity, we will skip the implementation detail here, but that's the high-performance path.
  }
  
  drawWorldBounds(worldWidth, worldHeight) {
    // This would be another shader drawing a large rectangle.
  }
  
  drawSimpleCircles(items) {
    if (!items || items.length === 0) return;
    const gl = this.gl;
    const numInstances = items.length;

    const instanceData = new Float32Array(numInstances * 8); // x, y, sizeX, sizeY, r, g, b, a
    items.forEach((item, i) => {
      const color = twgl.v3.create(...this.hexToRgb(item.color));
      const baseIndex = i * 8;
      instanceData[baseIndex + 0] = item.x;
      instanceData[baseIndex + 1] = item.y;
      instanceData[baseIndex + 2] = item.size * 2;
      instanceData[baseIndex + 3] = item.size * 2;
      instanceData[baseIndex + 4] = color[0];
      instanceData[baseIndex + 5] = color[1];
      instanceData[baseIndex + 6] = color[2];
      instanceData[baseIndex + 7] = 1.0;
    });

    const instanceBufferInfo = this.createInstanceBuffer(gl, instanceData);
    const vao = this.createVAO(gl, this.quadBufferInfo, instanceBufferInfo, this.programs.circle);

    gl.useProgram(this.programs.circle.program);
    twgl.setBuffersAndAttributes(gl, this.programs.circle, vao);
    twgl.setUniforms(this.programs.circle, { u_matrix: this.viewMatrix });
    twgl.drawBufferInfo(gl, this.quadBufferInfo, gl.TRIANGLES, this.quadBufferInfo.numElements, 0, numInstances);
  }

  drawTexturedItems(items, type) {
    if (!items || items.length === 0 || !this.iconAtlas) return;
    const gl = this.gl;
    const numInstances = items.length;

    const instanceData = new Float32Array(numInstances * 12); // x, y, sizeX, sizeY, r, g, b, a, texX, texY, texW, texH
    let validInstances = 0;
    
    items.forEach(item => {
      const key = `${type.replace(/s$/, '')}_${item.type}`;
      const texRect = this.iconMap.get(key) || this.iconMap.get('fallback');
      if (!texRect) return;

      const baseIndex = validInstances * 12;
      const size = (item.size || 10) * 2;
      
      instanceData[baseIndex + 0] = item.x;
      instanceData[baseIndex + 1] = item.y;
      instanceData[baseIndex + 2] = size;
      instanceData[baseIndex + 3] = size;
      instanceData[baseIndex + 4] = 1.0; // r
      instanceData[baseIndex + 5] = 1.0; // g
      instanceData[baseIndex + 6] = 1.0; // b
      instanceData[baseIndex + 7] = 1.0; // a
      instanceData[baseIndex + 8] = texRect[0];
      instanceData[baseIndex + 9] = texRect[1];
      instanceData[baseIndex + 10] = texRect[2];
      instanceData[baseIndex + 11] = texRect[3];
      validInstances++;
    });

    if (validInstances === 0) return;
    
    const instanceBufferInfo = this.createInstanceBuffer(gl, instanceData.slice(0, validInstances * 12));
    const vao = this.createVAO(gl, this.quadBufferInfo, instanceBufferInfo, this.programs.texture);
    
    gl.useProgram(this.programs.texture.program);
    twgl.setBuffersAndAttributes(gl, this.programs.texture, vao);
    twgl.setUniforms(this.programs.texture, {
      u_matrix: this.viewMatrix,
      u_texture: this.iconAtlas,
    });
    twgl.drawBufferInfo(gl, this.quadBufferInfo, gl.TRIANGLES, this.quadBufferInfo.numElements, 0, validInstances);
  }

  drawSnakes(snakes) {
    snakes.filter(s => s.alive && s.segments && s.segments.length > 0).forEach(snake => {
      // For a true WebGL implementation, this is the most complex part.
      // 1. Create a dynamic mesh (triangle strip) from the snake segments.
      // 2. Use a custom snake shader to draw the body with patterns.
      // 3. Draw head elements (eyes) and power-up effects as separate quads with their own shaders.
      
      // Simplified approach for this example: draw circles for each segment.
      this.drawSimpleCircles(snake.segments);
    });
  }

  drawProjectiles(projectiles) {
    if (!projectiles || projectiles.length === 0) return;
    // This would be another instanced draw call, drawing rotated quads.
    // The shader would create the trail effect.
    this.drawSimpleCircles(projectiles); // Placeholder with circles
  }

  // --- HELPER METHODS ---

  createInstanceBuffer(gl, data) {
    return twgl.createBufferInfoFromArrays(gl, {
      instanceWorldPosition: { numComponents: 2, data: data, stride: 48, offset: 0, divisor: 1 },
      instanceSize: { numComponents: 2, data: data, stride: 48, offset: 8, divisor: 1 },
      instanceColor: { numComponents: 4, data: data, stride: 48, offset: 16, divisor: 1 },
      instanceTexcoordRect: { numComponents: 4, data: data, stride: 48, offset: 32, divisor: 1 },
    });
  }
  
  createVAO(gl, bufferInfo, instanceBufferInfo, programInfo) {
    const vao = gl.createVertexArray();
    gl.bindVertexArray(vao);
    twgl.setBuffersAndAttributes(gl, programInfo, bufferInfo);
    twgl.setBuffersAndAttributes(gl, programInfo, instanceBufferInfo);
    gl.bindVertexArray(null);
    return vao;
  }

  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    ] : [1, 1, 1];
  }
}

export default WebGLRenderer;