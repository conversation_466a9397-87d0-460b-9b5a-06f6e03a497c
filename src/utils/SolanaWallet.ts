import { Keypair, Connection, PublicKey, Transaction, SystemProgram, LAMPORTS_PER_SOL, sendAndConfirmTransaction } from '@solana/web3.js';
import * as CryptoJS from 'crypto-js';
import bs58 from 'bs58';

// Solana network configuration
export const SOLANA_NETWORK = {
  DEVNET: 'https://api.devnet.solana.com',
  TESTNET: 'https://api.testnet.solana.com',
  MAINNET: 'https://api.mainnet-beta.solana.com'
};

// Use devnet for testing
export const connection = new Connection(SOLANA_NETWORK.DEVNET, 'confirmed');

// Encryption key for private keys (should be in environment variables)
const ENCRYPTION_KEY = process.env.REACT_APP_WALLET_ENCRYPTION_KEY || 'default-key-change-in-production';

export interface WalletInfo {
  publicKey: string;
  privateKey: string;
  encryptedPrivateKey: string;
}

export interface TransactionResult {
  success: boolean;
  signature?: string;
  error?: string;
}

/**
 * Generate a new Solana wallet keypair
 */
export function generateWallet(): WalletInfo {
  const keypair = Keypair.generate();
  const publicKey = keypair.publicKey.toBase58();
  const privateKey = bs58.encode(keypair.secretKey);
  const encryptedPrivateKey = encryptPrivateKey(privateKey);
  
  return {
    publicKey,
    privateKey,
    encryptedPrivateKey
  };
}

/**
 * Encrypt a private key for secure storage
 */
export function encryptPrivateKey(privateKey: string): string {
  return CryptoJS.AES.encrypt(privateKey, ENCRYPTION_KEY).toString();
}

/**
 * Decrypt a private key from storage
 */
export function decryptPrivateKey(encryptedPrivateKey: string): string {
  const bytes = CryptoJS.AES.decrypt(encryptedPrivateKey, ENCRYPTION_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
}

/**
 * Get wallet keypair from encrypted private key
 */
export function getKeypairFromEncrypted(encryptedPrivateKey: string): Keypair {
  const privateKey = decryptPrivateKey(encryptedPrivateKey);
  const secretKey = bs58.decode(privateKey);
  return Keypair.fromSecretKey(secretKey);
}

/**
 * Get wallet balance in SOL
 */
export async function getWalletBalance(publicKey: string): Promise<number> {
  try {
    const pubKey = new PublicKey(publicKey);
    const balance = await connection.getBalance(pubKey);
    return balance / LAMPORTS_PER_SOL;
  } catch (error) {
    console.error('Error getting wallet balance:', error);
    return 0;
  }
}

/**
 * Transfer SOL from one wallet to another
 */
export async function transferSOL(
  fromEncryptedPrivateKey: string,
  toPublicKey: string,
  amountSOL: number
): Promise<TransactionResult> {
  try {
    const fromKeypair = getKeypairFromEncrypted(fromEncryptedPrivateKey);
    const toPubKey = new PublicKey(toPublicKey);
    const lamports = Math.floor(amountSOL * LAMPORTS_PER_SOL);
    
    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: fromKeypair.publicKey,
        toPubkey: toPubKey,
        lamports
      })
    );
    
    const signature = await sendAndConfirmTransaction(
      connection,
      transaction,
      [fromKeypair],
      { commitment: 'confirmed' }
    );
    
    return {
      success: true,
      signature
    };
  } catch (error) {
    console.error('Error transferring SOL:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Validate a Solana public key
 */
export function isValidPublicKey(publicKey: string): boolean {
  try {
    new PublicKey(publicKey);
    return true;
  } catch {
    return false;
  }
}

/**
 * Convert SOL to lamports
 */
export function solToLamports(sol: number): number {
  return Math.floor(sol * LAMPORTS_PER_SOL);
}

/**
 * Convert lamports to SOL
 */
export function lamportsToSol(lamports: number): number {
  return lamports / LAMPORTS_PER_SOL;
}

/**
 * Get transaction details
 */
export async function getTransactionDetails(signature: string) {
  try {
    const transaction = await connection.getTransaction(signature, {
      commitment: 'confirmed'
    });
    return transaction;
  } catch (error) {
    console.error('Error getting transaction details:', error);
    return null;
  }
}

/**
 * Wait for transaction confirmation
 */
export async function waitForConfirmation(signature: string, timeout = 30000): Promise<boolean> {
  const start = Date.now();
  
  while (Date.now() - start < timeout) {
    try {
      const status = await connection.getSignatureStatus(signature);
      if (status.value?.confirmationStatus === 'confirmed' || status.value?.confirmationStatus === 'finalized') {
        return true;
      }
    } catch (error) {
      console.error('Error checking transaction status:', error);
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  return false;
}