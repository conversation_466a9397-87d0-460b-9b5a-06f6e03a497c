/**
 * <PERSON><PERSON><PERSON> to generate Solana wallets for existing user accounts
 * 
 * This script can be used to migrate existing accounts to have Solana wallets.
 * It's designed to be safe and idempotent - running it multiple times won't
 * create duplicate wallets.
 * 
 * Usage:
 * 1. Make sure your Supabase environment variables are set
 * 2. Run: npx ts-node src/scripts/generateWalletsForExistingUsers.ts
 * 
 * Or import the functions in your React app for admin functionality.
 */

import { createClient } from '@supabase/supabase-js';
import { Keypair } from '@solana/web3.js';
import bs58 from 'bs58';
import CryptoJS from 'crypto-js';

// Configuration
const SUPABASE_URL = process.env.REACT_APP_SUPABASE_URL || 'https://tbkovuplaylnboqjtney.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.REACT_APP_SUPABASE_SERVICE_KEY;
const ENCRYPTION_KEY = process.env.REACT_APP_WALLET_ENCRYPTION_KEY || 'default-key-change-in-production';

if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required for this script');
  console.error('   This script needs admin privileges to read all users and bypass RLS policies.');
  console.error('   Set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key in your environment.');
  process.exit(1);
}

// Initialize Supabase client with service role key (bypasses RLS)
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

interface WalletInfo {
  publicKey: string;
  encryptedPrivateKey: string;
}

interface UserProfile {
  id: string;
  username: string;
}

interface SolanaWallet {
  user_id: string;
  public_key: string;
}

/**
 * Encrypt a private key for secure storage
 */
function encryptPrivateKey(privateKey: string): string {
  return CryptoJS.AES.encrypt(privateKey, ENCRYPTION_KEY).toString();
}

/**
 * Generate a new Solana wallet
 */
function generateSolanaWallet(): WalletInfo {
  const keypair = Keypair.generate();
  const publicKey = keypair.publicKey.toBase58();
  const privateKey = bs58.encode(keypair.secretKey);
  const encryptedPrivateKey = encryptPrivateKey(privateKey);
  
  return {
    publicKey,
    encryptedPrivateKey
  };
}

/**
 * Get all users who don't have Solana wallets
 */
async function getUsersWithoutWallets(): Promise<UserProfile[]> {
  console.log('📊 Fetching all user profiles...');
  
  // Get all user profiles
  const { data: allUsers, error: usersError } = await supabase
    .from('user_profiles')
    .select('id, username');

  if (usersError) {
    throw new Error(`Error fetching users: ${usersError.message}`);
  }

  if (!allUsers || allUsers.length === 0) {
    console.log('📊 No users found in the database');
    return [];
  }

  console.log(`📊 Found ${allUsers.length} total users`);

  // Get all users who have active wallets
  const { data: usersWithWallets, error: walletsError } = await supabase
    .from('solana_wallets')
    .select('user_id')
    .eq('wallet_type', 'user')
    .eq('is_active', true);

  if (walletsError) {
    throw new Error(`Error fetching wallets: ${walletsError.message}`);
  }

  const userIdsWithWallets = new Set(usersWithWallets?.map(w => w.user_id) || []);
  const usersWithoutWallets = allUsers.filter(user => !userIdsWithWallets.has(user.id));

  console.log(`📊 Found ${usersWithoutWallets.length} users without wallets`);
  console.log(`📊 Found ${userIdsWithWallets.size} users with existing wallets`);

  return usersWithoutWallets;
}

/**
 * Generate a wallet for a single user
 */
async function generateWalletForUser(user: UserProfile): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`🔄 Generating wallet for user: ${user.username} (${user.id})`);
    
    // Double-check that user doesn't have a wallet
    const { data: existingWallet, error: checkError } = await supabase
      .from('solana_wallets')
      .select('public_key')
      .eq('user_id', user.id)
      .eq('wallet_type', 'user')
      .eq('is_active', true)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw new Error(`Error checking existing wallet: ${checkError.message}`);
    }

    if (existingWallet) {
      console.log(`⚠️  User ${user.username} already has a wallet: ${existingWallet.public_key}`);
      return { success: true };
    }

    // Generate new wallet
    const walletInfo = generateSolanaWallet();

    // Store wallet in database
    const { error: insertError } = await supabase
      .from('solana_wallets')
      .insert({
        user_id: user.id,
        public_key: walletInfo.publicKey,
        encrypted_private_key: walletInfo.encryptedPrivateKey,
        wallet_type: 'user',
        is_active: true
      });

    if (insertError) {
      throw new Error(`Error storing wallet: ${insertError.message}`);
    }

    console.log(`✅ Generated wallet for ${user.username}: ${walletInfo.publicKey}`);
    return { success: true };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Failed to generate wallet for ${user.username}: ${errorMessage}`);
    return { success: false, error: errorMessage };
  }
}

/**
 * Main function to generate wallets for all users without them
 */
async function generateWalletsForAllUsers(): Promise<void> {
  console.log('🚀 Starting wallet generation for existing users...');
  console.log('🔧 Configuration:');
  console.log(`   Supabase URL: ${SUPABASE_URL}`);
  console.log(`   Encryption Key: ${ENCRYPTION_KEY === 'default-key-change-in-production' ? '⚠️  Using default key!' : '✅ Custom key set'}`);
  console.log('');

  try {
    // Get users without wallets
    const usersWithoutWallets = await getUsersWithoutWallets();

    if (usersWithoutWallets.length === 0) {
      console.log('🎉 All existing users already have wallets!');
      return;
    }

    console.log(`🔄 Generating wallets for ${usersWithoutWallets.length} users...`);
    console.log('');

    const results = {
      successful: 0,
      failed: 0,
      errors: [] as string[]
    };

    // Generate wallets for each user
    for (let i = 0; i < usersWithoutWallets.length; i++) {
      const user = usersWithoutWallets[i];
      const result = await generateWalletForUser(user);
      
      if (result.success) {
        results.successful++;
      } else {
        results.failed++;
        if (result.error) {
          results.errors.push(`${user.username}: ${result.error}`);
        }
      }

      // Add a small delay to avoid overwhelming the database
      if (i < usersWithoutWallets.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Print summary
    console.log('');
    console.log('📊 SUMMARY:');
    console.log(`✅ Successful: ${results.successful}`);
    console.log(`❌ Failed: ${results.failed}`);
    
    if (results.errors.length > 0) {
      console.log('');
      console.log('❌ Errors:');
      results.errors.forEach(error => console.log(`   ${error}`));
    }

    console.log('');
    console.log('🎉 Wallet generation completed!');

  } catch (error) {
    console.error('💥 Fatal error during wallet generation:', error);
    process.exit(1);
  }
}

/**
 * Run the script if called directly
 */
if (import.meta.url === `file://${process.argv[1]}`) {
  generateWalletsForAllUsers()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

// Export functions for use in other modules
export {
  generateWalletsForAllUsers,
  getUsersWithoutWallets,
  generateWalletForUser,
  generateSolanaWallet
};