import NetworkManager from './utils/NetworkManager';
import GamepadManager from './controllers/GamepadManager.js';

class ClientGame {
  constructor(canvas, gameMode = 'classic', userData = null) {
    console.log('ClientGame constructor started');

    this.canvas = canvas;
    this.ctx = this.canvas.getContext('2d');
    this.gameMode = gameMode;
    this.userData = userData;

    // Minimap setup
    this.minimap = null;
    this.minimapCtx = null;

    // Canvas setup
    this.setupCanvas();

    // World dimensions
    this.worldWidth = 6000;
    this.worldHeight = 6000;

    // Game state from server
    this.gameState = {
      players: [],
      food: [],
      glowOrbs: [],
      coins: [],
      weapons: [],
      ammo: [],
      powerups: [],
      projectiles: []
    };

    // Local player data
    this.localPlayer = null;
    this.playerId = null;

    // Spectate mode
    this.spectating = false;
    this.spectateTarget = 0;
    this.cashedOut = false;

    // Camera
    this.camera = { x: 3000, y: 3000, zoom: 1.0 };

    // Performance optimizations for zoom
    this.zoomCache = {
      lastPlayerSize: 0,
      lastPlayerCash: 0,
      targetZoom: 1.0,
      lastZoomUpdate: 0,
      zoomUpdateInterval: 100,
      isDirty: true
    };

    // Input handling
    this.mouse = { x: this.canvas.width / 2, y: this.canvas.height / 2 };
    this.boosting = false;
    this.mouseHeld = false;

    // Gamepad support
    this.gamepadManager = new GamepadManager();
    this.setupGamepadCallbacks();

    // Network manager
    this.networkManager = NetworkManager;

    // Game state
    this.connected = false;
    this.gameRunning = false;

    // React callbacks
    this.onStateUpdate = null;
    this.onGameOver = null;
    this.onElimination = null;

    // Rendering
    this.lastRenderTime = 0;
    this.renderLoop = null;

    // FPS calculation
    this.fps = 0;
    this.frameTimes = [];

    // Throttling for performance
    this.lastGameStateUpdate = 0;
    this.gameStateUpdateRate = 1000 / 60;
    this.lastInputSent = 0;
    this.inputSendRate = 1000 / 60;

    // Icon system
    this.icons = new Map();
    this.iconsLoaded = false;
    this.loadIcons();

    console.log('ClientGame constructor completed');
  }

  loadIcons() {
    const iconMappings = {
      weapons: {
        'sidearm': 'Snake-Fang-Weapon-Icon.png',
        'laser_pistol': 'Laser-Pistol-Weapon-Icon.png',
        'laser_rifle': 'Laser-Rifle-Weapon-Icon.png',
        'plasma_smg': 'Plasma-SMG-Weapon-Icon.png',
        'plasma_cannon': 'Plasma-Cannon-Weapon-Icon.png',
        'rocket_launcher': 'Rocket-Launcher-Weapon-Icon.png',
        'rail_gun': 'Rail-Gun-Weapon-Icon.png',
        'minigun': 'Minigun-Weapon-Icon.png'
      },
      powerups: {
        'helmet': 'Combat-Helmet-Powerup-Icon.png',
        'armor_plating': 'Armor-Plating-Powerup-Icon.png',
        'shield_generator': 'Shield-Generator-Powerup-Icon.png',
        'forcefield': 'Force-Field-Powerup-Icon.png',
        'speed_boost': 'Speed-Boost-Powerup-Icon.png',
        'damage_amplifier': 'Damage-Amplifier-Powerup-Icon.png',
        'battering_ram': 'Battering-Ram-Powerup-Icon.png'
      },
      ammo: {
        'light_energy': 'Energy-Cells-Ammo-Icon.png',
        'heavy_energy': 'Heavy-Energy-Ammo-Icon.png',
        'plasma_cells': 'Plasma-Cells-Ammo-Icon.png',
        'heavy_plasma': 'Heavy-Plasma-Ammo-Icon.png',
        'rockets': 'Rockets-Ammo-Icon.png',
        'rail_slugs': 'Rail-Slugs-Ammo-Icon.png'
      }
    };

    let totalIcons = Object.values(iconMappings).reduce((sum, category) => sum + Object.keys(category).length, 0);
    let loadedCount = 0;

    const checkAllLoaded = () => {
      loadedCount++;
      if (loadedCount >= totalIcons) {
        this.iconsLoaded = true;
        console.log('All icons loaded successfully');
      }
    };

    Object.entries(iconMappings).forEach(([category, icons]) => {
      Object.entries(icons).forEach(([type, fileName]) => {
        const img = new Image();
        img.onload = () => {
          console.log(`✅ Loaded ${category} icon: ${type} -> /assets/${fileName}`);
          checkAllLoaded();
        };
        img.onerror = () => {
          console.warn(`❌ Failed to load ${category} icon: ${type} -> /assets/${fileName}`);
          checkAllLoaded();
        };
        // Avoid cache-busting query that can break some loaders; rely on dev reload for updates
        img.src = `/assets/${fileName}`;
        // Key format used by drawItemWithIcon calls: "weapon_type", "ammo_type", "powerup_type"
        this.icons.set(`${category.slice(0, -1)}_${type}`, img);
      });
    });
  }

  setupCanvas() {
    this.resizeCanvas = () => {
      const rect = this.canvas.getBoundingClientRect();
      this.canvas.width = rect.width || window.innerWidth;
      this.canvas.height = rect.height || window.innerHeight;
      console.log('Canvas resized to:', this.canvas.width, 'x', this.canvas.height);
    };
    window.addEventListener('resize', this.resizeCanvas);
    this.resizeCanvas();
  }

  async start() {
    console.log('Starting ClientGame...');
    try {
      await this.connectToServer();
      this.setupEventListeners();
      await this.joinGame();
      this.startRenderLoop();
      this.gameRunning = true;
      console.log('✅ ClientGame started successfully');
    } catch (error) {
      console.error('❌ Failed to start ClientGame:', error);
      throw error;
    }
  }

  async connectToServer() {
    console.log('Connecting to server...');
    try {
      this.networkManager.onGameStateUpdate = (gs) => this.handleGameStateUpdate(gs);
      this.networkManager.onGameJoined = (data) => this.handleGameJoined(data);
      this.networkManager.onConnectionError = (err) => this.handleConnectionError(err);
      this.networkManager.onDisconnected = (reason) => this.handleDisconnection(reason);
      this.networkManager.onCashoutResult = (res) => this.handleCashoutResult(res);
      this.networkManager.onPlayerElimination = (data) => this.handlePlayerElimination(data);

      await this.networkManager.connect();
      this.connected = true;
      console.log('✅ Network connection established');
    } catch (error) {
      console.error('❌ Failed to connect to server:', error);
      throw new Error(`Server connection failed: ${error.message}`);
    }
  }

  async joinGame() {
    const gameData = {
      gameMode: this.gameMode,
      username: this.userData?.username || 'Player_' + Math.floor(Math.random() * 1000),
      wager: this.userData?.wager || 50,
      color: '#FFD700',
      userId: this.userData?.userId
    };
    console.log('🎮 Joining game with user data:', gameData);
    this.networkManager.joinGame(gameData);
  }

  handleGameJoined(data) {
    console.log('Game joined:', data);
    this.playerId = data.playerId;
    if (data.gameState) {
      this.handleGameStateUpdate(data.gameState);
    }
  }

  handleGameStateUpdate(gameState) {
    const now = Date.now();
    if (now - this.lastGameStateUpdate < this.gameStateUpdateRate) return;
    this.lastGameStateUpdate = now;
    this.gameState = gameState;
    this.localPlayer = this.gameState.players?.find(p => p.id === this.playerId);

    if (this.localPlayer) {
      if (this.localPlayer.cashedOut && !this.cashedOut) {
        this.cashedOut = true;
        this.spectating = true;
        this.spectateTarget = 0;
      } else if (!this.localPlayer.cashedOut && this.cashedOut) {
        this.cashedOut = false;
        this.spectating = false;
      }
    }

    if (this.spectating) {
      const alivePlayers = this.gameState.players.filter(p => p.alive && p.id !== this.playerId);
      if (alivePlayers.length > 0) {
        const spectatedPlayer = alivePlayers[this.spectateTarget % alivePlayers.length];
        this.updateCameraForPlayer(spectatedPlayer);
      }
    } else if (this.localPlayer) {
      this.updateCameraZoom();
      const effectiveCanvasWidth = this.canvas.width / this.camera.zoom;
      const effectiveCanvasHeight = this.canvas.height / this.camera.zoom;
      let targetX = this.localPlayer.x - effectiveCanvasWidth / 2;
      let targetY = this.localPlayer.y - effectiveCanvasHeight / 2;
      this.camera.x = Math.max(0, Math.min(this.worldWidth - effectiveCanvasWidth, targetX));
      this.camera.y = Math.max(0, Math.min(this.worldHeight - effectiveCanvasHeight, targetY));
    }

    if (this.onStateUpdate && this.localPlayer) {
      const playerData = this.gameState.playerData || {};

      // Normalize weapon inventory for UI expectations
      // Server sends: weaponInventory.primaryWeapon / secondaryWeapon objects with {name,currentAmmo,maxAmmo}
      // GameUI expects: weaponSlots.primary / secondary as formatted strings "Name (cur/max)"
      const inv = playerData.weaponInventory || {};
      const primaryObj = inv.primaryWeapon || null;
      const secondaryObj = inv.secondaryWeapon || null;

      const formatSlot = (w) => {
        if (!w) return '';
        if (w.maxAmmo === Infinity) return `${w.name} (∞)`;
        const cur = Math.max(0, w.currentAmmo || 0);
        const max = Math.max(0, w.maxAmmo || 0);
        return `${w.name} (${cur}/${max})`;
      };

      const uiWeaponSlots = {
        primary: formatSlot(primaryObj),
        secondary: formatSlot(secondaryObj),
        sidearm: 'Snake Fang (∞)'
      };

      const uiState = {
        score: this.localPlayer.cash || 0,
        cashBalance: this.localPlayer.cash || 0,
        length: this.localPlayer.segments ? this.localPlayer.segments.length : 1,
        boost: this.localPlayer.boost || 100,
        weapon: playerData.weapon || this.localPlayer.weapon || 'None',
        weaponAmmo: playerData.currentWeapon ? (playerData.currentWeapon.type === 'sidearm' ? '∞' : `${playerData.currentWeapon.currentAmmo || 0}/${playerData.currentWeapon.maxAmmo || 0}`) : '0/0',
        weaponSlots: uiWeaponSlots,
        currentSlot: inv.currentSlot || 'sidearm',
        ammoInventory: playerData.ammoInventory || {},
        activePowerups: playerData.activePowerups || [],
        isGameOver: !this.localPlayer.alive || this.cashedOut,
        cashedOut: this.cashedOut,
        spectating: this.spectating,
        cashoutAmount: this.localPlayer.cashoutBalance || this.localPlayer.cash || 0,
        isKing: this.isKing(this.localPlayer)
      };
      this.onStateUpdate(uiState);
      if (uiState.isGameOver && this.onGameOver) {
        this.onGameOver(uiState);
      }
    }
  }

  updateCameraZoom() {
    if (!this.localPlayer) return;
    const now = Date.now();
    const snakeSize = this.localPlayer.size || 10;
    const playerCash = this.localPlayer.cash || 0;

    // Throttle updates
    const sizeChanged = Math.abs(snakeSize - this.zoomCache.lastPlayerSize) > 0.5;
    const cashChanged = Math.abs(playerCash - this.zoomCache.lastPlayerCash) > 10;
    const timeToUpdate = (now - this.zoomCache.lastZoomUpdate) > this.zoomCache.zoomUpdateInterval;

    if ((sizeChanged || cashChanged || timeToUpdate) && (sizeChanged || cashChanged || this.zoomCache.isDirty)) {
      this.zoomCache.lastPlayerSize = snakeSize;
      this.zoomCache.lastPlayerCash = playerCash;
      this.zoomCache.lastZoomUpdate = now;
      this.zoomCache.isDirty = false;

      // Make zoom-out on growth less aggressive
      // Reduce contribution of size and cash, and flatten the curve
      const growthFactor = (snakeSize * 0.03) * 0.6 + Math.sqrt(playerCash * 0.003) * 0.4;
      const normalizedGrowth = Math.min(growthFactor, 2.0);
      // Higher floor and smaller multiplier -> gentler zoom-out
      this.zoomCache.targetZoom = Math.max(0.6, 1.0 - (normalizedGrowth - 1.0) * 0.08);
    }

    // Ease more slowly to avoid abrupt changes
    this.camera.zoom += (this.zoomCache.targetZoom - this.camera.zoom) * 0.015;
    this.camera.zoom = Math.max(0.55, Math.min(1.0, this.camera.zoom));
  }

  updateCameraForPlayer(player) {
    if (!player) return;
    const growthFactor = (player.size || 10) * 0.07 + Math.sqrt((player.cash || 0) * 0.01) * 0.3;
    const normalizedGrowth = Math.min(growthFactor, 4.0);
    const targetZoom = Math.max(0.4, 1.0 - (normalizedGrowth - 1.0) * 0.2);
    this.camera.zoom += (targetZoom - this.camera.zoom) * 0.04;
    this.camera.zoom = Math.max(0.4, Math.min(1.0, this.camera.zoom));
    const effectiveCanvasWidth = this.canvas.width / this.camera.zoom;
    const effectiveCanvasHeight = this.canvas.height / this.camera.zoom;
    let targetX = player.x - effectiveCanvasWidth / 2;
    let targetY = player.y - effectiveCanvasHeight / 2;
    this.camera.x = Math.max(0, Math.min(this.worldWidth - effectiveCanvasWidth, targetX));
    this.camera.y = Math.max(0, Math.min(this.worldHeight - effectiveCanvasHeight, targetY));
  }

  handleConnectionError(error) {
    console.error('Connection error:', error);
    this.connected = false;
  }

  handleDisconnection(reason) {
    console.log('Disconnected:', reason);
    this.connected = false;
    this.gameRunning = false;
  }

  handleCashoutResult(result) {
    console.log('💰 Cashout result received:', result);
    if (this.onStateUpdate) {
      if (result.success) {
        this.onStateUpdate({ cashoutResult: result, showCashoutMessage: true });
      } else {
        this.onStateUpdate({ cashoutError: result.reason, showCashoutError: true });
      }
    }
  }

  handlePlayerElimination(eliminationData) {
    if (this.localPlayer && eliminationData.killerName === this.localPlayer.username && this.onElimination) {
      this.onElimination(eliminationData);
    }
  }

  setupEventListeners() {
    this.canvas.addEventListener('mousemove', (e) => {
      const rect = this.canvas.getBoundingClientRect();
      this.mouse.x = e.clientX - rect.left;
      this.mouse.y = e.clientY - rect.top;
      this.sendInput();
    });

    const handleMouseDown = (e) => {
      if (e.button === 0) {
        if (this.gameMode === 'warfare') {
          this.mouseHeld = true;
          this.shoot();
          this.startContinuousShooting();
        } else {
          this.boosting = true;
        }
      } else if (e.button === 2) {
        this.boosting = true;
      }
      this.sendInput();
    };

    const handleMouseUp = (e) => {
      if (e.button === 0) {
        this.mouseHeld = false;
        this.stopContinuousShooting();
        if (this.gameMode !== 'warfare') {
          this.boosting = false;
        }
      } else if (e.button === 2) {
        this.boosting = false;
      }
      this.sendInput();
    };

    this.canvas.addEventListener('mousedown', handleMouseDown);
    this.canvas.addEventListener('mouseup', handleMouseUp);
    this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    this.canvas.addEventListener('wheel', (e) => {
      e.preventDefault();
      if (this.gameMode === 'warfare' && this.localPlayer?.alive) {
        this.switchWeaponWithScroll(e.deltaY > 0 ? 1 : -1);
      }
    });

    document.addEventListener('keydown', (e) => {
      if (e.code === 'Space') {
        e.preventDefault();
        if (this.gameMode === 'warfare') {
          this.shoot();
        } else {
          this.boosting = true;
          this.sendInput();
        }
      }
      if (e.code === 'KeyC') { e.preventDefault(); this.cashOut(); }
      if (e.code === 'Tab' && this.spectating) { e.preventDefault(); this.cycleThroughPlayers(); }
      if (this.gameMode === 'warfare') {
        if (e.code === 'Digit1') this.switchWeapon('primaryWeapon');
        if (e.code === 'Digit2') this.switchWeapon('secondaryWeapon');
        if (e.code === 'Digit3') this.switchWeapon('sidearm');
      }
    });

    document.addEventListener('keyup', (e) => {
      if (e.code === 'Space') {
        e.preventDefault();
        this.boosting = false;
        this.sendInput();
      }
    });
  }

  setupGamepadCallbacks() {
    this.gamepadManager.setCallback('onMove', (leftX, leftY) => {
      if (!this.connected || !this.localPlayer) return;
      const magnitude = Math.sqrt(leftX * leftX + leftY * leftY);
      if (magnitude > this.gamepadManager.deadzone) {
        const angle = Math.atan2(leftY, leftX);
        const worldX = this.localPlayer.x + Math.cos(angle) * 200;
        const worldY = this.localPlayer.y + Math.sin(angle) * 200;
        this.mouse.x = (worldX - this.camera.x) * this.camera.zoom;
        this.mouse.y = (worldY - this.camera.y) * this.camera.zoom;
        this.sendInput();
      }
    });
    this.gamepadManager.setCallback('onBoost', (b) => { this.boosting = b; this.sendInput(); });
    this.gamepadManager.setCallback('onShoot', () => { if (this.gameMode === 'warfare') this.shoot(); });
    this.gamepadManager.setCallback('onCashout', () => this.cashOut());
    this.gamepadManager.setCallback('onWeaponSwitch', (s) => { if (this.gameMode === 'warfare') this.switchWeapon(s); });
    this.gamepadManager.setCallback('onSpectateNext', () => { if (this.spectating) this.cycleThroughPlayers(); });
  }

  sendInput() {
    if (!this.connected || !this.localPlayer) return;
    const now = Date.now();
    if (now - this.lastInputSent < this.inputSendRate) return;
    const worldMouseX = (this.mouse.x / this.camera.zoom) + this.camera.x;
    const worldMouseY = (this.mouse.y / this.camera.zoom) + this.camera.y;
    const dx = worldMouseX - this.localPlayer.x;
    const dy = worldMouseY - this.localPlayer.y;
    const inputData = {
      targetAngle: Math.atan2(dy, dx),
      boosting: this.boosting,
      mouseHeld: this.mouseHeld,
      timestamp: now
    };
    this.networkManager.sendPlayerInput(inputData);
    this.lastInputSent = now;
  }

  shoot() {
    if (!this.connected || this.gameMode !== 'warfare' || !this.localPlayer) return;
    const shootData = {
      targetX: (this.mouse.x / this.camera.zoom) + this.camera.x,
      targetY: (this.mouse.y / this.camera.zoom) + this.camera.y,
      timestamp: Date.now()
    };
    this.networkManager.socket.emit('playerShoot', shootData);
  }

  startContinuousShooting() {
    if (this.shootingInterval) clearInterval(this.shootingInterval);
    const playerData = this.gameState?.playerData;
    if (!playerData?.currentWeapon) return;
    const firingRates = { plasma_smg: 8, minigun: 15 };
    const fireRate = firingRates[playerData.currentWeapon.type] || 0;
    if (fireRate > 0) {
      this.shootingInterval = setInterval(() => {
        if (this.mouseHeld && this.localPlayer?.alive) {
          this.shoot();
        } else {
          this.stopContinuousShooting();
        }
      }, 1000 / fireRate);
    }
  }

  stopContinuousShooting() {
    if (this.shootingInterval) {
      clearInterval(this.shootingInterval);
      this.shootingInterval = null;
    }
  }

  switchWeapon(slot) {
    if (!this.connected || this.gameMode !== 'warfare') return;
    this.networkManager.socket.emit('switchWeapon', { slot });
    if (this.mouseHeld) {
      this.stopContinuousShooting();
      setTimeout(() => { if (this.mouseHeld) this.startContinuousShooting(); }, 50);
    }
  }

  switchWeaponWithScroll(direction) {
    const playerData = this.gameState?.playerData;
    if (!playerData?.weaponInventory) return;
    const inventory = playerData.weaponInventory;
    const slots = ['sidearm', 'primaryWeapon', 'secondaryWeapon'];
    const availableSlots = slots.filter(s => s === 'sidearm' || inventory[s]);
    if (availableSlots.length <= 1) return;
    const currentIndex = availableSlots.indexOf(inventory.currentSlot);
    const nextIndex = (currentIndex + direction + availableSlots.length) % availableSlots.length;
    const nextSlot = availableSlots[nextIndex];
    const weapon = inventory[nextSlot] || { name: 'Snake Fang' };
    this.showWeaponSwitchFeedback(weapon.name || weapon.type);
    this.switchWeapon(nextSlot);
  }

  showWeaponSwitchFeedback(weaponName) {
    this.weaponSwitchNotification = {
      text: `🔄 ${weaponName}`,
      startTime: Date.now(),
      duration: 1500
    };
  }

  cashOut() {
    if (!this.connected || !this.localPlayer?.alive) return;
    this.networkManager.socket.emit('playerCashOut', { timestamp: Date.now() });
  }

  cycleThroughPlayers() {
    if (!this.spectating) return;
    const alivePlayers = this.gameState.players.filter(p => p.alive && p.id !== this.playerId);
    if (alivePlayers.length > 0) {
      this.spectateTarget = (this.spectateTarget + 1) % alivePlayers.length;
    }
  }

  restart() {
    console.log('🔄 Restart called - requesting respawn');
    if (!this.connected) return;
    this.spectating = false;
    this.cashedOut = false;
    this.networkManager.socket.emit('playerRespawn', { timestamp: Date.now() });
  }

  startRenderLoop() {
    const render = (timestamp) => {
      this.render(timestamp);
      this.renderLoop = requestAnimationFrame(render);
    };
    this.renderLoop = requestAnimationFrame(render);
  }

  render(timestamp) {
    if (!this.gameRunning) return;
    this.lastRenderTime = timestamp;
    this.gamepadManager.update();
    this.calculateFPS(timestamp);

    this.ctx.fillStyle = '#0a0a0a';
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

    this.ctx.save();
    this.ctx.scale(this.camera.zoom, this.camera.zoom);

    this.drawGrid();
    this.drawWorldBounds();
    this.drawFood();
    this.drawGlowOrbs();
    this.drawSnakes();
    this.drawCoins();
    if (this.gameMode === 'warfare') {
      this.drawWeapons();
      this.drawAmmo();
      this.drawPowerups();
      this.drawProjectiles();
      this.drawCollisionEffects();
    }
    this.drawKingIndicator();
    this.drawMinimap();
    this.drawWeaponSwitchNotification();

    this.ctx.restore();
  }

  drawGrid() {
    this.ctx.strokeStyle = '#1a1a1a';
    this.ctx.lineWidth = 1;
    const gridSize = 50;
    const startX = Math.floor(this.camera.x / gridSize) * gridSize;
    const startY = Math.floor(this.camera.y / gridSize) * gridSize;
    const endX = this.camera.x + this.canvas.width / this.camera.zoom;
    const endY = this.camera.y + this.canvas.height / this.camera.zoom;
    for (let x = startX; x < endX; x += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(x - this.camera.x, 0);
      this.ctx.lineTo(x - this.camera.x, this.canvas.height / this.camera.zoom);
      this.ctx.stroke();
    }
    for (let y = startY; y < endY; y += gridSize) {
      this.ctx.beginPath();
      this.ctx.moveTo(0, y - this.camera.y);
      this.ctx.lineTo(this.canvas.width / this.camera.zoom, y - this.camera.y);
      this.ctx.stroke();
    }
  }

  drawWorldBounds() {
    this.ctx.strokeStyle = '#444444';
    this.ctx.lineWidth = 3;
    this.ctx.strokeRect(-this.camera.x, -this.camera.y, this.worldWidth, this.worldHeight);
  }

  // Disable culling: always draw entities regardless of on-screen checks
  drawFood() {
    this.gameState.food?.forEach(food => {
      const x = food.x - this.camera.x;
      const y = food.y - this.camera.y;
      this.ctx.fillStyle = food.color;
      this.ctx.beginPath();
      this.ctx.arc(x, y, food.size, 0, Math.PI * 2);
      this.ctx.fill();

      this.gameState.players?.forEach(snake => {
        if (snake.alive && snake.segments?.length > 0) {
          const headX = snake.segments[0].x - this.camera.x;
          const headY = snake.segments[0].y - this.camera.y;
          const distance = Math.hypot(x - headX, y - headY);
          const vacuumRadius = snake.size * (snake.boosting ? 8 : 6);
          if (distance < vacuumRadius) {
            this.drawSimpleVacuumEffect(food, snake, x, y, headX, headY);
          }
        }
      });
    });
  }

  drawSimpleVacuumEffect(food, snake, foodX, foodY, headX, headY) {
    const dx = headX - foodX;
    const dy = headY - foodY;
    const dist = Math.hypot(dx, dy);
    if (dist < 1) return;
    const trailCount = 3;
    for (let i = 0; i < trailCount; i++) {
        const progress = ((Date.now() * 0.002) + (i / trailCount)) % 1;
        const trailX = foodX + dx * progress;
        const trailY = foodY + dy * progress;
        const alpha = (1 - progress) * 0.7;
        const size = (1 - progress) * 2;
        this.ctx.fillStyle = this.addAlpha(food.color, alpha);
        this.ctx.beginPath();
        this.ctx.arc(trailX, trailY, size, 0, Math.PI * 2);
        this.ctx.fill();
    }
  }

  drawGlowOrbs() {
    this.gameState.glowOrbs?.forEach(orb => {
      const x = orb.x - this.camera.x;
      const y = orb.y - this.camera.y;
      const glowSize = orb.size + Math.sin(Date.now() * 0.002) * 3;
      const baseColor = `hsl(${orb.hue}, 100%, 70%)`;
      const glow = this.ctx.createRadialGradient(x, y, 0, x, y, glowSize * 2);
      glow.addColorStop(0, this.addAlpha(baseColor, 0.6));
      glow.addColorStop(0.5, this.addAlpha(baseColor, 0.3));
      glow.addColorStop(1, 'transparent');
      this.ctx.fillStyle = glow;
      this.ctx.beginPath();
      this.ctx.arc(x, y, glowSize * 2, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.fillStyle = baseColor;
      this.ctx.beginPath();
      this.ctx.arc(x, y, glowSize, 0, Math.PI * 2);
      this.ctx.fill();
    });
  }

  drawSnakes() {
    // No culling: draw all alive snakes
    this.gameState.players?.filter(s => s.alive).forEach(snake => {
      this.drawRealisticSnake(snake);
    });
  }

  // **** ORIGINAL, FULLY-FEATURED SNAKE RENDERING ****
  drawRealisticSnake(snake) {
    if (!snake.segments || snake.segments.length < 1) return;
    const isInvincible = snake.invincible;
    const shouldBlink = isInvincible && Math.sin(snake.blinkPhase || 0) < 0;
    if (shouldBlink) {
      this.drawWagerDisplay(snake);
      return;
    }
    // The full, original design requires all these components
    this.drawSmoothSnakeBody(snake);
    this.drawSnakePattern(snake);
    this.drawSnakeHead(snake);
    this.drawWagerDisplay(snake);
  }

  drawSmoothSnakeBody(snake) {
    this.ctx.lineCap = 'round';
    this.ctx.lineJoin = 'round';
    // Step 1: Draw the unified golden outline
    this.ctx.strokeStyle = snake.color;
    if (snake.segments.length > 1) {
      this.ctx.lineWidth = snake.size * 2.0;
      this.ctx.beginPath();
      for (let i = 1; i < snake.segments.length; i++) {
        const seg = snake.segments[i];
        const x = seg.x - this.camera.x;
        const y = seg.y - this.camera.y;
        if (i === 1) this.ctx.moveTo(x, y);
        else this.ctx.lineTo(x, y);
      }
      this.ctx.stroke();
    }
    // Draw wider head outline
    const head = snake.segments[0];
    const headX = head.x - this.camera.x;
    const headY = head.y - this.camera.y;
    this.ctx.lineWidth = snake.size * 2.8;
    this.ctx.beginPath();
    if (snake.segments.length > 1) {
      const neck = snake.segments[1];
      this.ctx.moveTo(neck.x - this.camera.x, neck.y - this.camera.y);
      this.ctx.lineTo(headX, headY);
    } else {
      this.ctx.moveTo(headX, headY);
      this.ctx.lineTo(headX, headY);
    }
    this.ctx.stroke();
    // Step 2: Fill the interior with black
    if (snake.segments.length > 1) {
      this.ctx.strokeStyle = '#000000';
      this.ctx.lineWidth = snake.size * 1.6;
      this.ctx.beginPath();
      for (let i = 1; i < snake.segments.length; i++) {
        const seg = snake.segments[i];
        const x = seg.x - this.camera.x;
        const y = seg.y - this.camera.y;
        if (i === 1) this.ctx.moveTo(x, y);
        else this.ctx.lineTo(x, y);
      }
      this.ctx.stroke();
    }
    // Fill head interior with black
    this.ctx.lineWidth = snake.size * 2.4;
    this.ctx.beginPath();
    if (snake.segments.length > 1) {
      const neck = snake.segments[1];
      this.ctx.moveTo(neck.x - this.camera.x, neck.y - this.camera.y);
      this.ctx.lineTo(headX, headY);
    } else {
      this.ctx.moveTo(headX, headY);
      this.ctx.lineTo(headX, headY);
    }
    this.ctx.stroke();
    // Step 3: Add golden center dots
    for (let i = 0; i < snake.segments.length; i++) {
      const seg = snake.segments[i];
      const x = seg.x - this.camera.x;
      const y = seg.y - this.camera.y;
      const dotSize = i === 0 ? snake.size * 0.25 : snake.size * 0.15;
      this.ctx.fillStyle = snake.color;
      this.ctx.beginPath();
      this.ctx.arc(x, y, dotSize, 0, Math.PI * 2);
      this.ctx.fill();
    }
  }

  drawSnakePattern(snake) {
    const time = Date.now() * 0.001;
    for (let i = 0; i < snake.segments.length; i += 3) {
      const seg = snake.segments[i];
      const x = seg.x - this.camera.x;
      const y = seg.y - this.camera.y;
      const segRatio = 1 - (i / snake.segments.length) * 0.4;
      const auraSize = (snake.size * segRatio) * (2.5 + Math.sin(time * 2 + i * 0.1) * 0.3);
      const auraGradient = this.ctx.createRadialGradient(x, y, 0, x, y, auraSize);
      auraGradient.addColorStop(0, this.addAlpha(snake.color, 0.2));
      auraGradient.addColorStop(0.5, this.addAlpha(snake.color, 0.1));
      auraGradient.addColorStop(1, 'transparent');
      this.ctx.fillStyle = auraGradient;
      this.ctx.beginPath();
      this.ctx.arc(x, y, auraSize, 0, Math.PI * 2);
      this.ctx.fill();
    }
    for (let i = 0; i < snake.segments.length - 1; i += 2) {
      const seg = snake.segments[i];
      const x = seg.x - this.camera.x;
      const y = seg.y - this.camera.y;
      const segRatio = 1 - (i / snake.segments.length) * 0.4;
      const scaleSize = (snake.size * segRatio) * 0.4;
      const angle = (snake.angle || 0) + (i * 0.1) + Math.sin(time + i * 0.2) * 0.1;
      this.ctx.save();
      this.ctx.translate(x, y);
      this.ctx.rotate(angle);
      const scaleGradient = this.ctx.createRadialGradient(-scaleSize * 0.2, -scaleSize * 0.2, 0, 0, 0, scaleSize);
      scaleGradient.addColorStop(0, '#ffffff');
      scaleGradient.addColorStop(0.3, snake.color);
      scaleGradient.addColorStop(0.6, this.darkenColor(snake.color, 0.2));
      scaleGradient.addColorStop(0.8, snake.color);
      scaleGradient.addColorStop(1, this.darkenColor(snake.color, 0.3));
      this.ctx.fillStyle = scaleGradient;
      this.ctx.beginPath();
      for (let j = 0; j < 6; j++) {
        const scaleAngle = (j * Math.PI / 3);
        const hx = Math.cos(scaleAngle) * scaleSize;
        const hy = Math.sin(scaleAngle) * scaleSize;
        if (j === 0) this.ctx.moveTo(hx, hy);
        else this.ctx.lineTo(hx, hy);
      }
      this.ctx.closePath();
      this.ctx.fill();
      const shineOpacity = Math.max(0, Math.sin(time * 3 + i * 0.2)) * 0.4;
      this.ctx.fillStyle = this.addAlpha('#ffffff', shineOpacity);
      this.ctx.beginPath();
      this.ctx.arc(-scaleSize * 0.2, -scaleSize * 0.2, scaleSize * 0.3, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.restore();
    }
  }

  drawSnakeHead(snake) {
    const head = snake.segments[0];
    const x = head.x - this.camera.x;
    const y = head.y - this.camera.y;
    const time = Date.now() * 0.001;
    const headSize = snake.size * 1.2;
    const eyeSize = headSize * 0.3;
    let eyeAngle = snake.angle || 0;
    if (!snake.angle && snake.segments.length > 1) {
      const neck = snake.segments[1];
      eyeAngle = Math.atan2(head.y - neck.y, head.x - neck.x);
    }
    const eyeDistance = headSize * 0.5;
    for (let i = -1; i <= 1; i += 2) {
      const eyeX = x + Math.cos(eyeAngle + Math.PI/2) * eyeDistance * i;
      const eyeY = y + Math.sin(eyeAngle + Math.PI/2) * eyeDistance * i;
      this.ctx.fillStyle = '#00ffff';
      this.ctx.beginPath();
      this.ctx.arc(eyeX, eyeY, eyeSize, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.fillStyle = '#ff0000';
      this.ctx.beginPath();
      this.ctx.arc(eyeX, eyeY, eyeSize * 0.5, 0, Math.PI * 2);
      this.ctx.fill();
    }
    if (Math.sin(time * 4) > 0.7) {
      const tongueLength = headSize * 2;
      const tongueAngle = eyeAngle;
      const tongueStartX = x + Math.cos(tongueAngle) * headSize;
      const tongueStartY = y + Math.sin(tongueAngle) * headSize;
      const tongueEndX = tongueStartX + Math.cos(tongueAngle) * tongueLength;
      const tongueEndY = tongueStartY + Math.sin(tongueAngle) * tongueLength;
      this.ctx.strokeStyle = '#ff3366';
      this.ctx.lineWidth = headSize * 0.15;
      this.ctx.lineCap = 'round';
      this.ctx.beginPath();
      this.ctx.moveTo(tongueStartX, tongueStartY);
      this.ctx.lineTo(tongueEndX, tongueEndY);
      this.ctx.stroke();
      for (let i = -1; i <= 1; i += 2) {
        this.ctx.beginPath();
        this.ctx.moveTo(tongueEndX, tongueEndY);
        this.ctx.lineTo(tongueEndX + Math.cos(tongueAngle + Math.PI/4 * i) * tongueLength * 0.3, tongueEndY + Math.sin(tongueAngle + Math.PI/4 * i) * tongueLength * 0.3);
        this.ctx.stroke();
      }
    }
    this.drawVacuumIndicator(snake, x, y);
    this.drawActivePowerupEffects(snake, x, y, headSize);
  }
  // **** END OF RESTORED SNAKE RENDERING ****

  drawWagerDisplay(snake) {
    if (!snake.segments?.length) return;
    const headX = snake.segments[0].x - this.camera.x;
    const headY = snake.segments[0].y - this.camera.y;
    const isKing = this.isKing(snake);
    const crownHeight = isKing ? 35 : 0;
    const tagY = headY - snake.size * 1.8 - crownHeight;

    if (isKing) {
      this.drawCrownAboveTag(headX, tagY - 10, snake.size);
    }

    this.ctx.save();
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    const username = snake.username || 'Player';
    const cash = snake.cash || 0;

    this.ctx.font = 'bold 12px "Orbitron", monospace';
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillText(username, headX + 1, tagY - 5);
    this.ctx.lineWidth = 3;
    this.ctx.strokeStyle = isKing ? '#FFD700' : snake.color;
    this.ctx.strokeText(username, headX, tagY - 6);
    this.ctx.fillStyle = '#333333';
    this.ctx.fillText(username, headX, tagY - 6);

    const formattedCash = cash >= 1000 ? `$${(cash / 1000).toFixed(1)}K` : `$${Math.floor(cash)}`;
    this.ctx.font = 'bold 11px "Orbitron", monospace';
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    this.ctx.fillText(formattedCash, headX + 1, tagY + 9);
    this.ctx.fillStyle = isKing ? '#FFD700' : '#00FF00';
    this.ctx.fillText(formattedCash, headX, tagY + 8);

    this.ctx.restore();
  }

  drawCrownAboveTag(centerX, centerY, headSize) {
    const crownSize = headSize * 3.0;

    // Always use the crown image above the king's head (all modes)
    if (!this.crownImage) {
      this.crownImage = new Image();
      this.crownImage.src = '/assets/King_of_the_Pit_Crown_Icon.png';
      this.crownImage.onerror = () => { this.crownImageFailed = true; };
    }
    if (this.crownImage.complete && !this.crownImageFailed && this.crownImage.naturalWidth > 0) {
      this.ctx.shadowColor = 'rgba(255, 215, 0, 0.7)';
      this.ctx.shadowBlur = 15;
      this.ctx.drawImage(this.crownImage, centerX - crownSize / 2, centerY - crownSize / 2, crownSize, crownSize);
      this.ctx.shadowColor = 'transparent';
      this.ctx.shadowBlur = 0;
      return;
    }

    // Fallback vector crown while image loads
    this.ctx.fillStyle = '#FFD700';
    this.ctx.fillRect(centerX - crownSize/2, centerY, crownSize, crownSize * 0.3);
    this.ctx.beginPath();
    this.ctx.moveTo(centerX - crownSize/2, centerY);
    this.ctx.lineTo(centerX - crownSize/3, centerY - crownSize * 0.4);
    this.ctx.lineTo(centerX, centerY - crownSize * 0.2);
    this.ctx.lineTo(centerX + crownSize/3, centerY - crownSize * 0.4);
    this.ctx.lineTo(centerX + crownSize/2, centerY);
    this.ctx.closePath();
    this.ctx.fill();
  }

  addAlpha(color, alpha) {
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
    return color;
  }
  
  darkenColor(color, factor) {
    if (color.startsWith('#')) {
      const r = Math.floor(parseInt(color.slice(1, 3), 16) * (1 - factor));
      const g = Math.floor(parseInt(color.slice(3, 5), 16) * (1 - factor));
      const b = Math.floor(parseInt(color.slice(5, 7), 16) * (1 - factor));
      return `rgb(${r}, ${g}, ${b})`;
    }
    return color;
  }

  drawVacuumIndicator(snake, headX, headY) {
    const vacuumRadius = snake.size * (snake.boosting ? 8 : 6);
    const alpha = (snake.boosting ? 0.25 : 0.15) * (0.7 + Math.sin(Date.now() * 0.004) * 0.3);
    this.ctx.strokeStyle = this.addAlpha(snake.color, alpha);
    this.ctx.lineWidth = 2;
    this.ctx.setLineDash([5, 5]);
    this.ctx.beginPath();
    this.ctx.arc(headX, headY, vacuumRadius, 0, Math.PI * 2);
    this.ctx.stroke();
    this.ctx.setLineDash([]);
  }

  isKing(snake) {
    if (!this.gameState.players?.length) return false;
    const king = this.gameState.players.reduce((p, c) => (c.cash > p.cash) ? c : p);
    return king.id === snake.id;
  }

  drawActivePowerupEffects(snake, headX, headY, headSize) {
    snake.activePowerups?.forEach(powerup => {
        const screenSegments = snake.segments.map(seg => ({...seg, x: seg.x - this.camera.x, y: seg.y - this.camera.y}));
      switch (powerup.type) {
        case 'helmet':
          const activeHelmet = snake.activePowerups?.find(p => p.type === 'helmet');
          if (activeHelmet) {
            this.ctx.strokeStyle = '#AAAAAA';
            this.ctx.lineWidth = headSize * 0.4;
            this.ctx.beginPath();
            this.ctx.arc(headX, headY, headSize, Math.PI, Math.PI * 2);
            this.ctx.stroke();
          }
          break;
        case 'forcefield':
          this.ctx.strokeStyle = 'rgba(0, 255, 255, 0.5)';
          this.ctx.lineWidth = 4;
          this.ctx.beginPath();
          screenSegments.forEach((seg, i) => {
              if (i === 0) this.ctx.moveTo(seg.x, seg.y);
              else this.ctx.lineTo(seg.x, seg.y);
          });
          this.ctx.stroke();
          break;
        case 'battering_ram':
          if (snake.boosting) {
            this.ctx.fillStyle = 'rgba(255, 100, 0, 0.7)';
            this.ctx.beginPath();
            const angle = snake.angle || 0;
            const ramHeadX = headX + Math.cos(angle) * headSize;
            const ramHeadY = headY + Math.sin(angle) * headSize;
            this.ctx.moveTo(ramHeadX + Math.cos(angle) * headSize, ramHeadY + Math.sin(angle) * headSize);
            this.ctx.lineTo(ramHeadX + Math.cos(angle - 1.2) * headSize, ramHeadY + Math.sin(angle - 1.2) * headSize);
            this.ctx.lineTo(ramHeadX + Math.cos(angle + 1.2) * headSize, ramHeadY + Math.sin(angle + 1.2) * headSize);
            this.ctx.closePath();
            this.ctx.fill();
          }
          break;
        case 'shield_generator':
            this.ctx.fillStyle = 'rgba(0, 170, 255, 0.3)';
            this.ctx.beginPath();
            this.ctx.arc(headX, headY, headSize * 2, 0, Math.PI * 2);
            this.ctx.fill();
            break;
        case 'speed_boost':
            const screenCoordsSegments = snake.segments.map(s => ({...s, x: s.x - this.camera.x, y: s.y - this.camera.y}));
            this.drawSpeedBoostEffect(snake, screenCoordsSegments, headSize, snake.boosting || false);
            break;
        case 'damage_amplifier':
          const glow = this.ctx.createRadialGradient(headX, headY, 0, headX, headY, headSize * 2);
          glow.addColorStop(0, 'rgba(255, 68, 68, 0.5)');
          glow.addColorStop(1, 'transparent');
          this.ctx.fillStyle = glow;
          this.ctx.beginPath();
          this.ctx.arc(headX, headY, headSize * 2, 0, Math.PI * 2);
          this.ctx.fill();
          break;
      }
    });
  }
  
  // **** ORIGINAL, FULLY-FEATURED SPEED BOOST RENDERING ****
  drawSpeedBoostEffect(snake, segments, headSize, isBoosting) {
    if (!snake || !segments || segments.length === 0) return;
    const time = Date.now() * 0.005;
    this.ctx.save();
    const trailColor = 'rgba(255, 221, 0, 0.5)';
    const headX = segments[0].x;
    const headY = segments[0].y;
    const baseAuraSize = headSize * (isBoosting ? 1.5 : 1.2);
    const auraPulse = (Math.sin(time * (isBoosting ? 10 : 5)) + 1) / 2;
    const currentAuraSize = baseAuraSize * (0.8 + auraPulse * 0.4);
    const headAngle = snake.angle || 0;
    this.ctx.beginPath();
    this.ctx.moveTo(headX + Math.cos(headAngle - Math.PI / 2) * headSize * 0.5, headY + Math.sin(headAngle - Math.PI / 2) * headSize * 0.5);
    for (let i = 0; i < 3; i++) {
        const flareAngle = headAngle + (i - 1) * (isBoosting ? 0.5 : 0.3);
        const flareLength = currentAuraSize * (1 + Math.random() * 0.3);
        this.ctx.lineTo(headX + Math.cos(flareAngle) * flareLength, headY + Math.sin(flareAngle) * flareLength);
    }
    this.ctx.lineTo(headX + Math.cos(headAngle + Math.PI / 2) * headSize * 0.5, headY + Math.sin(headAngle + Math.PI / 2) * headSize * 0.5);
    this.ctx.closePath();
    const headGradient = this.ctx.createRadialGradient(headX, headY, headSize * 0.2, headX, headY, currentAuraSize);
    headGradient.addColorStop(0, `rgba(255, 221, 0, ${0.8 + auraPulse * 0.2})`);
    headGradient.addColorStop(0.5, `rgba(255, 170, 0, ${0.5 + auraPulse * 0.3})`);
    headGradient.addColorStop(1, 'transparent');
    this.ctx.fillStyle = headGradient;
    this.ctx.fill();
    if (segments.length > 1) {
        this.ctx.beginPath();
        this.ctx.moveTo(segments[0].x, segments[0].y);
        for (let i = 1; i < segments.length; i++) {
            const seg = segments[i];
            const prevSeg = segments[i - 1];
            const controlX = (prevSeg.x + seg.x) / 2;
            const controlY = (prevSeg.y + seg.y) / 2;
            this.ctx.quadraticCurveTo(prevSeg.x, prevSeg.y, controlX, controlY);
        }
        this.ctx.lineWidth = headSize * 0.3 * (0.5 + auraPulse);
        this.ctx.strokeStyle = trailColor;
        this.ctx.globalAlpha = 0.3 + auraPulse * 0.3;
        this.ctx.stroke();
        this.ctx.globalAlpha = 1.0;
    }
    this.ctx.restore();
  }

  drawItemWithIcon(item, iconKey, fallbackIcon) {
    const x = item.x - this.camera.x;
    const y = item.y - this.camera.y;

    // Make icons bigger: bump glow and sprite size by 40%
    const sizeMultiplier = 1.4;
    const iconSize = (item.size || 10) * sizeMultiplier;

    const glow = this.ctx.createRadialGradient(x, y, 0, x, y, iconSize * 2.2);
    glow.addColorStop(0, this.addAlpha(item.color, 0.75));
    glow.addColorStop(1, 'transparent');
    this.ctx.fillStyle = glow;
    this.ctx.beginPath();
    this.ctx.arc(x, y, iconSize * 2.2, 0, Math.PI * 2);
    this.ctx.fill();

    const iconImage = this.icons.get(iconKey);
    if (iconImage?.complete && iconImage.naturalWidth > 0 && this.iconsLoaded) {
      this.ctx.drawImage(iconImage, x - iconSize, y - iconSize, iconSize * 2, iconSize * 2);
    } else {
      this.ctx.fillStyle = '#FFFFFF';
      this.ctx.font = `${Math.round(iconSize)}px Arial`;
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';
      this.ctx.fillText(fallbackIcon, x, y);
    }
  }
  
  // Ensure icon keys match loader keys and draw uncollected items
  drawWeapons() {
    this.gameState.weapons?.forEach(w => {
      if (w.collected) return;
      // Loader stored as "weapon_<type>"
      this.drawItemWithIcon(w, `weapon_${w.type}`, '🔫');
    });
  }
  drawAmmo() {
    this.gameState.ammo?.forEach(a => {
      if (a.collected) return;
      this.drawItemWithIcon(a, `ammo_${a.type}`, '🔋');
    });
  }
  drawPowerups() {
    this.gameState.powerups?.forEach(p => {
      if (p.collected) return;
      this.drawItemWithIcon(p, `powerup_${p.type}`, '⚡️');
    });
  }

  drawProjectiles() {
    this.gameState.projectiles?.forEach(p => this.drawProjectile(p));
  }
  
  drawCollisionEffects() {
    this.gameState.collisionEffects?.forEach(effect => {
        effect.particles.forEach(p => {
            const pX = p.x - this.camera.x;
            const pY = p.y - this.camera.y;
            const alpha = 1 - (p.life / p.maxLife);
            if (alpha > 0) {
                this.ctx.fillStyle = this.addAlpha(p.color, alpha);
                this.ctx.beginPath();
                this.ctx.arc(pX, pY, p.size * alpha, 0, Math.PI * 2);
                this.ctx.fill();
            }
        });
    });
  }

  drawProjectile(projectile) {
    const x = projectile.x - this.camera.x;
    const y = projectile.y - this.camera.y;
    const angle = projectile.angle || 0;
    const size = projectile.size || 4;
    const trailLength = 15;
    const startX = x - Math.cos(angle) * trailLength;
    const startY = y - Math.sin(angle) * trailLength;
    const grad = this.ctx.createLinearGradient(startX, startY, x, y);
    grad.addColorStop(0, 'transparent');
    grad.addColorStop(1, projectile.color || '#FFFF00');
    this.ctx.strokeStyle = grad;
    this.ctx.lineWidth = size / 2;
    this.ctx.beginPath();
    this.ctx.moveTo(startX, startY);
    this.ctx.lineTo(x, y);
    this.ctx.stroke();
    this.ctx.fillStyle = projectile.color || '#FFFF00';
    this.ctx.beginPath();
    this.ctx.arc(x, y, size / 2, 0, Math.PI * 2);
    this.ctx.fill();
  }

  drawCoins() {
    if (!this.currencyIcon) {
        this.currencyIcon = new Image();
        this.currencyIcon.src = '/assets/SnakePit-Currency-Icon.png';
        this.currencyIcon.onload = () => this.currencyIconLoaded = true;
    }
    this.gameState.coins?.forEach(coin => {
      const x = coin.x - this.camera.x;
      const y = coin.y - this.camera.y;
      const size = coin.size || 10;
      const glow = this.ctx.createRadialGradient(x, y, 0, x, y, size * 2);
      glow.addColorStop(0, 'rgba(255, 215, 0, 0.7)');
      glow.addColorStop(1, 'transparent');
      this.ctx.fillStyle = glow;
      this.ctx.beginPath();
      this.ctx.arc(x, y, size * 2, 0, Math.PI * 2);
      this.ctx.fill();
      if (this.currencyIconLoaded && this.currencyIcon.complete) {
        this.ctx.drawImage(this.currencyIcon, x - size, y - size, size * 2, size * 2);
      } else {
        this.ctx.fillStyle = '#FFD700';
        this.ctx.beginPath();
        this.ctx.arc(x, y, size, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.fillStyle = '#000';
        this.ctx.font = `${size}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText('$', x, y);
      }
    });
  }

  drawKingIndicator() {
    if (!this.gameState.players?.length) return;
    const king = this.gameState.players.reduce((p, c) => c.cash > p.cash ? c : p);
    if (!king || king.id === this.playerId) return;

    // Compute screen position
    const kingScreenX = (king.x - this.camera.x) * this.camera.zoom;
    const kingScreenY = (king.y - this.camera.y) * this.camera.zoom;
    const isVisible = kingScreenX > 0 && kingScreenX < this.canvas.width && kingScreenY > 0 && kingScreenY < this.canvas.height;
    if (isVisible) return;

    // Angle from view center to king (in world space)
    const viewCenterX = this.camera.x + (this.canvas.width / 2) / this.camera.zoom;
    const viewCenterY = this.camera.y + (this.canvas.height / 2) / this.camera.zoom;
    const angle = Math.atan2(king.y - viewCenterY, king.x - viewCenterX);

    // Place indicator near screen edge
    const radius = Math.min(this.canvas.width, this.canvas.height) * 0.45;
    const indicatorX = this.canvas.width/2 + Math.cos(angle) * radius;
    const indicatorY = this.canvas.height/2 + Math.sin(angle) * radius;

    // Ensure crown image is loaded
    if (!this.crownImage) {
      this.crownImage = new Image();
      this.crownImage.src = '/assets/King_of_the_Pit_Crown_Icon.png';
      this.crownImage.onerror = () => { this.crownImageFailed = true; };
    }

    this.ctx.save();
    this.ctx.translate(indicatorX, indicatorY);
    this.ctx.rotate(angle);

    // Arrow (triangle) pointing towards the king - shorter as requested
    const pulse = 0.9 + 0.1 * Math.sin(Date.now() * 0.006);
    const arrowLen = 16 * pulse;       // shortened from 22
    const arrowBack = -8 * pulse;      // shortened from -10

    // Crown PNG behind the arrow (upright regardless of arrow rotation)
    const crownSize = 22 * pulse;      // slightly smaller to suit shorter arrow
    const crownOffsetY = -20;          // behind/above arrow tip

    this.ctx.save();                   // isolate rotation for upright crown
    this.ctx.rotate(-angle);           // cancel previous rotation so crown is upright

    if (this.crownImage && this.crownImage.complete && !this.crownImageFailed && this.crownImage.naturalWidth > 0) {
      this.ctx.shadowColor = 'rgba(255, 215, 0, 0.5)';
      this.ctx.shadowBlur = 8;
      this.ctx.drawImage(this.crownImage, -crownSize/2, crownOffsetY - crownSize, crownSize, crownSize);
      this.ctx.shadowColor = 'transparent';
      this.ctx.shadowBlur = 0;
    } else {
      // Fallback simple upright crown
      this.ctx.fillStyle = '#FFD700';
      this.ctx.fillRect(-crownSize/2, crownOffsetY - crownSize * 0.7, crownSize, crownSize * 0.2);
      this.ctx.beginPath();
      this.ctx.moveTo(-crownSize/2, crownOffsetY - crownSize * 0.7);
      this.ctx.lineTo(-crownSize/3, crownOffsetY - crownSize * 1.1);
      this.ctx.lineTo(0, crownOffsetY - crownSize * 0.9);
      this.ctx.lineTo(crownSize/3, crownOffsetY - crownSize * 1.1);
      this.ctx.lineTo(crownSize/2, crownOffsetY - crownSize * 0.7);
      this.ctx.closePath();
      this.ctx.fill();
    }
    this.ctx.restore();                // restore upright crown transform

    // Arrow outline (drawn after crown so arrow is on top)
    this.ctx.fillStyle = '#000';
    this.ctx.beginPath();
    this.ctx.moveTo(arrowLen + 2, 0);
    this.ctx.lineTo(arrowBack - 2, -7);
    this.ctx.lineTo(arrowBack - 2, 7);
    this.ctx.closePath();
    this.ctx.fill();

    // Arrow fill
    this.ctx.fillStyle = '#FFD700';
    this.ctx.beginPath();
    this.ctx.moveTo(arrowLen, 0);
    this.ctx.lineTo(arrowBack, -5);
    this.ctx.lineTo(arrowBack, 5);
    this.ctx.closePath();
    this.ctx.fill();

    this.ctx.restore();
  }

  calculateFPS(timestamp) {
    this.frameTimes.push(timestamp);
    const oneSecondAgo = timestamp - 1000;
    this.frameTimes = this.frameTimes.filter(t => t > oneSecondAgo);
    this.fps = this.frameTimes.length;
  }
  
  drawWeaponSwitchNotification() {
    if (!this.weaponSwitchNotification) return;
    const elapsed = Date.now() - this.weaponSwitchNotification.startTime;
    if (elapsed > this.weaponSwitchNotification.duration) {
      this.weaponSwitchNotification = null;
      return;
    }
    const fadeOutStart = this.weaponSwitchNotification.duration - 300;
    let alpha = 1;
    if (elapsed > fadeOutStart) {
        alpha = Math.max(0, 1 - (elapsed - fadeOutStart) / 300);
    }
    this.ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
    this.ctx.font = '16px Arial';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(this.weaponSwitchNotification.text, this.canvas.width / 2, 80);
  }

  drawMinimap() {
    if (!this.minimapCtx) return;
    const ctx = this.minimapCtx;
    const { width, height } = this.minimap;
    const scaleX = width / this.worldWidth;
    const scaleY = height / this.worldHeight;
    ctx.fillStyle = 'rgba(10, 10, 10, 0.8)';
    ctx.fillRect(0, 0, width, height);
    ctx.strokeStyle = '#444';
    ctx.strokeRect(0, 0, width, height);
    this.gameState.players?.forEach(p => {
      if (!p.alive) return;
      ctx.fillStyle = p.id === this.playerId ? '#4CAF50' : p.color;
      const size = Math.max(2, (p.size / 20));
      ctx.beginPath();
      ctx.arc(p.x * scaleX, p.y * scaleY, size, 0, Math.PI * 2);
      ctx.fill();
    });
    if (this.localPlayer) {
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
      ctx.lineWidth = 1;
      const viewX = this.camera.x * scaleX;
      const viewY = this.camera.y * scaleY;
      const viewW = (this.canvas.width / this.camera.zoom) * scaleX;
      const viewH = (this.canvas.height / this.camera.zoom) * scaleY;
      ctx.strokeRect(viewX, viewY, viewW, viewH);
    }
  }

  setMinimapCanvas(minimapCanvas) {
    if (minimapCanvas) {
      this.minimap = minimapCanvas;
      this.minimapCtx = minimapCanvas.getContext('2d');
      this.minimap.width = 250;
      this.minimap.height = 200;
      console.log('Minimap setup complete.');
    }
  }

  destroy() {
    console.log('Destroying ClientGame...');
    this.gameRunning = false;
    if (this.renderLoop) cancelAnimationFrame(this.renderLoop);
    if (this.connected) this.networkManager.disconnect();
    window.removeEventListener('resize', this.resizeCanvas);
    console.log('ClientGame destroyed');
  }
}

export default ClientGame;
