* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    color: white;
    overflow: hidden;
    cursor: none;
}

.menu-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #1e3c72, #2a5298);
    z-index: 100;
}

.menu-screen h1 {
    font-size: 48px;
    margin-bottom: 40px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.mode-buttons {
    display: flex;
    gap: 30px;
}

.mode-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 30px;
    width: 300px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.mode-btn h2 {
    color: white;
    margin-bottom: 15px;
    font-size: 24px;
}

.mode-btn p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    line-height: 1.4;
}

#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
}

#gameCanvas {
    display: block;
    background: #0a0a0a;
    border: 2px solid #333;
}

#ui {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#ui div {
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 15px;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    border: 1px solid #444;
}

#weapon, #cooldown {
    color: #FF9800;
}

.hidden {
    display: none !important;
}

#warfareInstructions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

#score {
    color: #4CAF50;
}

#length {
    color: #2196F3;
}

#boost {
    color: #FF9800;
}

#minimapContainer {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
    border: 2px solid #444;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.8);
}

#minimap {
    display: block;
    border-radius: 3px;
}

#instructions {
    position: absolute;
    bottom: 20px;
    left: 20px;
    z-index: 10;
    background: rgba(0, 0, 0, 0.8);
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #444;
    max-width: 250px;
}

#instructions h3 {
    margin-bottom: 10px;
    color: #4CAF50;
}

#instructions p {
    margin: 5px 0;
    font-size: 14px;
}

#gameOver {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 20;
    background: rgba(0, 0, 0, 0.9);
    padding: 30px;
    border-radius: 10px;
    border: 2px solid #444;
    text-align: center;
    min-width: 300px;
}

#gameOver h2 {
    color: #f44336;
    margin-bottom: 20px;
    font-size: 28px;
}

#gameOver p {
    margin: 10px 0;
    font-size: 18px;
}

#restartBtn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 20px;
    transition: background 0.3s;
}

#restartBtn:hover {
    background: #45a049;
}

.hidden {
    display: none !important;
}

/* Custom cursor for game area */
#gameCanvas {
    cursor: crosshair;
}

/* Responsive design */
@media (max-width: 768px) {
    #instructions {
        font-size: 12px;
        max-width: 200px;
    }

    #ui div {
        font-size: 14px;
        padding: 8px 12px;
    }

    #minimapContainer {
        top: 10px;
        right: 10px;
    }
}

