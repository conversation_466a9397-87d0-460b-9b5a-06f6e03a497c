import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { OBJLoader } from 'three-stdlib';

interface Avatar3DProps {
  className?: string;
  style?: React.CSSProperties;
}

const Avatar3D: React.FC<Avatar3DProps> = ({ className, style }) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const animationIdRef = useRef<number | null>(null);
  const modelRef = useRef<THREE.Group | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isDraggingRef = useRef(false);
  const previousMousePositionRef = useRef({ x: 0, y: 0 });

  useEffect(() => {
    if (!mountRef.current) return;

    const currentMount = mountRef.current;

    // Scene setup
    const scene = new THREE.Scene();
    // Transparent background to blend with UI
    scene.background = null;
    sceneRef.current = scene;

    // Ensure we have valid dimensions, fallback to minimum size if container is 0x0
    const width = currentMount.clientWidth || 400;
    const height = currentMount.clientHeight || 600;

    // Camera setup - zoomed in closer
    const camera = new THREE.PerspectiveCamera(
      60, // Slightly wider FOV to prevent cutoff
      width / height, // Use the fallback dimensions
      0.1,
      1000
    );
    camera.position.set(0, 0.3, 3); // Adjusted to see model at y: 0.6
    camera.lookAt(0, 0.6, 0); // Look at where the model is positioned

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    });
    renderer.setSize(width, height);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.outputColorSpace = THREE.SRGBColorSpace;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2;
    rendererRef.current = renderer;

    currentMount.appendChild(renderer.domElement);

    // Enhanced lighting setup with much more illumination
    const ambientLight = new THREE.AmbientLight(0x606060, 0.8); // Brighter ambient
    scene.add(ambientLight);

    // Main key light (stronger)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5);
    directionalLight.position.set(5, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    directionalLight.shadow.camera.near = 0.5;
    directionalLight.shadow.camera.far = 50;
    scene.add(directionalLight);

    // Orange hemisphere light positioned between model and camera
    const hemisphereLight = new THREE.HemisphereLight(0xff6600, 0x331100, 0.8);
    hemisphereLight.position.set(0, 1, 2);
    scene.add(hemisphereLight);

    // Additional fill lights for better illumination
    const fillLight1 = new THREE.DirectionalLight(0x6699ff, 0.6);
    fillLight1.position.set(-5, 8, 3);
    scene.add(fillLight1);

    const fillLight2 = new THREE.DirectionalLight(0xff9966, 0.5);
    fillLight2.position.set(3, 6, -4);
    scene.add(fillLight2);

    // Top light for better visibility
    const topLight = new THREE.DirectionalLight(0xffffff, 0.7);
    topLight.position.set(0, 15, 0);
    scene.add(topLight);

    // Side rim lights
    const rimLight1 = new THREE.DirectionalLight(0xffaa66, 0.4);
    rimLight1.position.set(-8, 4, 0);
    scene.add(rimLight1);

    const rimLight2 = new THREE.DirectionalLight(0xffaa66, 0.4);
    rimLight2.position.set(8, 4, 0);
    scene.add(rimLight2);

    // Create lava puddle
    const lavaPuddleGeometry = new THREE.CircleGeometry(2, 32);

    // Load lava texture and create material
    const lavaTextureLoader = new THREE.TextureLoader();
    const lavaTexture = lavaTextureLoader.load('/assets/avatars/CobraWarrior/lava.jpg');

    // Configure texture properties for better appearance
    lavaTexture.wrapS = THREE.RepeatWrapping;
    lavaTexture.wrapT = THREE.RepeatWrapping;
    lavaTexture.repeat.set(1, 1);

    // Create lava material with texture and animated properties
    const lavaMaterial = new THREE.MeshStandardMaterial({
      map: lavaTexture,
      emissive: 0x441100,
      emissiveIntensity: 0.8,
      roughness: 0.8,
      metalness: 0.2,
      transparent: true,
      opacity: 0.9
    });

    const lavaPuddle = new THREE.Mesh(lavaPuddleGeometry, lavaMaterial);
    lavaPuddle.rotation.x = -Math.PI / 1.8; // Tilt back (forward toward viewer)
    lavaPuddle.position.y = -1.3; // Position below the model
    lavaPuddle.position.z = -2; // Position behind the model
    lavaPuddle.receiveShadow = true;
    scene.add(lavaPuddle);

    // Store reference for animation
    const lavaPuddleRef = { current: lavaPuddle };

    // Bottom lighting effects (adjusted for model at y: 0.6)
    const bottomLight1 = new THREE.PointLight(0xff3300, 2, 8);
    bottomLight1.position.set(0, 0, 0); // Below the model
    scene.add(bottomLight1);

    const bottomLight2 = new THREE.PointLight(0xff6600, 1.5, 12);
    bottomLight2.position.set(0, 0.3, 0); // Slightly below model
    scene.add(bottomLight2);

    const bottomLight3 = new THREE.PointLight(0xff9900, 1, 15);
    bottomLight3.position.set(0, 0.6, 0); // At model level
    scene.add(bottomLight3);

    // Load the 3D model
    const loader = new OBJLoader();

    // First load the texture
    const textureLoader = new THREE.TextureLoader();
    textureLoader.load(
      '/assets/avatars/CobraWarrior/shaded.png',
      (texture) => {
        // Texture loaded successfully, now load the OBJ
        loader.load(
          '/assets/avatars/CobraWarrior/base.obj',
          (object: THREE.Group) => {
            // Apply the texture to all meshes in the object
            object.traverse((child: THREE.Object3D) => {
              if (child instanceof THREE.Mesh) {
                child.material = new THREE.MeshLambertMaterial({
                  map: texture
                });
                child.castShadow = true;
                child.receiveShadow = true;
              }
            });

            // Scale and position the model - larger and flipped
            object.scale.setScalar(2); // Moderate scale to fit in view
            object.position.y = -1.03; // Centered better
            object.rotation.y = -1; // Face forward (flipped from before)
            object.position.z=-1

            // Store model reference for rotation controls
            modelRef.current = object;

            scene.add(object);
            setIsLoading(false);

            // Animation variables
            let time = 0;

            // Animation loop
            const animate = () => {
              time += 0.01;

              // No floating animation - model stays static

              // Animate lava puddle
              if (lavaPuddleRef.current) {
                // Animate lava emissive intensity for pulsing glow effect
                const lavaIntensity = 0.8 + Math.sin(time * 2) * 0.4;
                lavaPuddleRef.current.material.emissiveIntensity = lavaIntensity;

                // Animate emissive color for glowing effect
                const emissiveVariation = 0.1 + Math.sin(time * 1.5) * 0.05;
                lavaPuddleRef.current.material.emissive.setRGB(
                  0.27 + emissiveVariation,
                  0.07 + emissiveVariation * 0.5,
                  0
                );
              }

              // Animate bottom lighting effects
              bottomLight1.intensity = 1.8 + Math.sin(time * 4) * 1.2;
              bottomLight2.intensity = 1.2 + Math.sin(time * 3) * 0.8;
              bottomLight3.intensity = 0.8 + Math.sin(time * 2) * 0.6;

              renderer.render(scene, camera);
              animationIdRef.current = requestAnimationFrame(animate);
            };

            animate();
          },
          (progress: ProgressEvent) => {
            console.log('Loading progress:', (progress.loaded / progress.total * 100) + '%');
          },
          (error: unknown) => {
            console.error('Error loading OBJ model:', error);
            setError('Failed to load 3D model');
            setIsLoading(false);
          }
        );
      },
      (progress: ProgressEvent) => {
        console.log('Texture loading progress:', (progress.loaded / progress.total * 100) + '%');
      },
      (error: unknown) => {
        console.error('Error loading texture:', error);
        setError('Failed to load texture');
        setIsLoading(false);
      }
    );

    // Handle window resize
    const handleResize = () => {
      if (!currentMount || !camera || !renderer) return;

      const width = currentMount.clientWidth;
      const height = currentMount.clientHeight;

      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);

    // Add mouse event listeners to the renderer canvas
    const canvas = renderer.domElement;
    canvas.style.cursor = 'grab';

    canvas.addEventListener('mousedown', (e) => {
      e.preventDefault();
      isDraggingRef.current = true;
      previousMousePositionRef.current = {
        x: e.clientX,
        y: e.clientY
      };
      canvas.style.cursor = 'grabbing';
    });

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (!isDraggingRef.current || !modelRef.current) return;

      const deltaMove = {
        x: e.clientX - previousMousePositionRef.current.x,
        y: e.clientY - previousMousePositionRef.current.y
      };

      // Only allow Y-axis rotation (horizontal mouse movement)
      modelRef.current.rotation.y += deltaMove.x * 0.01;

      previousMousePositionRef.current = {
        x: e.clientX,
        y: e.clientY
      };
    };

    const handleGlobalMouseUp = () => {
      isDraggingRef.current = false;
      canvas.style.cursor = 'grab';
    };

    window.addEventListener('mousemove', handleGlobalMouseMove);
    window.addEventListener('mouseup', handleGlobalMouseUp);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleGlobalMouseMove);
      window.removeEventListener('mouseup', handleGlobalMouseUp);

      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }

      if (currentMount && renderer.domElement) {
        currentMount.removeChild(renderer.domElement);
      }

      renderer.dispose();
      
      // Dispose of all materials and geometries
      scene.traverse((object) => {
        if (object instanceof THREE.Mesh) {
          object.geometry.dispose();
          if (object.material instanceof THREE.Material) {
            object.material.dispose();
          }
        }
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Remove isDragging from dependencies to prevent reloading

  return (
    <div
      ref={mountRef}
      className={className}
      style={{
        width: '100%',
        height: '100%',
        position: 'relative',
        ...style
      }}
    >

      {isLoading && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: '#ff6600',
          fontSize: '16px',
          fontWeight: 'bold'
        }}>
          Loading Avatar...
        </div>
      )}
      {error && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: '#ff0000',
          fontSize: '14px',
          textAlign: 'center'
        }}>
          {error}
        </div>
      )}
    </div>
  );
};

export default Avatar3D;
