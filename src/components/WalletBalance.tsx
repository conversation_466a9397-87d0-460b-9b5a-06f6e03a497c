import React from 'react'
import { useWalletBalance } from '../hooks/useWalletBalance'
import { useSolanaPrice } from '../hooks/useSolanaPrice'

interface WalletBalanceProps {
  autoRefresh?: boolean
  refreshInterval?: number
  showRefreshButton?: boolean
  showFaucetLink?: boolean
  enableRealTimeUpdates?: boolean
  showMonitoringStatus?: boolean
  className?: string
}

const WalletBalance: React.FC<WalletBalanceProps> = ({
  autoRefresh = false,
  refreshInterval = 30000,
  showRefreshButton = true,
  showFaucetLink = true,
  enableRealTimeUpdates = true,
  showMonitoringStatus = false,
  className = ''
}) => {
  const { 
    balance, 
    loading, 
    error, 
    refreshBalance, 
    lastUpdated, 
    isMonitoring 
  } = useWalletBalance({
    autoRefresh,
    refreshInterval,
    syncWithDatabase: true,
    enableRealTimeUpdates
  })

  const {
    solPrice,
    loading: priceLoading,
    error: priceError,
    convertSolToUsd,
    formatUsdAmount
  } = useSolanaPrice({
    autoRefresh: true,
    refreshInterval: 60000 // Update price every minute
  })

  const containerStyle: React.CSSProperties = {
    padding: '12px',
    border: '1px solid #e0e0e0',
    borderRadius: '8px',
    background: '#f9f9f9',
    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif'
  }

  const balanceDisplayStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '8px',
    gap: '8px'
  }

  const balanceLabelStyle: React.CSSProperties = {
    fontWeight: 600,
    color: '#333'
  }

  const balanceValueStyle: React.CSSProperties = {
    fontFamily: 'Courier New, monospace',
    fontWeight: 'bold',
    color: error ? '#d32f2f' : '#2e7d32',
    flex: 1,
    textAlign: 'right',
    opacity: loading ? 0.7 : 1
  }

  const liveIndicatorStyle: React.CSSProperties = {
    fontSize: '0.75em',
    color: '#4caf50',
    fontWeight: 'normal',
    whiteSpace: 'nowrap'
  }

  const lastUpdatedStyle: React.CSSProperties = {
    fontSize: '0.8em',
    color: '#666',
    marginBottom: '8px',
    fontStyle: autoRefresh ? 'italic' : 'normal'
  }

  const monitoringStatusStyle: React.CSSProperties = {
    fontSize: '0.8em',
    marginBottom: '8px',
    padding: '4px 8px',
    borderRadius: '4px',
    background: '#f0f0f0',
    color: isMonitoring ? '#2e7d32' : '#f57c00'
  }

  const errorMessageStyle: React.CSSProperties = {
    fontSize: '0.85em',
    color: '#d32f2f',
    background: '#ffebee',
    padding: '4px 8px',
    borderRadius: '4px',
    marginBottom: '8px'
  }

  const balanceActionsStyle: React.CSSProperties = {
    display: 'flex',
    gap: '8px',
    flexWrap: 'wrap'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '6px 12px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    background: 'white',
    color: '#333',
    textDecoration: 'none',
    fontSize: '0.85em',
    cursor: loading ? 'not-allowed' : 'pointer',
    transition: 'all 0.2s ease',
    opacity: loading ? 0.6 : 1
  }

  const faucetLinkStyle: React.CSSProperties = {
    ...buttonStyle,
    background: '#e3f2fd',
    borderColor: '#2196f3',
    color: '#1976d2'
  }

  return (
    <div style={containerStyle}>
      <div style={balanceDisplayStyle}>
        <span style={balanceLabelStyle} className="sol-balance-label">
          <img 
                src="/assets/solana-icon.png" 
                alt="Solana" 
                style={{ width: '50px', height: '50px', marginRight: '6px', verticalAlign: 'middle', border: '2px solid transparent', background: 'linear-gradient(45deg, #9945FF, #14F195, #00D4AA, #9945FF) border-box', backgroundClip: 'padding-box, border-box', backgroundOrigin: 'padding-box, border-box', borderRadius: '8px' }}
              />
          SOL Balance:
        </span>
        <div style={{ ...balanceValueStyle, display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
          <span>
            {loading ? 'Loading...' : error ? 'Error' : balance !== null ? `${balance.toFixed(6)} SOL` : 'N/A'}
            {enableRealTimeUpdates && !loading && !error && (
              <span style={liveIndicatorStyle}> (Live)</span>
            )}
          </span>
          {balance !== null && !loading && !error && (
            <span style={{ fontSize: '0.85em', color: '#666', marginTop: '2px' }}>
              {priceLoading ? 'Loading USD...' : 
               priceError ? 'Price unavailable' :
               solPrice !== null ? formatUsdAmount(convertSolToUsd(balance) || 0) : 'Price unavailable'}
            </span>
          )}
        </div>
      </div>

      {lastUpdated && !autoRefresh && (
        <div style={lastUpdatedStyle}>
          Last updated: {lastUpdated.toLocaleTimeString()}
        </div>
      )}

      {autoRefresh && (
        <div style={lastUpdatedStyle}>
          Auto-updating every {Math.round(refreshInterval / 1000)}s
        </div>
      )}

      {showMonitoringStatus && (
        <div style={monitoringStatusStyle}>
          {isMonitoring ? <><span className="icon-connected"></span> Real-time monitoring active</> : <><span className="icon-warning"></span> Real-time monitoring inactive</>}
        </div>
      )}

      {error && (
        <div style={errorMessageStyle}>
          {error}
        </div>
      )}

      <div style={balanceActionsStyle}>
        {showRefreshButton && (
          <button 
            style={buttonStyle}
            onClick={refreshBalance}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : <><span className="icon-refresh"></span> Refresh</>}
          </button>
        )}
        
        {showFaucetLink && (
          <a 
            style={faucetLinkStyle}
            href="https://faucet.solana.com"
            target="_blank"
            rel="noopener noreferrer"
          >
            <span className="icon-drop"></span> Get Test SOL
          </a>
        )}
      </div>

    </div>
  )
}

export default WalletBalance