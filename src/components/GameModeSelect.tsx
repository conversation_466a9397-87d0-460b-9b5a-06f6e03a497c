import React, { useState, useEffect } from 'react';
import { GameMode } from '../App';
import AudioManager from '../utils/AudioManager';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from './AuthModal';
import Leaderboard from './Leaderboard';
import UserProfile from './UserProfile';
import { GameStatsService } from '../services/GameStatsService';
import { Settings } from './Settings';
import { Help } from './Help';
import { Glossary } from './Glossary';
import { About } from './About';
import { CryptoManager } from './CryptoManager';
import StartScreen from './StartScreen';
import GameModeSelectLobby from './GameModeSelectLobby';



interface GameModeSelectProps {
  onModeSelect: (mode: GameMode, userData: UserData) => void;
}

interface UserData {
  username: string;
  wager: number;
  solanaBalance: number;
  userId?: string;
}

const GameModeSelect: React.FC<GameModeSelectProps> = ({ onModeSelect }) => {
  // Auth context
  const { user, userProfile, updateProfile, loading: authLoading } = useAuth();

  // Debug logging for auth state
  useEffect(() => {
    console.log('🎮 GameModeSelect: Auth state update')
    console.log('  User:', user ? `✅ ${user.id}` : '❌ None')
    console.log('  Profile:', userProfile ? `✅ ${userProfile.username}` : '❌ None')
    console.log('  Loading:', authLoading)
  }, [user, userProfile, authLoading]);

  // Multi-stage state management
  const [currentStage, setCurrentStage] = useState<'start-screen' | 'lobby'>('start-screen');

  // State for lobby
  const [userData, setUserData] = useState<UserData>({
    username: '',
    wager: 50, // Default $50 USD wager
    solanaBalance: 0.1
  });

  // Modal states
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [showGlossary, setShowGlossary] = useState(false);
  const [showAbout, setShowAbout] = useState(false);
  const [showCryptoManager, setShowCryptoManager] = useState(false);

  // Audio control state
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState(0.6);

  // Selected mode state - default to classic
  const [selectedMode, setSelectedMode] = useState<GameMode | null>('classic');

  // Update userData when userProfile changes
  useEffect(() => {
    if (userProfile) {
      setUserData({
        username: userProfile.username,
        wager: 50, // Default $50 USD wager
        solanaBalance: userProfile.solana_balance
      });
      // Update audio settings from profile
      setIsMuted(userProfile.audio_muted);
      setVolume(userProfile.audio_volume);
    }
  }, [userProfile]);

  // Add/remove background class based on current stage
  useEffect(() => {
    const appElement = document.querySelector('.snakepit-app');
    if (appElement) {
      if (currentStage === 'start-screen') {
        appElement.classList.add('start-screen-bg');
      } else {
        appElement.classList.remove('start-screen-bg');
      }
    }

    // Cleanup on unmount
    return () => {
      const appElement = document.querySelector('.snakepit-app');
      if (appElement) {
        appElement.classList.remove('start-screen-bg');
      }
    };
  }, [currentStage]);

  // Handler functions
  const handlePlayNowClick = () => {
    // Check if user is authenticated
    if (!user) {
      console.log('🔐 User not authenticated, showing auth modal');
      setShowAuthModal(true);
      return;
    }

    // Check if user profile is loaded
    if (!userProfile) {
      console.log('⏳ User profile loading, please wait...');
      return;
    }

    console.log('✅ User authenticated, proceeding to lobby');
    setCurrentStage('lobby');
  };

  const handleBackToStartScreen = () => {
    setCurrentStage('start-screen');
  };



  const handleEnterBattle = async () => {
    if (!user || !userProfile) {
      setShowAuthModal(true);
      return;
    }

    if (userData.username.trim() === '') {
      alert('Please enter a username');
      return;
    }

    if (!selectedMode) {
      alert('Please select a game mode');
      return;
    }

    try {
      console.log('🎮 Validating game entry with wager:', userData.wager);

      // Validate game entry with server-side balance check
      const serverUrl = process.env.REACT_APP_SERVER_URL || 'http://localhost:3005';

      const validationResponse = await fetch(`${serverUrl}/api/solana/validate-game-entry`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          wagerAmount: userData.wager,
          gameMode: selectedMode
        })
      });

      const validationResult = await validationResponse.json();

      if (!validationResult.success || !validationResult.canJoin) {
        alert(`Cannot join game: ${validationResult.message || validationResult.error}`);
        return;
      }

      console.log('✅ Game entry validated:', validationResult);

      // Use secure wager function to transfer funds to room escrow
      const wagerResult = await GameStatsService.startGame(selectedMode as 'classic' | 'warfare' | 'speed', userData.wager, user.id);

      if (!wagerResult.success) {
        alert(`Failed to start game: ${wagerResult.error}`);
        return;
      }

      console.log('🎮 Wager transferred to escrow successfully:', wagerResult);

      // Update local userData with new balance from validation
      setUserData(prev => ({
        ...prev,
        solanaBalance: validationResult.balanceSol || validationResult.user_balance_sol || prev.solanaBalance
      }));

      // Trigger profile refresh to update the UI
      window.dispatchEvent(new CustomEvent('profileRefresh'));

      // Transition to game music
      const audioManager = AudioManager.getInstance();
      await audioManager.playTrack('game', {
        volume: 0.5,
        loop: true,
        fadeInDuration: 1500,
        fadeOutDuration: 1000
      });
      console.log('Transitioned to game music');

      // Proceed to game with updated userData including userId
      onModeSelect(selectedMode, {
        ...userData,
        solanaBalance: wagerResult.new_balance || userData.solanaBalance,
        userId: user.id // Pass userId for Solana integration
      });
    } catch (error) {
      console.error('Failed to start game:', error);
      alert('Failed to start game. Please try again.');
    }
  };

  // Audio control handlers
  const handleToggleMute = async () => {
    const audioManager = AudioManager.getInstance();
    const newMutedState = audioManager.toggleMute();
    setIsMuted(newMutedState);

    // Save to profile if user is logged in
    if (user) {
      await updateProfile({ audio_muted: newMutedState });
    }
  };

  const handleVolumeChange = async (newVolume: number) => {
    const audioManager = AudioManager.getInstance();
    audioManager.setMasterVolume(newVolume);
    setVolume(newVolume);

    // Save to profile if user is logged in
    if (user) {
      await updateProfile({ audio_volume: newVolume });
    }
  };

  const handleAuthSuccess = () => {
    console.log('🎉 Authentication successful!');
    setShowAuthModal(false);

    // Small delay to let the user see the success state
    setTimeout(() => {
      // If user was trying to play, proceed to lobby
      if (currentStage === 'start-screen') {
        console.log('🚀 Proceeding to lobby...');
        setCurrentStage('lobby');
      }
    }, 500);
  };

  return (
    <div className={currentStage === 'start-screen' ? 'apple-start-screen' : 'fortnite-lobby'}>
      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={handleAuthSuccess}
      />

      {/* Profile Modal */}
      <UserProfile
        isOpen={showProfile}
        onClose={() => setShowProfile(false)}
      />

      {/* Leaderboard Modal */}
      <Leaderboard
        isOpen={showLeaderboard}
        onClose={() => setShowLeaderboard(false)}
      />

      {/* Settings Modal */}
      <Settings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />

      {/* Help Modal */}
      <Help
        isOpen={showHelp}
        onClose={() => setShowHelp(false)}
      />

      {/* Glossary Modal */}
      <Glossary
        isOpen={showGlossary}
        onClose={() => setShowGlossary(false)}
      />

      {/* About Modal */}
      <About
        isOpen={showAbout}
        onClose={() => setShowAbout(false)}
      />

      {/* Crypto Manager Modal */}
      <CryptoManager
        isOpen={showCryptoManager}
        onClose={() => setShowCryptoManager(false)}
      />

      {currentStage === 'start-screen' ? (
        <StartScreen
          onPlayNowClick={handlePlayNowClick}
          onShowProfile={() => setShowProfile(true)}
          onShowAuthModal={() => setShowAuthModal(true)}
          isMuted={isMuted}
          volume={volume}
          onToggleMute={handleToggleMute}
          onVolumeChange={handleVolumeChange}
        />
      ) : (
        <GameModeSelectLobby
          onModeSelect={onModeSelect}
          onBackToStartScreen={handleBackToStartScreen}
          onShowProfile={() => setShowProfile(true)}
          onShowSettings={() => setShowSettings(true)}
          onShowLeaderboard={() => setShowLeaderboard(true)}
          onShowHelp={() => setShowHelp(true)}
          userData={userData}
          setUserData={setUserData}
          selectedMode={selectedMode}
          setSelectedMode={setSelectedMode}
          onEnterBattle={handleEnterBattle}
        />
      )}
    </div>
  );
};
export default GameModeSelect;
