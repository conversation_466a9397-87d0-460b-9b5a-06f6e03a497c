import React from 'react';

interface GameOverProps {
  finalScore: number;
  finalLength: number;
  onRestart: () => void;
  onBackToMenu: () => void;
}

// Cute skull icon component with pink glow
const CuteSkull: React.FC = () => (
  <span className="cute-skull" role="img" aria-label="skull">
    💀
  </span>
);

const GameOver: React.FC<GameOverProps> = ({
  finalScore,
  finalLength,
  onRestart,
  onBackToMenu
}) => {
  return (
    <div className="game-over">
      <div className="game-over-content">
        <h2>
          <CuteSkull /> WASTED! <CuteSkull />
        </h2>
        <div className="final-stats">
          <div className="final-stat">
            <i className="icon-dollar"></i> Final Cash: ${finalScore.toLocaleString()}
          </div>
          <div className="final-stat">
            <i className="icon-snake"></i> Final Length: {finalLength}
          </div>
        </div>
        <div className="game-over-buttons">
          <button className="retro-button" onClick={onRestart}>
            <i className="icon-refresh"></i> Try Again
          </button>
          <button className="retro-button secondary" onClick={onBackToMenu}>
            <i className="icon-home"></i> Exit Pit
          </button>
        </div>
      </div>
    </div>
  );
};

export default GameOver;