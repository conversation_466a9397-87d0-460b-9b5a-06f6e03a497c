import React, { useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { User, Lock, VolumeX, Volume2, Gamepad2 } from 'lucide-react';
import { useSolanaPrice } from '../hooks/useSolanaPrice';

interface LogoSnake {
  segments: { x: number; y: number }[];
  color: string;
  size: number;
  speed: number;
  angle: number;
  pathProgress: number; // 0-1 around the border
  direction: 1 | -1; // 1 for clockwise, -1 for counter-clockwise
}

interface StartScreenProps {
  onPlayNowClick: () => void;
  onShowProfile: () => void;
  onShowAuthModal: () => void;
  isMuted: boolean;
  volume: number;
  onToggleMute: () => void;
  onVolumeChange: (volume: number) => void;
}

const StartScreen: React.FC<StartScreenProps> = ({
  onPlayNowClick,
  onShowProfile,
  onShowAuthModal,
  isMuted,
  volume,
  onToggleMute,
  onVolumeChange
}) => {
  const logoSnakesCanvasRef = useRef<HTMLCanvasElement>(null);
  const logoSnakesAnimationRef = useRef<number | undefined>(undefined);
  const logoSnakesRef = useRef<LogoSnake[]>([]);

  const { user, userProfile, loading: authLoading } = useAuth();
  const { solPrice, convertSolToUsd, formatUsdAmount } = useSolanaPrice({ autoRefresh: true });

  // Logo Snakes Animation
  useEffect(() => {
    const canvas = logoSnakesCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Performance optimizations for logo snakes
    let lastLogoFrameTime = 0;
    const logoTargetFPS = 30; // Limit to 30 FPS for better performance
    const logoFrameInterval = 1000 / logoTargetFPS;

    // Set canvas size to match container
    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width;
      canvas.height = rect.height;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Create three snakes
    const createLogoSnake = (color: string, direction: 1 | -1, startProgress: number): LogoSnake => {
      const snake: LogoSnake = {
        segments: [],
        color,
        size: 16, // Increased from 12 to 16 for bigger snakes
        speed: 0.003, // Slow movement around border
        angle: 0,
        pathProgress: startProgress,
        direction
      };

      // Initialize with 8 segments for longer snakes
      for (let i = 0; i < 8; i++) {
        snake.segments.push({ x: 0, y: 0 });
      }

      return snake;
    };

    logoSnakesRef.current = [
      createLogoSnake('#00ff41', 1, 0), // Green snake, clockwise, start at top-left
      createLogoSnake('#00ffff', 1, 0.33), // Cyan snake, also clockwise, start at 1/3 around
      createLogoSnake('#ff0080', 1, 0.66) // Hot pink snake, also clockwise, start at 2/3 around
    ];

    // Get position on border path
    const getBorderPosition = (progress: number, canvasWidth: number, canvasHeight: number) => {
      const margin = 20; // Distance from edge
      const w = canvasWidth - margin * 2;
      const h = (canvasHeight - margin * 2) * 0.85; // Reduce height by 15% (less compression)
      const perimeter = (w + h) * 2;

      // Normalize progress to 0-1
      progress = ((progress % 1) + 1) % 1;

      const distance = progress * perimeter;

      if (distance < w) {
        // Top edge
        return {
          x: margin + distance,
          y: margin,
          angle: 0
        };
      } else if (distance < w + h) {
        // Right edge
        return {
          x: margin + w,
          y: margin + (distance - w),
          angle: Math.PI / 2
        };
      } else if (distance < w * 2 + h) {
        // Bottom edge
        return {
          x: margin + w - (distance - w - h),
          y: margin + h,
          angle: Math.PI
        };
      } else {
        // Left edge
        return {
          x: margin,
          y: margin + h - (distance - w * 2 - h),
          angle: -Math.PI / 2
        };
      }
    };

    // Draw game-style snake
    const drawGameSnake = (snake: LogoSnake) => {
      if (snake.segments.length < 2) return;

      // Draw unified outline (like in game)
      ctx.strokeStyle = snake.color;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      ctx.lineWidth = snake.size * 2;

      // Draw body outline
      ctx.beginPath();
      for (let i = 1; i < snake.segments.length; i++) {
        const segment = snake.segments[i];
        if (i === 1) {
          ctx.moveTo(segment.x, segment.y);
        } else {
          ctx.lineTo(segment.x, segment.y);
        }
      }
      ctx.stroke();

      // Draw head outline (wider)
      const head = snake.segments[0];
      ctx.lineWidth = snake.size * 2.8;
      ctx.beginPath();
      if (snake.segments.length > 1) {
        const neck = snake.segments[1];
        ctx.moveTo(neck.x, neck.y);
        ctx.lineTo(head.x, head.y);
      }
      ctx.stroke();

      // Draw black interior
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = snake.size * 1.6;
      ctx.beginPath();
      for (let i = 1; i < snake.segments.length; i++) {
        const segment = snake.segments[i];
        if (i === 1) {
          ctx.moveTo(segment.x, segment.y);
        } else {
          ctx.lineTo(segment.x, segment.y);
        }
      }
      ctx.stroke();

      // Draw head interior
      ctx.lineWidth = snake.size * 2.4;
      ctx.beginPath();
      if (snake.segments.length > 1) {
        const neck = snake.segments[1];
        ctx.moveTo(neck.x, neck.y);
        ctx.lineTo(head.x, head.y);
      }
      ctx.stroke();

      // Draw center dots
      for (let i = 0; i < snake.segments.length; i++) {
        const segment = snake.segments[i];
        const dotSize = i === 0 ? snake.size * 0.25 : snake.size * 0.15;

        ctx.fillStyle = snake.color;
        ctx.beginPath();
        ctx.arc(segment.x, segment.y, dotSize, 0, Math.PI * 2);
        ctx.fill();
      }

      // Draw eyes on head
      const eyeSize = snake.size * 0.15;
      const eyeDistance = snake.size * 0.4;
      const eyeAngle = snake.angle + Math.PI / 2;

      const leftEyeX = head.x + Math.cos(eyeAngle) * eyeDistance;
      const leftEyeY = head.y + Math.sin(eyeAngle) * eyeDistance;
      const rightEyeX = head.x - Math.cos(eyeAngle) * eyeDistance;
      const rightEyeY = head.y - Math.sin(eyeAngle) * eyeDistance;

      // Cyan eyes
      ctx.fillStyle = '#00ffff';
      ctx.beginPath();
      ctx.arc(leftEyeX, leftEyeY, eyeSize, 0, Math.PI * 2);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(rightEyeX, rightEyeY, eyeSize, 0, Math.PI * 2);
      ctx.fill();

      // Red pupils
      ctx.fillStyle = '#ff0000';
      ctx.beginPath();
      ctx.arc(leftEyeX, leftEyeY, eyeSize * 0.5, 0, Math.PI * 2);
      ctx.fill();
      ctx.beginPath();
      ctx.arc(rightEyeX, rightEyeY, eyeSize * 0.5, 0, Math.PI * 2);
      ctx.fill();
    };

    const animateLogoSnakes = (currentTime: number) => {
      // Frame rate limiting for better performance
      if (currentTime - lastLogoFrameTime < logoFrameInterval) {
        logoSnakesAnimationRef.current = requestAnimationFrame(animateLogoSnakes);
        return;
      }
      lastLogoFrameTime = currentTime;

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      logoSnakesRef.current.forEach(snake => {
        // Update snake position
        snake.pathProgress += snake.speed * snake.direction;

        // Get head position
        const headPos = getBorderPosition(snake.pathProgress, canvas.width, canvas.height);
        snake.angle = headPos.angle;
        snake.segments[0] = { x: headPos.x, y: headPos.y };

        // Update body segments to follow head
        for (let i = 1; i < snake.segments.length; i++) {
          const segmentDistance = snake.size * 1.5;
          const targetProgress = snake.pathProgress - (i * segmentDistance) / ((canvas.width + canvas.height) * 2) * snake.direction;
          const segmentPos = getBorderPosition(targetProgress, canvas.width, canvas.height);

          // Smooth interpolation
          const current = snake.segments[i];
          const dx = segmentPos.x - current.x;
          const dy = segmentPos.y - current.y;
          current.x += dx * 0.3;
          current.y += dy * 0.3;
        }

        drawGameSnake(snake);
      });

      logoSnakesAnimationRef.current = requestAnimationFrame(animateLogoSnakes);
    };

    animateLogoSnakes(0); // Start with initial time of 0

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (logoSnakesAnimationRef.current) {
        cancelAnimationFrame(logoSnakesAnimationRef.current);
      }
    };
  }, []);

  return (
    <div className="landing-content">
      {/* User Status Indicator - Top Right */}
      <div className="top-user-status">
        {user && userProfile ? (
          <button
            onClick={onShowProfile}
            className="user-status"
            title="View Profile"
          >
            <User size={16} /> {userProfile.username} | <img src="/assets/solana-icon.png" alt="Solana" style={{ width: '20px', height: '20px', marginLeft: '4px', marginRight: '4px', verticalAlign: 'middle', borderRadius: '4px' }} /> {userProfile.solana_balance.toFixed(2)}
            {solPrice && (
              <span style={{ fontSize: '0.8em', marginLeft: '4px', opacity: 0.7 }}>
                ({formatUsdAmount(convertSolToUsd(userProfile.solana_balance) || 0)})
              </span>
            )}
          </button>
        ) : (
          <button
            onClick={onShowAuthModal}
            className="login-button"
          >
            <Lock size={16} /> Sign In
          </button>
        )}

        {/* Audio Controls */}
        <div className="audio-controls">
          <button
            className="audio-btn"
            onClick={onToggleMute}
            title={isMuted ? 'Unmute' : 'Mute'}
          >
            {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
          </button>
          <div className="volume-control">
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => onVolumeChange(parseFloat(e.target.value))}
              className="volume-slider"
              title="Volume"
            />
          </div>
        </div>
      </div>

      <div className="landing-container">
        {/* Main Hero Section */}
        <div className="hero-section">
          <div className="snakepit-logo">
            {/* Game-style Animated Snakes around the border */}
            <canvas
              ref={logoSnakesCanvasRef}
              className="logo-snakes-canvas"
              width="800"
              height="400"
            />

            <pre className="ascii-logo neon-text neon-green">
{`
╔══════════════════════════════════════════════════════════════════════╗
║  ~~o                                                            o~~  ║
║                                                                      ║
║      ██████  ███    ██  █████  ██   ██ ███████ ██████  ██ ████████   ║
║     ██       ████   ██ ██   ██ ██  ██  ██      ██   ██ ██    ██      ║
║     ███████  ██ ██  ██ ███████ █████   █████   █████   ██    ██      ║
║          ██  ██  ██ ██ ██   ██ ██  ██  ██      ██      ██    ██      ║
║     ███████  ██   ████ ██   ██ ██   ██ ███████ ██      ██    ██      ║
║                                                                      ║
║  ~~o                                                            o~~  ║
╚══════════════════════════════════════════════════════════════════════╝
`}
            </pre>
          </div>

          <div className="hero-content">
            <h1 className="hero-title">Enter the Ultimate Snake Arena</h1>
            <p className="hero-description">
              Be the best. Win some cash.
            </p>

            <div className="cta-section">
              <button
                className="play-now-button"
                onClick={onPlayNowClick}
                disabled={authLoading}
              >
                {authLoading ? '⏳ Loading...' :
                 !user ? <><Lock size={16} /> START PLAYING</> :
                 <><Gamepad2 size={16} /> START PLAYING </>}
              </button>
              <p className="cta-subtitle">
                {!user ? 'Sign up or login to play • Real money rewards' :
                 'Pay to play • No downloads required'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StartScreen;
