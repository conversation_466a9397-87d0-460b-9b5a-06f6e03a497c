import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase, addTransaction, updateUserProfile } from '../lib/supabase';
import { getWalletBalance } from '../utils/SolanaWallet';
import { useSolanaPrice } from '../hooks/useSolanaPrice';
import { 
  Zap, X, BarChart3, CreditCard, Banknote, FileText, 
  Copy, RefreshCw, DollarSign
} from 'lucide-react';
import '../snakepit-theme.css';

interface CryptoManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CryptoManager: React.FC<CryptoManagerProps> = ({ isOpen, onClose }) => {
  const { user, userProfile, refreshProfile } = useAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'deposit' | 'withdraw' | 'history'>('overview');
  const [depositAmount, setDepositAmount] = useState('');
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawAddress, setWithdrawAddress] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [message, setMessage] = useState('');
  const [userWallet, setUserWallet] = useState<{ public_key: string } | null>(null);
  
  const {
    solPrice,
    loading: priceLoading,
    error: priceError,
    convertSolToUsd,
    formatUsdAmount,
    lastUpdated
  } = useSolanaPrice({ autoRefresh: true });

  useEffect(() => {
    const fetchUserWallet = async () => {
      if (!user) return;
      
      try {
        const { data, error } = await supabase
          .from('solana_wallets')
          .select('public_key')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .single();
        
        if (data && !error) {
          setUserWallet(data);
        }
      } catch (error) {
        console.error('Error fetching user wallet:', error);
      }
    };

    if (isOpen) {
      fetchUserWallet();
    }
  }, [user, isOpen]);

  const refreshBalance = async () => {
    if (!userWallet || !user) return;

    setIsRefreshing(true);
    try {
      const balance = await getWalletBalance(userWallet.public_key);
      await updateUserProfile(user.id, { solana_balance: balance });
      await refreshProfile();
      setMessage('Balance refreshed successfully!');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('Error refreshing balance:', error);
      setMessage('Error refreshing balance. Please try again.');
      setTimeout(() => setMessage(''), 5000);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleDeposit = async () => {
    if (!user || !userProfile || !userWallet) return;

    setIsProcessing(true);
    setMessage('');

    try {
      // For deposits, we refresh the balance since users fund externally
      await refreshBalance();
      
      // Add transaction record for tracking if amount was specified
      if (depositAmount) {
        await addTransaction({
          user_id: user.id,
          type: 'deposit',
          amount: parseFloat(depositAmount),
          description: `Manual deposit check for ${depositAmount} SOL`,
          status: 'confirmed'
        });
      }
      
      setDepositAmount('');
      setMessage('Balance refreshed! If you just sent SOL, it should appear now.');
      setTimeout(() => setMessage(''), 5000);
    } catch (error) {
      console.error('Deposit error:', error);
      setMessage('Error refreshing balance. Please try again.');
      setTimeout(() => setMessage(''), 5000);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleWithdraw = async () => {
    if (!user || !userProfile || !withdrawAmount || !withdrawAddress) return;

    const amount = parseFloat(withdrawAmount);
    if (amount <= 0 || amount > userProfile.solana_balance) {
      setMessage(`Please enter a valid amount between 0.01 and ${userProfile.solana_balance} SOL`);
      return;
    }

    setIsProcessing(true);
    setMessage('');

    try {
      // For withdrawals, users would need to provide a destination address
      // This is a simplified version - in production you'd want a proper withdrawal flow
      setMessage('Withdrawal functionality coming soon! For now, contact support to withdraw funds.');
      setWithdrawAmount('');
      setWithdrawAddress('');
      setTimeout(() => setMessage(''), 5000);
    } catch (error) {
      console.error('Withdrawal error:', error);
      setMessage('Error processing withdrawal. Please try again.');
      setTimeout(() => setMessage(''), 5000);
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-backdrop" onClick={onClose}></div>
      <div className="modal-content crypto-manager-modal">
        <div className="modal-header">
          <h2 className="modal-title neon-text neon-yellow"><Zap size={20} /> Solana Wallet</h2>
        <button className="modal-close" onClick={onClose}><X size={20} /></button>
        </div>

        <div className="modal-body">
          {/* Tab Navigation */}
          <div className="crypto-tabs">
            <button
              className={`crypto-tab ${activeTab === 'overview' ? 'active' : ''}`}
              onClick={() => setActiveTab('overview')}
            >
              <BarChart3 size={16} /> Overview
            </button>
            <button
              className={`crypto-tab ${activeTab === 'deposit' ? 'active' : ''}`}
              onClick={() => setActiveTab('deposit')}
            >
              <CreditCard size={16} /> Deposit
            </button>
            <button
              className={`crypto-tab ${activeTab === 'withdraw' ? 'active' : ''}`}
              onClick={() => setActiveTab('withdraw')}
            >
              <Banknote size={16} /> Withdraw
            </button>
            <button
              className={`crypto-tab ${activeTab === 'history' ? 'active' : ''}`}
              onClick={() => setActiveTab('history')}
            >
              <FileText size={16} /> History
            </button>
          </div>

          {/* Tab Content */}
          <div className="crypto-content">
            {activeTab === 'overview' && (
              <div className="overview-section">
                <div className="balance-display">
                  <h3 className="balance-title neon-text neon-cyan">Current Balance</h3>
                  <div className="balance-amount neon-text neon-yellow">
                    <img 
                      src="/assets/solana-icon.png" 
                      alt="Solana" 
                      style={{ width: '50px', height: '50px', marginRight: '6px', verticalAlign: 'middle', border: '2px solid transparent', background: 'linear-gradient(45deg, #9945FF, #14F195, #00D4AA, #9945FF) border-box', backgroundClip: 'padding-box, border-box', backgroundOrigin: 'padding-box, border-box', borderRadius: '8px' }}
                    />
                    {userProfile?.solana_balance.toFixed(6)} SOL
                  </div>
                  <div className="balance-usd">
                    {solPrice && userProfile?.solana_balance ? (
                      <>≈ {formatUsdAmount(convertSolToUsd(userProfile.solana_balance) || 0)}</>
                    ) : priceLoading ? (
                      <span style={{ color: '#888' }}>Loading price...</span>
                    ) : priceError ? (
                      <span style={{ color: '#ff6b6b' }}>Price unavailable</span>
                    ) : (
                      <span style={{ color: '#888' }}>≈ $0.00</span>
                    )}
                  </div>
                  <button
                    className="refresh-btn neon-button neon-dim"
                    onClick={refreshBalance}
                    disabled={isRefreshing || !userWallet}
                  >
                    {isRefreshing ? <><RefreshCw size={16} /> Refreshing...</> : <><RefreshCw size={16} /> Refresh Balance</>}
                  </button>
                </div>
                
                {/* Solana Price Information */}
                <div className="price-info">
                  <h4 className="section-subtitle neon-text neon-cyan">Solana Price</h4>
                  <div className="price-display">
                    {solPrice ? (
                      <>
                        <div className="current-price neon-text neon-green">
                          <DollarSign size={16} /> {formatUsdAmount(solPrice)}
                        </div>
                        {lastUpdated && (
                          <div className="price-updated" style={{ fontSize: '0.8em', color: '#888', marginTop: '4px' }}>
                            Updated: {lastUpdated.toLocaleTimeString()}
                          </div>
                        )}
                      </>
                    ) : priceLoading ? (
                      <div className="price-loading" style={{ color: '#888' }}>
                        <RefreshCw size={16} /> Loading current price...
                      </div>
                    ) : (
                      <div className="price-error" style={{ color: '#ff6b6b' }}>
                        Unable to fetch current price
                      </div>
                    )}
                  </div>
                </div>

                {userWallet && (
                  <div className="wallet-info">
                    <h4 className="section-subtitle neon-text neon-cyan">Your Wallet Address</h4>
                    <div className="wallet-address">
                      <code className="address-text">{userWallet.public_key}</code>
                      <button
                        className="copy-btn neon-button neon-dim"
                        onClick={() => {
                          navigator.clipboard.writeText(userWallet.public_key);
                          setMessage('Address copied to clipboard!');
                          setTimeout(() => setMessage(''), 3000);
                        }}
                      >
                        <Copy size={16} /> Copy
                      </button>
                    </div>
                  </div>
                )}

                <div className="quick-actions">
                  <h4 className="section-subtitle neon-text neon-cyan">Quick Actions</h4>
                  <div className="action-buttons">
                    <button
                      className="action-btn neon-button neon-green"
                      onClick={() => setActiveTab('deposit')}
                    >
                      <CreditCard size={16} /> Check Deposits
                    </button>
                    <button
                      className="action-btn neon-button neon-orange"
                      onClick={() => setActiveTab('withdraw')}
                    >
                      <Banknote size={16} /> Withdraw SOL
                    </button>
                  </div>
                </div>

                <div className="account-info">
                  <h4 className="section-subtitle neon-text neon-cyan">Network Information</h4>
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="info-label">Network:</span>
                      <span className="info-value neon-text neon-green">Solana Devnet</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Transaction Fee:</span>
                      <span className="info-value">~0.000005 SOL</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Confirmation Time:</span>
                      <span className="info-value">~1-2 seconds</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label">Security Level:</span>
                      <span className="info-value neon-text neon-green">High</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'deposit' && (
              <div className="deposit-section">
                <h3 className="section-title neon-text neon-green"><CreditCard size={18} /> Deposit SOL</h3>

                {!userWallet ? (
                  <div className="no-wallet-message">
                    <p className="neon-text neon-orange">No wallet found. Please sign out and sign back in to create a Solana wallet.</p>
                  </div>
                ) : (
                  <div className="deposit-form">
                    <div className="wallet-address-section">
                      <h4 className="info-title neon-text neon-cyan">Your Deposit Address</h4>
                      <div className="address-display">
                        <code className="address-text">{userWallet.public_key}</code>
                        <button
                          className="copy-btn neon-button neon-dim"
                          onClick={() => {
                            navigator.clipboard.writeText(userWallet.public_key);
                            setMessage('Address copied to clipboard!');
                            setTimeout(() => setMessage(''), 3000);
                          }}
                        >
                          <Copy size={16} /> Copy
                        </button>
                      </div>
                    </div>

                    <div className="deposit-info">
                      <h4 className="info-title neon-text neon-cyan">How to Deposit</h4>
                      <ul className="info-list">
                        <li>Send SOL to the address above from any Solana wallet</li>
                        <li>This is a Solana Devnet address</li>
                        <li>Get free devnet SOL from the <a href="https://faucet.solana.com" target="_blank" rel="noopener noreferrer" className="neon-text neon-yellow">Solana Faucet</a></li>
                        <li>Transactions confirm in 1-2 seconds</li>
                        <li>Click 'Check for Deposits' after sending</li>
                      </ul>
                    </div>

                    <button
                      className="crypto-action-btn neon-button neon-green"
                      onClick={handleDeposit}
                      disabled={isProcessing}
                    >
                      {isProcessing ? 'Checking...' : <><RefreshCw size={16} /> Check for Deposits</>}
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'withdraw' && (
              <div className="withdraw-section">
                <h3 className="section-title neon-text neon-orange"><Banknote size={18} /> Withdraw SOL</h3>

                <div className="withdraw-form">
                  <div className="form-group">
                    <label className="form-label">Amount (SOL)</label>
                    <input
                      type="number"
                      value={withdrawAmount}
                      onChange={(e) => setWithdrawAmount(e.target.value)}
                      placeholder="Enter amount to withdraw"
                      className="crypto-input neon-input"
                      min="0.01"
                      max={userProfile?.solana_balance || 0}
                      step="0.000001"
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Solana Wallet Address</label>
                    <input
                      type="text"
                      value={withdrawAddress}
                      onChange={(e) => setWithdrawAddress(e.target.value)}
                      placeholder="Enter destination Solana wallet address"
                      className="crypto-input neon-input"
                    />
                  </div>

                  <div className="withdraw-info">
                    <h4 className="info-title neon-text neon-cyan">Withdrawal Information</h4>
                    <ul className="info-list">
                      <li>Minimum withdrawal: 0.01 SOL</li>
                      <li>Maximum available: {userProfile?.solana_balance.toFixed(6)} SOL</li>
                      <li>Network fee: ~0.000005 SOL</li>
                      <li>Processing time: 1-2 seconds</li>
                      <li>Withdrawals are irreversible</li>
                      <li>Currently in development - contact support for withdrawals</li>
                    </ul>
                  </div>

                  <button
                    className="crypto-action-btn neon-button neon-orange"
                    onClick={handleWithdraw}
                    disabled={isProcessing || !withdrawAmount || !withdrawAddress}
                  >
                    {isProcessing ? 'Processing...' : 'Request Withdrawal'}
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'history' && (
              <div className="history-section">
                <h3 className="section-title neon-text neon-cyan"><FileText size={18} /> Transaction History</h3>

                <div className="transaction-list">
                  <div className="transaction-item">
                    <div className="transaction-info">
                      <span className="transaction-type deposit"><CreditCard size={14} /> Deposit Check</span>
                      <span className="transaction-amount neon-text neon-green">Balance Refresh</span>
                    </div>
                    <div className="transaction-details">
                      <span className="transaction-date">Recent</span>
                      <span className="transaction-status completed">Completed</span>
                    </div>
                  </div>

                  <div className="no-transactions">
                    <p className="neon-text neon-dim">Transaction history will appear here as you use the wallet.</p>
                  </div>
                </div>

                <div className="history-footer">
                  <button className="load-more-btn neon-button neon-dim" disabled>
                    Load More Transactions
                  </button>
                </div>
              </div>
            )}
          </div>

          {message && (
            <div className={`crypto-message ${message.includes('Success') || message.includes('copied') || message.includes('refreshed') ? 'success' : 'error'}`}>
              {message}
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button className="neon-button neon-yellow" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};
