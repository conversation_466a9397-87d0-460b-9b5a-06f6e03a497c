import React from 'react';
import { useSolanaPrice } from '../hooks/useSolanaPrice';

interface SolBalanceDisplayProps {
  balance: number | null;
  loading?: boolean;
  error?: string | null;
  showUsd?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const SolBalanceDisplay: React.FC<SolBalanceDisplayProps> = ({
  balance,
  loading = false,
  error = null,
  showUsd = true,
  className = '',
  style = {}
}) => {
  const {
    solPrice,
    loading: priceLoading,
    error: priceError,
    convertSolToUsd,
    formatUsdAmount
  } = useSolanaPrice({ autoRefresh: true });

  const formatSolBalance = (balance: number | null): string => {
    if (balance === null) return 'N/A';
    return `${balance.toFixed(6)} SOL`;
  };

  const getUsdValue = (): string => {
    if (!showUsd || balance === null || priceLoading) return '';
    if (priceError || solPrice === null) return '(Price unavailable)';
    
    const usdValue = convertSolToUsd(balance);
    return usdValue !== null ? `(${formatUsdAmount(usdValue)})` : '(Price unavailable)';
  };

  return (
    <div className={className} style={style}>
      <span className="sol-amount" style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
        <img 
          src="/assets/solana-icon.png" 
          alt="Solana" 
          style={{ width: '50px', height: '50px', marginRight: '6px', verticalAlign: 'middle', border: '2px solid transparent', background: 'linear-gradient(45deg, #9945FF, #14F195, #00D4AA, #9945FF) border-box', backgroundClip: 'padding-box, border-box', backgroundOrigin: 'padding-box, border-box', borderRadius: '8px' }}
        />
        {loading ? 'Loading...' : error ? 'Error' : formatSolBalance(balance)}
      </span>
      {showUsd && balance !== null && !loading && !error && (
        <span className="usd-amount" style={{ 
          marginLeft: '8px', 
          fontSize: '0.85em', 
          color: '#666',
          fontWeight: 'normal'
        }}>
          {getUsdValue()}
        </span>
      )}
    </div>
  );
};

export default SolBalanceDisplay;