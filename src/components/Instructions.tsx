import React, { useState } from 'react';
import { 
  Gamepad2, Mouse, Zap, DollarSign, Star, Skull, 
  Crosshair, Target, Hash, Navigation, Eye, RefreshCw
} from 'lucide-react';
import { GameMode } from '../App';
import '../snakepit-theme.css';

interface InstructionsProps {
  gameMode: GameMode;
}

const Instructions: React.FC<InstructionsProps> = ({ gameMode }) => {
  const [isCollapsed, setIsCollapsed] = useState(true); // Collapsed by default

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div className={`instructions ${isCollapsed ? 'collapsed' : ''}`}>
      <div className="instructions-header" onClick={toggleCollapse}>
        <h3><Gamepad2 size={18} /> Pit Rules:</h3>
        <button className="collapse-button">
          {isCollapsed ? '▲' : '▼'}
        </button>
      </div>

      <div className="instructions-content">
        <p><Mouse size={16} /> Mouse: Navigate your snake</p>
        <p><Zap size={16} /> Right Click: Turbo boost</p>
        <p><DollarSign size={16} /> Collect cash to grow your empire!</p>
        <p><Star size={16} /> Grab golden orbs for mega payouts!</p>
        <p><Skull size={16} /> Don't crash into other snakes!</p>
        {gameMode === 'warfare' && (
          <>
            <p><Crosshair size={16} /> Left Click / Space: Fire weapons</p>
        <p><Target size={16} /> Collect weapon drops to arm up!</p>
        <p><Skull size={16} /> Headshots = instant elimination</p>
        <p>💧 Body shots = snake damage</p>
        <p><Hash size={16} /> 1-3: Switch weapons</p>
          </>
        )}

        <div className="controller-section">
          <p><Gamepad2 size={16} /> <strong>Controller Support:</strong></p>
        <p><Navigation size={16} /> Left Stick: Move snake</p>
        <p><Zap size={16} /> A/Cross: Boost</p>
        <p><DollarSign size={16} /> X/Square: Cash out</p>
        <p><Eye size={16} /> Y/Triangle: Spectate next</p>
          {gameMode === 'warfare' && (
            <>
              <p><Crosshair size={16} /> RT/R2: Shoot</p>
        <p><RefreshCw size={16} /> LB/L1, RB/R1, LT/L2: Switch weapons</p>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default Instructions;