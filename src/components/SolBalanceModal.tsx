import React, { useState, useEffect, useCallback } from 'react';
import { X, ArrowUpRight, ArrowDownLeft, Clock, CheckCircle, XCircle, Copy, ExternalLink, RefreshCw } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useSolanaPrice } from '../hooks/useSolanaPrice';
import { getUserTransactions, addTransaction, type Transaction } from '../lib/supabase';
import solanaClientService from '../services/SolanaClientService';
import { useWalletBalance } from '../hooks/useWalletBalance';

interface SolBalanceModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SolBalanceModal: React.FC<SolBalanceModalProps> = ({ isOpen, onClose }) => {
  const { user, userProfile, updateProfile } = useAuth();
  const { solPrice, convertSolToUsd, formatUsdAmount } = useSolanaPrice({ autoRefresh: true });
  const {
    balance: walletBalance,
    usdBalance: walletUsdBalance,
    loading: balanceLoading,
    priceLoading,
    refreshBalance,
    refreshPrice,
    refreshAll
  } = useWalletBalance({
    autoRefresh: true,
    refreshInterval: 60000, // 1 minute
    syncWithDatabase: true
  });
  
  const [activeTab, setActiveTab] = useState<'deposit' | 'withdraw' | 'transactions'>('deposit');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Deposit state
  const [depositAmount, setDepositAmount] = useState('');
  const [depositAddress, setDepositAddress] = useState('');
  const [showQRCode, setShowQRCode] = useState(false);

  // Withdraw state
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawAddress, setWithdrawAddress] = useState('');

  const loadUserWallet = useCallback(async () => {
    if (!user) return;

    try {
      const balanceResponse = await solanaClientService.getUserBalance(user.id);
      if (balanceResponse.success && balanceResponse.publicKey) {
        setDepositAddress(balanceResponse.publicKey);
      }
    } catch (error) {
      console.error('Error loading wallet:', error);
    }
  }, [user]);

  const loadTransactions = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    try {
      const userTransactions = await getUserTransactions(user.id, 50);
      setTransactions(userTransactions);
    } catch (error) {
      console.error('Error loading transactions:', error);
      setError('Failed to load transactions');
    } finally {
      setLoading(false);
    }
  }, [user]);

  // Load user's wallet address and transactions
  useEffect(() => {
    if (isOpen && user) {
      loadUserWallet();
      loadTransactions();
    }
  }, [isOpen, user, loadUserWallet, loadTransactions]);

  const handleDeposit = async () => {
    if (!user || !depositAmount) return;
    
    const amount = parseFloat(depositAmount);
    if (isNaN(amount) || amount <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // Create a pending deposit transaction
      const success = await addTransaction({
        user_id: user.id,
        type: 'deposit',
        amount: amount,
        status: 'pending',
        description: `Deposit of ${amount} SOL`,
        to_wallet: depositAddress
      });

      if (success) {
        setSuccess(`Deposit request created. Please send ${amount} SOL to ${depositAddress}`);
        setDepositAmount('');
        loadTransactions();
      } else {
        setError('Failed to create deposit request');
      }
    } catch (error) {
      console.error('Error creating deposit:', error);
      setError('Failed to create deposit request');
    } finally {
      setLoading(false);
    }
  };

  const handleWithdraw = async () => {
    if (!user || !withdrawAmount || !withdrawAddress) return;
    
    const amount = parseFloat(withdrawAmount);
    if (isNaN(amount) || amount <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    if (amount > (userProfile?.solana_balance || 0)) {
      setError('Insufficient balance');
      return;
    }

    setLoading(true);
    setError(null);
    
    try {
      // Use the Solana client service to transfer to external wallet
      const result = await solanaClientService.transferToExternalWallet(user.id, withdrawAddress, amount);
      
      if (result.success) {
        setSuccess(`Withdrawal of ${amount} SOL initiated to ${withdrawAddress}`);
        setWithdrawAmount('');
        setWithdrawAddress('');
        
        // Update local balance
        if (userProfile) {
          await updateProfile({ solana_balance: userProfile.solana_balance - amount });
        }
        
        loadTransactions();
      } else {
        setError(result.error || 'Failed to process withdrawal');
      }
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      setError('Failed to process withdrawal');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setSuccess('Copied to clipboard');
    setTimeout(() => setSuccess(null), 2000);
  };

  // Generate QR code URL using a public QR code API
  const generateQRCodeUrl = (text: string) => {
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(text)}`;
  };

  const formatTransactionType = (type: string) => {
    switch (type) {
      case 'deposit': return 'Deposit';
      case 'withdrawal': return 'Withdrawal';
      case 'wager': return 'Game Wager';
      case 'cashout': return 'Game Cashout';
      case 'room_transfer': return 'Room Transfer';
      case 'admin_transfer': return 'Admin Transfer';
      default: return type;
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit':
      case 'cashout':
        return <ArrowDownLeft className="text-green-400" size={16} />;
      case 'withdrawal':
      case 'wager':
        return <ArrowUpRight className="text-red-400" size={16} />;
      default:
        return <Clock className="text-yellow-400" size={16} />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle className="text-green-400" size={16} />;
      case 'failed':
        return <XCircle className="text-red-400" size={16} />;
      default:
        return <Clock className="text-yellow-400" size={16} />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content sol-balance-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>SOL Balance Management</h2>
          <button onClick={onClose} className="modal-close-btn">
            <X size={24} />
          </button>
        </div>

        {/* Current Balance Display - Navbar Style */}
        <div className="balance-overview">
          <div className="balance-main-navbar-style">
            <div className="balance-section-modal sol-section-modal">
              <img
                src="/assets/solana-icon.png"
                alt="SOL"
                className="balance-icon-modal"
              />
              <span className="balance-label-modal sol-label-modal">SOL</span>
              <span className="balance-value-modal">
                {walletBalance !== null ? walletBalance.toFixed(3) : (userProfile?.solana_balance?.toFixed(3) || '0.000')}
              </span>
              <button
                className="refresh-btn-modal sol-refresh-modal"
                onClick={refreshBalance}
                disabled={balanceLoading}
                title="Refresh SOL balance from blockchain"
              >
                <RefreshCw size={14} className={balanceLoading ? 'spinning' : ''} />
              </button>
            </div>
            {solPrice && (
              <div className="balance-section-modal usd-section-modal">
                <span className="balance-label-modal usd-label-modal">USD</span>
                <span className="balance-value-modal">
                  {walletUsdBalance !== null
                    ? formatUsdAmount(walletUsdBalance)
                    : formatUsdAmount(convertSolToUsd(userProfile?.solana_balance ?? 0) ?? 0)
                  }
                </span>
                <button
                  className="refresh-btn-modal usd-refresh-modal"
                  onClick={refreshPrice}
                  disabled={priceLoading}
                  title="Refresh USD price from Pyth Network"
                >
                  <RefreshCw size={14} className={priceLoading ? 'spinning' : ''} />
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="tab-navigation">
          <button 
            className={`tab-btn ${activeTab === 'deposit' ? 'active' : ''}`}
            onClick={() => setActiveTab('deposit')}
          >
            <ArrowDownLeft size={16} />
            Deposit
          </button>
          <button 
            className={`tab-btn ${activeTab === 'withdraw' ? 'active' : ''}`}
            onClick={() => setActiveTab('withdraw')}
          >
            <ArrowUpRight size={16} />
            Withdraw
          </button>
          <button 
            className={`tab-btn ${activeTab === 'transactions' ? 'active' : ''}`}
            onClick={() => setActiveTab('transactions')}
          >
            <Clock size={16} />
            Transactions
          </button>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {activeTab === 'deposit' && (
            <div className="deposit-tab">
              <h3>Deposit SOL</h3>
              <p>Send SOL to your wallet address to add funds to your account.</p>

              <div className="form-group">
                <label>Your Wallet Address</label>
                <div className="address-input-container">
                  <div className="address-input">
                    <input
                      type="text"
                      value={depositAddress}
                      readOnly
                      className="wallet-address-input"
                    />
                    <button
                      onClick={() => copyToClipboard(depositAddress)}
                      className="copy-btn"
                      title="Copy address"
                    >
                      <Copy size={16} />
                    </button>
                    <button
                      onClick={() => setShowQRCode(!showQRCode)}
                      className="qr-btn"
                      title="Show QR Code"
                    >
                      📱
                    </button>
                  </div>

                  {showQRCode && depositAddress && (
                    <div className="qr-code-container">
                      <div className="qr-code-wrapper">
                        <img
                          src={generateQRCodeUrl(depositAddress)}
                          alt="Wallet Address QR Code"
                          className="qr-code-image"
                        />
                        <p className="qr-code-label">Scan with your mobile wallet</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="form-group">
                <label>Amount to Deposit (SOL)</label>
                <input
                  type="number"
                  value={depositAmount}
                  onChange={(e) => setDepositAmount(e.target.value)}
                  placeholder="0.000000"
                  step="0.000001"
                  min="0"
                  className="amount-input"
                />
                {depositAmount && solPrice && (
                  <div className="amount-usd">
                    ≈ {formatUsdAmount(convertSolToUsd(parseFloat(depositAmount) || 0) ?? 0)}
                  </div>
                )}
              </div>

              <button
                onClick={handleDeposit}
                disabled={loading || !depositAmount}
                className="action-btn deposit-btn"
              >
                {loading ? 'Creating...' : 'Create Deposit Request'}
              </button>
            </div>
          )}

          {activeTab === 'withdraw' && (
            <div className="withdraw-tab">
              <h3>Withdraw SOL</h3>
              <p>Send SOL from your account to an external wallet address.</p>

              <div className="form-group">
                <label>Recipient Wallet Address</label>
                <div className="withdraw-address-input">
                  <input
                    type="text"
                    value={withdrawAddress}
                    onChange={(e) => setWithdrawAddress(e.target.value)}
                    placeholder="Enter Solana wallet address (e.g., 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU)"
                    className="wallet-address-input withdraw-input"
                  />
                </div>
              </div>

              <div className="form-group">
                <label>Amount to Withdraw (SOL)</label>
                <input 
                  type="number" 
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  placeholder="0.000000"
                  step="0.000001"
                  min="0"
                  max={userProfile?.solana_balance || 0}
                  className="amount-input"
                />
                {withdrawAmount && solPrice && (
                  <div className="amount-usd">
                    ≈ {formatUsdAmount(convertSolToUsd(parseFloat(withdrawAmount) || 0) ?? 0)}
                  </div>
                )}
                <div className="balance-info-small">
                  Available: {userProfile?.solana_balance?.toFixed(6) || '0.000000'} SOL
                </div>
              </div>

              <button 
                onClick={handleWithdraw}
                disabled={loading || !withdrawAmount || !withdrawAddress}
                className="action-btn withdraw-btn"
              >
                {loading ? 'Processing...' : 'Withdraw SOL'}
              </button>
            </div>
          )}

          {activeTab === 'transactions' && (
            <div className="transactions-tab">
              <h3>Transaction History</h3>
              
              {loading ? (
                <div className="loading-state">Loading transactions...</div>
              ) : transactions.length === 0 ? (
                <div className="empty-state">No transactions found</div>
              ) : (
                <div className="transactions-list">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="transaction-item">
                      <div className="transaction-icon">
                        {getTransactionIcon(transaction.type)}
                      </div>
                      <div className="transaction-details">
                        <div className="transaction-type">
                          {formatTransactionType(transaction.type)}
                        </div>
                        <div className="transaction-description">
                          {transaction.description}
                        </div>
                        <div className="transaction-date">
                          {new Date(transaction.created_at).toLocaleDateString()} {new Date(transaction.created_at).toLocaleTimeString()}
                        </div>
                      </div>
                      <div className="transaction-amount">
                        <div className={`amount ${transaction.type === 'deposit' || transaction.type === 'cashout' ? 'positive' : 'negative'}`}>
                          {transaction.type === 'deposit' || transaction.type === 'cashout' ? '+' : '-'}{transaction.amount.toFixed(6)} SOL
                        </div>
                        <div className="transaction-status">
                          {getStatusIcon(transaction.status)}
                          <span>{transaction.status}</span>
                        </div>
                      </div>
                      {transaction.solana_signature && (
                        <div className="transaction-signature">
                          <button 
                            onClick={() => window.open(`https://explorer.solana.com/tx/${transaction.solana_signature}`, '_blank')}
                            className="signature-btn"
                            title="View on Solana Explorer"
                          >
                            <ExternalLink size={14} />
                          </button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="message error-message">
            <XCircle size={16} />
            {error}
          </div>
        )}
        
        {success && (
          <div className="message success-message">
            <CheckCircle size={16} />
            {success}
          </div>
        )}
      </div>
    </div>
  );
};

export default SolBalanceModal;

// Add styles to the component
const styles = `
  .sol-balance-modal {
    width: 600px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
  }

  .sol-balance-modal .modal-header {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    position: relative !important;
    margin-bottom: 20px !important;
    width: 100% !important;
  }

  .sol-balance-modal .modal-header h2 {
    color: #ffffff !important;
    text-align: center !important;
    margin: 0 !important;
    font-size: 20px !important;
    font-weight: 600 !important;
    flex: 1 !important;
    width: 100% !important;
    position: absolute !important;
    left: 0 !important;
    right: 0 !important;
  }

  .sol-balance-modal .modal-close-btn {
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 10 !important;
    background: transparent !important;
    border: none !important;
    color: #ffffff !important;
    cursor: pointer !important;
    padding: 8px !important;
    border-radius: 4px !important;
    transition: background-color 0.2s ease !important;
  }

  .sol-balance-modal .modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.1) !important;
  }

  .balance-overview {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .balance-main-navbar-style {
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: center;
  }

  .balance-section-modal {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  .balance-section-modal:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .sol-section-modal {
    background: rgba(153, 69, 255, 0.1);
    border-color: rgba(153, 69, 255, 0.3);
  }

  .sol-section-modal:hover {
    background: rgba(153, 69, 255, 0.15);
    border-color: rgba(153, 69, 255, 0.4);
  }

  .usd-section-modal {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
  }

  .usd-section-modal:hover {
    background: rgba(34, 197, 94, 0.15);
    border-color: rgba(34, 197, 94, 0.4);
  }

  .balance-label-modal {
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .sol-label-modal {
    color: #9945FF;
  }

  .usd-label-modal {
    color: #22c55e;
  }

  .balance-value-modal {
    color: #ffffff;
    font-weight: 700;
    font-size: 12px;
  }

  .balance-icon-modal {
    width: 14px;
    height: 14px;
    border-radius: 2px;
  }

  .tab-navigation {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .tab-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .tab-btn:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
  }

  .tab-btn.active {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .tab-content {
    min-height: 300px;
  }

  .deposit-tab,
  .withdraw-tab,
  .transactions-tab {
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .deposit-tab h3,
  .withdraw-tab h3,
  .transactions-tab h3 {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 8px;
  }

  .deposit-tab p,
  .withdraw-tab p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 24px;
    font-size: 14px;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 8px;
  }

  .address-input-container {
    width: 100%;
  }

  .address-input {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
  }

  .wallet-address-input,
  .amount-input {
    flex: 1;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #ffffff;
    font-size: 14px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }

  .wallet-address-input:focus,
  .amount-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  }

  .withdraw-address-input {
    width: 100%;
  }

  .withdraw-input {
    width: 100%;
    padding: 14px 18px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    color: #ffffff;
    font-size: 14px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    transition: all 0.3s ease;
  }

  .withdraw-input:focus {
    outline: none;
    border-color: rgba(153, 69, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(153, 69, 255, 0.1);
    background: rgba(255, 255, 255, 0.1);
  }

  .withdraw-input::placeholder {
    color: rgba(255, 255, 255, 0.4);
    font-size: 13px;
  }

  .copy-btn,
  .qr-btn {
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .copy-btn:hover,
  .qr-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
  }

  .qr-btn {
    font-size: 16px;
  }

  .qr-code-container {
    margin-top: 16px;
    display: flex;
    justify-content: center;
  }

  .qr-code-wrapper {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .qr-code-image {
    width: 200px;
    height: 200px;
    border-radius: 8px;
    margin-bottom: 12px;
  }

  .qr-code-label {
    color: #333333;
    font-size: 12px;
    font-weight: 500;
    margin: 0;
  }

  .amount-usd {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 4px;
  }

  .balance-info-small {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 4px;
  }

  .action-btn {
    width: 100%;
    padding: 14px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 8px;
  }

  .deposit-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: #ffffff;
  }

  .deposit-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
  }

  .withdraw-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: #ffffff;
  }

  .withdraw-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
  }

  .action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }

  .loading-state,
  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
  }

  .transactions-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .transaction-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
  }

  .transaction-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .transaction-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
  }

  .transaction-details {
    flex: 1;
    text-align: left !important;
  }

  .transaction-type {
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    margin-bottom: 2px;
    text-align: left !important;
  }

  .transaction-description {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 2px;
    text-align: left !important;
  }

  .transaction-date {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.4);
    text-align: left !important;
  }

  .transaction-amount {
    text-align: right;
  }

  .amount {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  }

  .amount.positive {
    color: #10b981;
  }

  .amount.negative {
    color: #f87171;
  }

  .transaction-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
  }

  .transaction-signature {
    display: flex;
    align-items: center;
  }

  .signature-btn {
    padding: 6px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .signature-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
  }

  .message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    margin-top: 16px;
  }

  .error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #fca5a5;
  }

  .success-message {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
