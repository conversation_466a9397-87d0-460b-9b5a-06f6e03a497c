import React, { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { Copy, ExternalLink, X } from 'lucide-react'
import WalletBalance from './WalletBalance'
import { useSolanaPrice } from '../hooks/useSolanaPrice'

interface WalletPopupProps {
  isOpen: boolean
  onClose: () => void
}

export function WalletPopup({ isOpen, onClose }: WalletPopupProps) {
  const { walletCreationData, setWalletCreationData } = useAuth()
  const [copied, setCopied] = useState(false)
  
  const {
    solPrice,
    convertSolToUsd,
    formatUsdAmount
  } = useSolanaPrice({ autoRefresh: true })

  if (!isOpen || !walletCreationData) return null

  const handleCopyAddress = async () => {
    try {
      await navigator.clipboard.writeText(walletCreationData.publicKey)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy address:', error)
    }
  }

  const handleClose = () => {
    setWalletCreationData(null)
    onClose()
  }

  const openSolanaFaucet = () => {
    window.open('https://faucet.solana.com/', '_blank')
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 relative">
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
        >
          <X size={20} />
        </button>

        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Wallet Created Successfully!
          </h2>
          <p className="text-gray-600">
            Your Solana wallet has been created. You'll need to fund it to start playing.
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your Wallet Address
            </label>
            <div className="flex items-center space-x-2">
              <div className="flex-1 p-3 bg-gray-50 rounded-lg border text-sm font-mono break-all">
                {walletCreationData.publicKey}
              </div>
              <button
                onClick={handleCopyAddress}
                className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
                title="Copy address"
              >
                <Copy size={16} />
              </button>
            </div>
            {copied && (
              <p className="text-sm text-green-600 mt-1">Address copied to clipboard!</p>
            )}
          </div>

          {/* Current Balance Display */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Current Balance</h3>
            <WalletBalance 
              enableRealTimeUpdates={true}
              autoRefresh={false}
              refreshInterval={15000}
              showRefreshButton={true}
              showFaucetLink={true}
              showMonitoringStatus={true}
              className="wallet-balance-section"
            />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">Next Steps:</h3>
            <ol className="text-sm text-blue-800 space-y-1">
              <li>1. Fund your wallet with SOL (Solana's native token)</li>
              <li>2. You need at least 0.05 SOL to join games</li>
              <li>3. Use the Solana faucet for free testnet SOL</li>
              <li>4. Your balance will update automatically</li>
            </ol>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={openSolanaFaucet}
              className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center space-x-2"
            >
              <ExternalLink size={16} />
              <span>Get Free SOL</span>
            </button>
            <button
              onClick={handleClose}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
            >
              I'll Fund Later
            </button>
          </div>

          <div className="text-xs text-gray-500 text-center">
            <p><span className="icon-warning"></span> This is a testnet wallet. Do not send real SOL to this address.</p>
          </div>
        </div>
      </div>
    </div>
  )
}