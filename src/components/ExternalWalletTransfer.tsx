import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import solanaClientService from '../services/SolanaClientService';
import { useWalletBalance } from '../hooks/useWalletBalance';

interface ExternalWalletTransferProps {
  onClose: () => void;
  onTransferComplete?: (signature: string, amount: number) => void;
}

const ExternalWalletTransfer: React.FC<ExternalWalletTransferProps> = ({
  onClose,
  onTransferComplete
}) => {
  const { user } = useAuth();
  const { balance, refreshBalance } = useWalletBalance();
  const [externalAddress, setExternalAddress] = useState('');
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleTransfer = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError('User not authenticated');
      return;
    }

    if (!externalAddress || !amount) {
      setError('Please fill in all fields');
      return;
    }

    const transferAmount = parseFloat(amount);
    if (isNaN(transferAmount) || transferAmount <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    if (balance && transferAmount > balance) {
      setError('Insufficient balance');
      return;
    }

    if (!solanaClientService.isValidSolanaAddress(externalAddress)) {
      setError('Invalid Solana wallet address');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await solanaClientService.transferToExternalWallet(
        user.id,
        externalAddress,
        transferAmount
      );

      if (result.success) {
        setSuccess(`Transfer successful! Transaction: ${result.signature}`);
        
        // Refresh balance
        await refreshBalance();
        
        // Call completion callback
        if (onTransferComplete && result.signature) {
          onTransferComplete(result.signature, transferAmount);
        }

        // Clear form
        setExternalAddress('');
        setAmount('');
      } else {
        setError(result.error || 'Transfer failed');
      }
    } catch (err) {
      console.error('Transfer error:', err);
      setError('Transfer failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const maxAmount = balance || 0;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">Transfer to External Wallet</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            ✕
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-300 text-sm mb-2">
            Current Balance: {balance ? solanaClientService.formatSolAmount(balance) : '0.00'} SOL
          </p>
        </div>

        <form onSubmit={handleTransfer} className="space-y-4">
          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">
              External Wallet Address
            </label>
            <input
              type="text"
              value={externalAddress}
              onChange={(e) => setExternalAddress(e.target.value)}
              placeholder="Enter Solana wallet address"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={loading}
            />
          </div>

          <div>
            <label className="block text-gray-300 text-sm font-medium mb-2">
              Amount (SOL)
            </label>
            <div className="relative">
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="0.00"
                step="0.000001"
                min="0"
                max={maxAmount}
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              />
              <button
                type="button"
                onClick={() => setAmount(maxAmount.toString())}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-400 hover:text-blue-300 text-sm"
                disabled={loading}
              >
                Max
              </button>
            </div>
          </div>

          {error && (
            <div className="bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-900 border border-green-700 text-green-300 px-4 py-3 rounded">
              {success}
            </div>
          )}

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading || !externalAddress || !amount}
            >
              {loading ? 'Transferring...' : 'Transfer'}
            </button>
          </div>
        </form>

        <div className="mt-4 text-xs text-gray-400">
          <p>⚠️ Double-check the wallet address before transferring.</p>
          <p>Transfers to external wallets cannot be reversed.</p>
        </div>
      </div>
    </div>
  );
};

export default ExternalWalletTransfer;
