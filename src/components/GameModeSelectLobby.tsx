import React, { useState, useEffect } from 'react';
import { GameMode } from '../App';
import { useAuth } from '../contexts/AuthContext';
import { useSolanaPrice } from '../hooks/useSolanaPrice';
import {
  User, Gamepad2, ShoppingCart, Backpack, Trophy, Medal,
  BarChart3, HelpCircle, Crown, Target, Settings as SettingsIcon,
  Search, Filter, Users, Clock, Star, Palette, Sword, Zap,
  Smile, Sparkles, Lock, Check, Shield, DollarSign, Package,
  Gift, Loader2, CheckCircle, AlertCircle, Flame, ChevronUp, ChevronDown,
  RefreshCw
} from 'lucide-react';
import Avatar3D from './Avatar3D';
import SolBalanceModal from './SolBalanceModal';
import NewsSlider from './NewsSlider';
import { useWalletBalance } from '../hooks/useWalletBalance';

interface UserData {
  username: string;
  wager: number;
  solanaBalance: number;
  userId?: string;
}

interface GameModeSelectLobbyProps {
  onModeSelect: (mode: GameMode, userData: UserData) => void;
  onBackToStartScreen: () => void;
  onShowProfile: () => void;
  onShowSettings: () => void;
  onShowLeaderboard: () => void;
  onShowHelp: () => void;
  userData: UserData;
  setUserData: (userData: UserData) => void;
  selectedMode: GameMode;
  setSelectedMode: (mode: GameMode) => void;
  onEnterBattle: () => void;
}

const GameModeSelectLobby: React.FC<GameModeSelectLobbyProps> = ({
  onModeSelect,
  onBackToStartScreen,
  onShowProfile,
  onShowSettings,
  onShowLeaderboard,
  onShowHelp,
  userData,
  setUserData,
  selectedMode,
  setSelectedMode,
  onEnterBattle
}) => {
  const { userProfile } = useAuth();
  const { solPrice, convertSolToUsd, formatUsdAmount } = useSolanaPrice({ autoRefresh: true });
  const {
    balance: walletBalance,
    usdBalance: walletUsdBalance,
    loading: balanceLoading,
    priceLoading,
    refreshBalance,
    refreshPrice,
    refreshAll
  } = useWalletBalance({
    autoRefresh: true,
    refreshInterval: 60000, // 1 minute
    syncWithDatabase: true
  });

  // Tab system state
  const [activeTab, setActiveTab] = useState<'play' | 'locker' | 'shop' | 'pitpass' | 'leaderboard' | 'compete' | 'career'>('play');

  // Local state for tracking selected card (to avoid duplicate selections)
  const [selectedCardId, setSelectedCardId] = useState<number>(1);

  // Locker state
  const [selectedLockerCategory, setSelectedLockerCategory] = useState<'skins' | 'weapons' | 'powerups' | 'emotes' | 'trails'>('skins');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedSkin, setSelectedSkin] = useState<string>('default');

  // Shop state
  const [selectedShopCategory, setSelectedShopCategory] = useState<'skins' | 'weapons' | 'powerups' | 'emotes' | 'trails' | 'bundles'>('skins');
  const [isPurchasing, setIsPurchasing] = useState<string | null>(null);
  const [purchaseSuccess, setPurchaseSuccess] = useState<string | null>(null);

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'classic' | 'warfare' | 'speed' | 'tournament' | 'custom'>('all');
  const [isGameModesPanelVisible, setIsGameModesPanelVisible] = useState(false);

  // Sol balance modal state
  const [isSolBalanceModalOpen, setIsSolBalanceModalOpen] = useState(false);

  // Profile dropdown state
  const [isProfileDropdownVisible, setIsProfileDropdownVisible] = useState(false);

  // PitPass subscription state
  const [selectedPitPassTier, setSelectedPitPassTier] = useState<'basic' | 'premium' | 'elite'>('basic');
  const [isPitPassPurchasing, setIsPitPassPurchasing] = useState<string | null>(null);
  const [showPitPassConfirmation, setShowPitPassConfirmation] = useState(false);

  // Leaderboard state
  const [selectedLeaderboardCategory, setSelectedLeaderboardCategory] = useState<'global' | 'friends' | 'weekly' | 'monthly' | 'alltime'>('global');
  const [selectedLeaderboardType, setSelectedLeaderboardType] = useState<'wins' | 'score' | 'kills' | 'survival' | 'earnings'>('wins');

  // Manual toggle for game modes panel
  const toggleGameModesPanel = () => {
    setIsGameModesPanelVisible(!isGameModesPanelVisible);
  };

  // Scroll state for dynamic drawer positioning
  const [drawerScrollOffset, setDrawerScrollOffset] = useState(0);

  // Load selected mode from localStorage on component mount
  useEffect(() => {
    const savedMode = localStorage.getItem('snakepit-selected-game-mode');
    if (savedMode && ['classic', 'warfare', 'speed'].includes(savedMode)) {
      setSelectedMode(savedMode as GameMode);
    }
  }, [setSelectedMode]);

  // Save selected mode to localStorage whenever it changes
  useEffect(() => {
    if (selectedMode) {
      localStorage.setItem('snakepit-selected-game-mode', selectedMode);
    }
  }, [selectedMode]);

  // Handle scroll events for dynamic drawer height expansion
  useEffect(() => {
    const handleScroll = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('game-modes-grid')) {
        const scrollTop = target.scrollTop;
        const maxExpansion = 30; // Maximum vh to expand (from 70vh to 100vh)
        const expansionRate = scrollTop / 10; // How fast to expand based on scroll
        const expansion = Math.min(expansionRate, maxExpansion);
        setDrawerScrollOffset(expansion);
      }
    };

    // Add scroll listener when drawer is visible
    if (isGameModesPanelVisible) {
      const gridElement = document.querySelector('.game-modes-grid');
      if (gridElement) {
        gridElement.addEventListener('scroll', handleScroll);
        return () => {
          gridElement.removeEventListener('scroll', handleScroll);
        };
      }
    } else {
      // Reset expansion when drawer is closed
      setDrawerScrollOffset(0);
    }
  }, [isGameModesPanelVisible]);

  // Sample game modes data with landscape images and ratings
  const gameModes = [
    { id: 0, name: 'LEVIATHON', type: 'leviathon', players: '1-100', duration: '60+ min', difficulty: 'LEGENDARY', rating: 5.0, online: 2847, image: '/assets/cards/Leviathan.png', isBossMode: true, prize: '$100K' },
    { id: 1, name: 'Classic Snake', type: 'classic', players: '1-4', duration: '5-10 min', difficulty: 'Easy', rating: 4.8, online: 127, image: '/assets/cards/classic.png' },
    { id: 2, name: 'Snake Warfare', type: 'warfare', players: '2-8', duration: '10-15 min', difficulty: 'Hard', rating: 4.9, online: 89, image: '/assets/cards/warfare.png' },
    { id: 3, name: 'Speed Snake', type: 'speed', players: '1-2', duration: '3-5 min', difficulty: 'Medium', rating: 4.6, online: 45, image: '/assets/cards/speed.png' },
    { id: 4, name: 'Team Battle', type: 'team', players: '4-12', duration: '15-20 min', difficulty: 'Hard', rating: 4.7, online: 63, image: '/assets/cards/TeamBattle.png' },
    { id: 5, name: 'Tournament Mode', type: 'tournament', players: '8-32', duration: '30-45 min', difficulty: 'Expert', rating: 4.9, online: 156, image: '/assets/cards/tournament.png' },
  ];

  const filteredGameModes = gameModes.filter(mode => {
    const matchesSearch = mode.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || mode.type === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  // PitPass subscription data
  interface PitPassTier {
    id: string;
    name: string;
    price: number;
    originalPrice?: number;
    popular?: boolean;
    features: string[];
    exclusiveRewards: string[];
    monthlyBonuses: {
      solBonus: number;
      xpMultiplier: number;
      exclusiveSkins: number;
      priorityQueue: boolean;
    };
  }

  const pitPassTiers: PitPassTier[] = [
    {
      id: 'basic',
      name: 'PitPass Basic',
      price: 9.99,
      features: [
        '10% XP Boost',
        '5% SOL Bonus on Wins',
        'Monthly Exclusive Skin',
        'Priority Customer Support',
        'Early Access to New Modes'
      ],
      exclusiveRewards: [
        'Venom Strike Skin',
        'Pit Warrior Badge',
        'Basic Trail Effects'
      ],
      monthlyBonuses: {
        solBonus: 0.05,
        xpMultiplier: 1.1,
        exclusiveSkins: 1,
        priorityQueue: false
      }
    },
    {
      id: 'premium',
      name: 'PitPass Premium',
      price: 19.99,
      originalPrice: 24.99,
      popular: true,
      features: [
        '25% XP Boost',
        '15% SOL Bonus on Wins',
        '3 Monthly Exclusive Skins',
        'Priority Queue Access',
        'Exclusive Tournament Entry',
        'Advanced Statistics',
        'Custom Emotes'
      ],
      exclusiveRewards: [
        'Golden Serpent Skin',
        'Pit Champion Badge',
        'Premium Trail Effects',
        'Victory Emotes Pack'
      ],
      monthlyBonuses: {
        solBonus: 0.15,
        xpMultiplier: 1.25,
        exclusiveSkins: 3,
        priorityQueue: true
      }
    },
    {
      id: 'elite',
      name: 'PitPass Elite',
      price: 39.99,
      features: [
        '50% XP Boost',
        '25% SOL Bonus on Wins',
        '5 Monthly Exclusive Skins',
        'VIP Priority Queue',
        'Exclusive Elite Tournaments',
        'Personal Statistics Coach',
        'Custom Avatar Frames',
        'Exclusive Discord Channel',
        'Monthly 1-on-1 Strategy Session'
      ],
      exclusiveRewards: [
        'Diamond Viper Skin',
        'Elite Pit Master Badge',
        'Legendary Trail Effects',
        'Elite Emotes Collection',
        'Custom Victory Animations'
      ],
      monthlyBonuses: {
        solBonus: 0.25,
        xpMultiplier: 1.5,
        exclusiveSkins: 5,
        priorityQueue: true
      }
    }
  ];

  // Mock user subscription status
  const userSubscription = {
    isActive: false,
    currentTier: null as string | null,
    expiresAt: null as Date | null,
    autoRenew: true,
    daysRemaining: 0
  };

  // Get current selected game mode details
  const getCurrentModeDetails = () => {
    const currentMode = gameModes.find(mode => mode.type === selectedMode);
    return currentMode || {
      id: 0,
      name: 'Select a Mode',
      type: selectedMode,
      players: '1-4',
      duration: '5-10 min',
      difficulty: 'Easy',
      rating: 4.5,
      online: 0,
      image: '/assets/classicMode.png'
    };
  };

  const currentModeDetails = getCurrentModeDetails();

  // Sample locker items data
  const lockerItems = {
    skins: [
      { id: 'default', name: 'Classic Snake', rarity: 'common', owned: true, equipped: true, icon: 'Palette', description: 'The original snake design' },
      { id: 'cobra', name: 'Cobra Warrior', rarity: 'legendary', owned: true, equipped: false, icon: 'Crown', description: 'A fierce cobra with battle scars' },
      { id: 'viper', name: 'Venom Viper', rarity: 'epic', owned: false, equipped: false, icon: 'Target', description: 'Poisonous green viper' },
      { id: 'python', name: 'Royal Python', rarity: 'rare', owned: true, equipped: false, icon: 'Star', description: 'Majestic golden python' },
      { id: 'rainbow', name: 'Rainbow Serpent', rarity: 'mythic', owned: false, equipped: false, icon: 'Sparkles', description: 'Legendary rainbow snake' },
      { id: 'fire', name: 'Fire Drake', rarity: 'legendary', owned: false, equipped: false, icon: 'Zap', description: 'Blazing fire snake' }
    ],
    weapons: [
      { id: 'basic', name: 'Basic Bite', rarity: 'common', owned: true, equipped: true, icon: 'Sword', description: 'Standard snake bite' },
      { id: 'venom', name: 'Venom Fangs', rarity: 'rare', owned: true, equipped: false, icon: 'Target', description: 'Poisonous fangs' },
      { id: 'laser', name: 'Laser Eyes', rarity: 'epic', owned: false, equipped: false, icon: 'Zap', description: 'High-tech laser vision' },
      { id: 'plasma', name: 'Plasma Cannon', rarity: 'legendary', owned: false, equipped: false, icon: 'Crown', description: 'Energy-based weapon' }
    ],
    powerups: [
      { id: 'speed', name: 'Speed Boost', rarity: 'common', owned: true, equipped: true, icon: 'Zap', description: 'Increases movement speed' },
      { id: 'shield', name: 'Energy Shield', rarity: 'rare', owned: true, equipped: false, icon: 'Shield', description: 'Temporary invincibility' },
      { id: 'magnet', name: 'Food Magnet', rarity: 'epic', owned: false, equipped: false, icon: 'Target', description: 'Attracts nearby food' },
      { id: 'ghost', name: 'Ghost Mode', rarity: 'legendary', owned: false, equipped: false, icon: 'Users', description: 'Phase through walls' }
    ],
    emotes: [
      { id: 'victory', name: 'Victory Dance', rarity: 'common', owned: true, equipped: true, icon: 'Trophy', description: 'Celebrate your wins' },
      { id: 'taunt', name: 'Intimidate', rarity: 'rare', owned: true, equipped: false, icon: 'Smile', description: 'Taunt your opponents' },
      { id: 'laugh', name: 'Evil Laugh', rarity: 'epic', owned: false, equipped: false, icon: 'Crown', description: 'Sinister laughter' },
      { id: 'roar', name: 'Dragon Roar', rarity: 'legendary', owned: false, equipped: false, icon: 'Medal', description: 'Mighty dragon roar' }
    ],
    trails: [
      { id: 'none', name: 'No Trail', rarity: 'common', owned: true, equipped: true, icon: 'Target', description: 'Clean, no trail' },
      { id: 'fire', name: 'Fire Trail', rarity: 'rare', owned: true, equipped: false, icon: 'Zap', description: 'Leaves a fire trail' },
      { id: 'ice', name: 'Ice Trail', rarity: 'epic', owned: false, equipped: false, icon: 'Sparkles', description: 'Freezing ice trail' },
      { id: 'rainbow', name: 'Rainbow Trail', rarity: 'legendary', owned: false, equipped: false, icon: 'Star', description: 'Colorful rainbow trail' }
    ]
  };

  // Sample shop items data
  const shopItems = {
    skins: [
      { id: 'shop-skin-1', name: 'Neon Viper', rarity: 'epic', price: 0.05, icon: 'Sparkles', description: 'Glowing neon snake skin', discount: 0, featured: true },
      { id: 'shop-skin-2', name: 'Golden Serpent', rarity: 'legendary', price: 0.1, icon: 'Crown', description: 'Luxurious golden snake', discount: 20, featured: false },
      { id: 'shop-skin-3', name: 'Shadow Snake', rarity: 'rare', price: 0.03, icon: 'Target', description: 'Stealthy dark snake', discount: 0, featured: false },
      { id: 'shop-skin-4', name: 'Crystal Cobra', rarity: 'mythic', price: 0.25, icon: 'Star', description: 'Rare crystalline snake', discount: 0, featured: true }
    ],
    weapons: [
      { id: 'shop-weapon-1', name: 'Plasma Blaster', rarity: 'legendary', price: 0.08, icon: 'Zap', description: 'High-energy plasma weapon', discount: 15, featured: true },
      { id: 'shop-weapon-2', name: 'Venom Injector', rarity: 'epic', price: 0.06, icon: 'Target', description: 'Poisonous weapon upgrade', discount: 0, featured: false },
      { id: 'shop-weapon-3', name: 'Sonic Cannon', rarity: 'rare', price: 0.04, icon: 'Sword', description: 'Sound-based weapon', discount: 10, featured: false },
      { id: 'shop-weapon-4', name: 'Lightning Strike', rarity: 'mythic', price: 0.2, icon: 'Crown', description: 'Ultimate electric weapon', discount: 0, featured: false }
    ],
    powerups: [
      { id: 'shop-powerup-1', name: 'Speed Boost Pack', rarity: 'common', price: 0.01, icon: 'Zap', description: '5x Speed boost powerups', discount: 0, featured: false },
      { id: 'shop-powerup-2', name: 'Shield Generator', rarity: 'rare', price: 0.03, icon: 'Shield', description: '3x Energy shield powerups', discount: 25, featured: true },
      { id: 'shop-powerup-3', name: 'Magnet Field', rarity: 'epic', price: 0.05, icon: 'Target', description: '2x Food magnet powerups', discount: 0, featured: false },
      { id: 'shop-powerup-4', name: 'Ghost Mode Kit', rarity: 'legendary', price: 0.12, icon: 'Users', description: '1x Ghost mode powerup', discount: 0, featured: false }
    ],
    emotes: [
      { id: 'shop-emote-1', name: 'Victory Celebration', rarity: 'rare', price: 0.02, icon: 'Trophy', description: 'Epic victory dance', discount: 0, featured: false },
      { id: 'shop-emote-2', name: 'Taunt Master', rarity: 'epic', price: 0.04, icon: 'Smile', description: 'Ultimate taunt emote', discount: 30, featured: true },
      { id: 'shop-emote-3', name: 'Roar of Power', rarity: 'legendary', price: 0.07, icon: 'Medal', description: 'Intimidating roar', discount: 0, featured: false },
      { id: 'shop-emote-4', name: 'Mystic Gesture', rarity: 'mythic', price: 0.15, icon: 'Crown', description: 'Rare mystical emote', discount: 0, featured: false }
    ],
    trails: [
      { id: 'shop-trail-1', name: 'Fire Trail Pro', rarity: 'rare', price: 0.025, icon: 'Zap', description: 'Enhanced fire trail', discount: 0, featured: false },
      { id: 'shop-trail-2', name: 'Ice Crystal Trail', rarity: 'epic', price: 0.045, icon: 'Sparkles', description: 'Freezing ice crystals', discount: 20, featured: true },
      { id: 'shop-trail-3', name: 'Rainbow Spectrum', rarity: 'legendary', price: 0.09, icon: 'Star', description: 'Full rainbow trail', discount: 0, featured: false },
      { id: 'shop-trail-4', name: 'Cosmic Dust', rarity: 'mythic', price: 0.18, icon: 'Crown', description: 'Galactic trail effect', discount: 0, featured: false }
    ],
    bundles: [
      { id: 'shop-bundle-1', name: 'Starter Pack', rarity: 'common', price: 0.08, icon: 'Package', description: 'Basic skin + weapon + powerups', discount: 40, featured: true },
      { id: 'shop-bundle-2', name: 'Warrior Bundle', rarity: 'epic', price: 0.15, icon: 'Sword', description: 'Epic skin + legendary weapon + emotes', discount: 35, featured: true },
      { id: 'shop-bundle-3', name: 'Ultimate Collection', rarity: 'legendary', price: 0.3, icon: 'Crown', description: 'All premium items included', discount: 50, featured: true },
      { id: 'shop-bundle-4', name: 'Mystic Master Pack', rarity: 'mythic', price: 0.5, icon: 'Star', description: 'Exclusive mythic items bundle', discount: 25, featured: false }
    ]
  };

  // Sample leaderboard data
  const leaderboardData = {
    global: {
      wins: [
        { rank: 1, username: 'ViperKing', wins: 2847, level: 89, avatar: '👑', country: 'US', streak: 23, earnings: 45.67 },
        { rank: 2, username: 'CobraStrike', wins: 2756, level: 87, avatar: '🐍', country: 'UK', streak: 18, earnings: 42.34 },
        { rank: 3, username: 'PythonMaster', wins: 2689, level: 85, avatar: '🏆', country: 'CA', streak: 15, earnings: 39.82 },
        { rank: 4, username: 'SnakeCharmer', wins: 2634, level: 84, avatar: '🎭', country: 'DE', streak: 12, earnings: 38.91 },
        { rank: 5, username: 'VenomousViper', wins: 2598, level: 83, avatar: '☠️', country: 'FR', streak: 9, earnings: 37.45 },
        { rank: 6, username: 'SerpentLord', wins: 2567, level: 82, avatar: '⚡', country: 'JP', streak: 7, earnings: 36.78 },
        { rank: 7, username: 'RattlesnakeRex', wins: 2534, level: 81, avatar: '🔥', country: 'AU', streak: 5, earnings: 35.92 },
        { rank: 8, username: 'AnacondaAce', wins: 2501, level: 80, avatar: '💎', country: 'BR', streak: 4, earnings: 34.67 },
        { rank: 9, username: 'BoaConstrictor', wins: 2478, level: 79, avatar: '🌟', country: 'KR', streak: 3, earnings: 33.89 },
        { rank: 10, username: userData.username || 'You', wins: 1247, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      score: [
        { rank: 1, username: 'ScoreMaster', score: 1847293, level: 92, avatar: '🏆', country: 'US', streak: 15, earnings: 52.34 },
        { rank: 2, username: 'PointHunter', score: 1789456, level: 90, avatar: '🎯', country: 'UK', streak: 12, earnings: 48.92 },
        { rank: 3, username: 'HighScoreHero', score: 1734821, level: 88, avatar: '⭐', country: 'CA', streak: 10, earnings: 45.67 },
        { rank: 4, username: 'RecordBreaker', score: 1698734, level: 87, avatar: '💫', country: 'DE', streak: 8, earnings: 43.21 },
        { rank: 5, username: 'ScoreSlayer', score: 1645892, level: 85, avatar: '🔥', country: 'FR', streak: 6, earnings: 41.78 },
        { rank: 6, username: 'PointPirate', score: 1598743, level: 84, avatar: '🏴‍☠️', country: 'JP', streak: 5, earnings: 39.45 },
        { rank: 7, username: 'ScoreShark', score: 1567234, level: 83, avatar: '🦈', country: 'AU', streak: 4, earnings: 37.89 },
        { rank: 8, username: 'NumberNinja', score: 1534567, level: 82, avatar: '🥷', country: 'BR', streak: 3, earnings: 36.12 },
        { rank: 9, username: 'DigitDominator', score: 1498765, level: 81, avatar: '👑', country: 'KR', streak: 2, earnings: 34.67 },
        { rank: 10, username: userData.username || 'You', score: 847293, level: 45, avatar: '🎮', country: 'US', streak: 1, earnings: 18.45 }
      ],
      kills: [
        { rank: 1, username: 'KillStreakKing', kills: 15847, level: 94, avatar: '⚔️', country: 'US', streak: 28, earnings: 58.92 },
        { rank: 2, username: 'DeathDealer', kills: 15234, level: 92, avatar: '💀', country: 'UK', streak: 25, earnings: 55.67 },
        { rank: 3, username: 'SlayerSupreme', kills: 14892, level: 91, avatar: '🗡️', country: 'CA', streak: 22, earnings: 52.34 },
        { rank: 4, username: 'CombatChampion', kills: 14567, level: 90, avatar: '🛡️', country: 'DE', streak: 19, earnings: 49.78 },
        { rank: 5, username: 'WarriorWolf', kills: 14234, level: 89, avatar: '🐺', country: 'FR', streak: 16, earnings: 47.23 },
        { rank: 6, username: 'BattleBeast', kills: 13987, level: 88, avatar: '🦁', country: 'JP', streak: 14, earnings: 45.89 },
        { rank: 7, username: 'FightingFalcon', kills: 13756, level: 87, avatar: '🦅', country: 'AU', streak: 12, earnings: 43.45 },
        { rank: 8, username: 'CombatCobra', kills: 13523, level: 86, avatar: '🐍', country: 'BR', streak: 10, earnings: 41.67 },
        { rank: 9, username: 'WarMachine', kills: 13298, level: 85, avatar: '🤖', country: 'KR', streak: 8, earnings: 39.89 },
        { rank: 10, username: userData.username || 'You', kills: 7847, level: 45, avatar: '🎮', country: 'US', streak: 3, earnings: 18.45 }
      ],
      survival: [
        { rank: 1, username: 'SurvivalSage', survivalTime: '2h 47m', level: 88, avatar: '🕰️', country: 'US', streak: 31, earnings: 41.23 },
        { rank: 2, username: 'EnduranceExpert', survivalTime: '2h 34m', level: 86, avatar: '⏱️', country: 'UK', streak: 28, earnings: 38.67 },
        { rank: 3, username: 'LastManStanding', survivalTime: '2h 28m', level: 85, avatar: '🏃', country: 'CA', streak: 25, earnings: 36.89 },
        { rank: 4, username: 'TimeKeeper', survivalTime: '2h 19m', level: 84, avatar: '⌚', country: 'DE', streak: 22, earnings: 35.12 },
        { rank: 5, username: 'ClockMaster', survivalTime: '2h 12m', level: 83, avatar: '🕐', country: 'FR', streak: 19, earnings: 33.45 },
        { rank: 6, username: 'DurationDuke', survivalTime: '2h 8m', level: 82, avatar: '👑', country: 'JP', streak: 17, earnings: 32.78 },
        { rank: 7, username: 'PersistencePro', survivalTime: '2h 3m', level: 81, avatar: '💪', country: 'AU', streak: 15, earnings: 31.23 },
        { rank: 8, username: 'StaminaStorm', survivalTime: '1h 58m', level: 80, avatar: '⚡', country: 'BR', streak: 13, earnings: 29.67 },
        { rank: 9, username: 'EndlessEnergy', survivalTime: '1h 54m', level: 79, avatar: '🔋', country: 'KR', streak: 11, earnings: 28.45 },
        { rank: 10, username: userData.username || 'You', survivalTime: '1h 12m', level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      earnings: [
        { rank: 1, username: 'CryptoKing', earnings: 127.89, level: 96, avatar: '💰', country: 'US', streak: 45, wins: 3247 },
        { rank: 2, username: 'SolanaSlayer', earnings: 118.45, level: 94, avatar: '🪙', country: 'UK', streak: 42, wins: 3089 },
        { rank: 3, username: 'TokenTitan', earnings: 109.67, level: 92, avatar: '💎', country: 'CA', streak: 38, wins: 2934 },
        { rank: 4, username: 'CoinCollector', earnings: 98.23, level: 90, avatar: '🏆', country: 'DE', streak: 35, wins: 2789 },
        { rank: 5, username: 'WealthWarrior', earnings: 89.56, level: 89, avatar: '👑', country: 'FR', streak: 32, wins: 2645 },
        { rank: 6, username: 'ProfitPython', earnings: 82.34, level: 87, avatar: '🐍', country: 'JP', streak: 29, wins: 2534 },
        { rank: 7, username: 'MoneyMamba', earnings: 76.78, level: 86, avatar: '💸', country: 'AU', streak: 26, wins: 2423 },
        { rank: 8, username: 'CashCobra', earnings: 71.23, level: 85, avatar: '🤑', country: 'BR', streak: 23, wins: 2312 },
        { rank: 9, username: 'RichRattler', earnings: 67.45, level: 84, avatar: '💵', country: 'KR', streak: 20, wins: 2234 },
        { rank: 10, username: userData.username || 'You', earnings: 18.45, level: 45, avatar: '🎮', country: 'US', streak: 2, wins: 1247 }
      ]
    },
    friends: {
      wins: [
        { rank: 1, username: 'BestFriend', wins: 1847, level: 67, avatar: '👥', country: 'US', streak: 12, earnings: 28.45 },
        { rank: 2, username: 'GamingBuddy', wins: 1634, level: 62, avatar: '🎮', country: 'CA', streak: 9, earnings: 25.67 },
        { rank: 3, username: 'SnakePartner', wins: 1523, level: 59, avatar: '🐍', country: 'UK', streak: 7, earnings: 23.89 },
        { rank: 4, username: userData.username || 'You', wins: 1247, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 },
        { rank: 5, username: 'CasualPlayer', wins: 987, level: 38, avatar: '😊', country: 'DE', streak: 1, earnings: 15.23 }
      ],
      score: [
        { rank: 1, username: 'BestFriend', score: 847293, level: 67, avatar: '👥', country: 'US', streak: 12, earnings: 28.45 },
        { rank: 2, username: 'GamingBuddy', score: 734821, level: 62, avatar: '🎮', country: 'CA', streak: 9, earnings: 25.67 },
        { rank: 3, username: 'SnakePartner', score: 698734, level: 59, avatar: '🐍', country: 'UK', streak: 7, earnings: 23.89 },
        { rank: 4, username: userData.username || 'You', score: 547293, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      kills: [
        { rank: 1, username: 'BestFriend', kills: 7847, level: 67, avatar: '👥', country: 'US', streak: 12, earnings: 28.45 },
        { rank: 2, username: 'GamingBuddy', kills: 6234, level: 62, avatar: '🎮', country: 'CA', streak: 9, earnings: 25.67 },
        { rank: 3, username: 'SnakePartner', kills: 5892, level: 59, avatar: '🐍', country: 'UK', streak: 7, earnings: 23.89 },
        { rank: 4, username: userData.username || 'You', kills: 4847, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      survival: [
        { rank: 1, username: 'BestFriend', survivalTime: '1h 47m', level: 67, avatar: '👥', country: 'US', streak: 12, earnings: 28.45 },
        { rank: 2, username: 'GamingBuddy', survivalTime: '1h 34m', level: 62, avatar: '🎮', country: 'CA', streak: 9, earnings: 25.67 },
        { rank: 3, username: 'SnakePartner', survivalTime: '1h 23m', level: 59, avatar: '🐍', country: 'UK', streak: 7, earnings: 23.89 },
        { rank: 4, username: userData.username || 'You', survivalTime: '1h 12m', level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      earnings: [
        { rank: 1, username: 'BestFriend', earnings: 28.45, level: 67, avatar: '👥', country: 'US', streak: 12, wins: 1847 },
        { rank: 2, username: 'GamingBuddy', earnings: 25.67, level: 62, avatar: '🎮', country: 'CA', streak: 9, wins: 1634 },
        { rank: 3, username: 'SnakePartner', earnings: 23.89, level: 59, avatar: '🐍', country: 'UK', streak: 7, wins: 1523 },
        { rank: 4, username: userData.username || 'You', earnings: 18.45, level: 45, avatar: '🎮', country: 'US', streak: 2, wins: 1247 }
      ]
    },
    weekly: {
      wins: [
        { rank: 1, username: 'WeeklyChamp', wins: 147, level: 78, avatar: '🏆', country: 'US', streak: 8, earnings: 12.45 },
        { rank: 2, username: 'WeekWarrior', wins: 134, level: 72, avatar: '⚔️', country: 'UK', streak: 6, earnings: 10.67 },
        { rank: 3, username: 'SevenDaySlayer', wins: 123, level: 69, avatar: '🗡️', country: 'CA', streak: 5, earnings: 9.89 },
        { rank: 4, username: userData.username || 'You', wins: 87, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 6.45 }
      ],
      score: [
        { rank: 1, username: 'WeeklyChamp', score: 147293, level: 78, avatar: '🏆', country: 'US', streak: 8, earnings: 12.45 },
        { rank: 2, username: 'WeekWarrior', score: 134821, level: 72, avatar: '⚔️', country: 'UK', streak: 6, earnings: 10.67 },
        { rank: 3, username: 'SevenDaySlayer', score: 123734, level: 69, avatar: '🗡️', country: 'CA', streak: 5, earnings: 9.89 },
        { rank: 4, username: userData.username || 'You', score: 87293, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 6.45 }
      ],
      kills: [
        { rank: 1, username: 'WeeklyChamp', kills: 1847, level: 78, avatar: '🏆', country: 'US', streak: 8, earnings: 12.45 },
        { rank: 2, username: 'WeekWarrior', kills: 1634, level: 72, avatar: '⚔️', country: 'UK', streak: 6, earnings: 10.67 },
        { rank: 3, username: 'SevenDaySlayer', kills: 1523, level: 69, avatar: '🗡️', country: 'CA', streak: 5, earnings: 9.89 },
        { rank: 4, username: userData.username || 'You', kills: 1247, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 6.45 }
      ],
      survival: [
        { rank: 1, username: 'WeeklyChamp', survivalTime: '12h 47m', level: 78, avatar: '🏆', country: 'US', streak: 8, earnings: 12.45 },
        { rank: 2, username: 'WeekWarrior', survivalTime: '11h 34m', level: 72, avatar: '⚔️', country: 'UK', streak: 6, earnings: 10.67 },
        { rank: 3, username: 'SevenDaySlayer', survivalTime: '10h 23m', level: 69, avatar: '🗡️', country: 'CA', streak: 5, earnings: 9.89 },
        { rank: 4, username: userData.username || 'You', survivalTime: '8h 12m', level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 6.45 }
      ],
      earnings: [
        { rank: 1, username: 'WeeklyChamp', earnings: 12.45, level: 78, avatar: '🏆', country: 'US', streak: 8, wins: 147 },
        { rank: 2, username: 'WeekWarrior', earnings: 10.67, level: 72, avatar: '⚔️', country: 'UK', streak: 6, wins: 134 },
        { rank: 3, username: 'SevenDaySlayer', earnings: 9.89, level: 69, avatar: '🗡️', country: 'CA', streak: 5, wins: 123 },
        { rank: 4, username: userData.username || 'You', earnings: 6.45, level: 45, avatar: '🎮', country: 'US', streak: 2, wins: 87 }
      ]
    },
    monthly: {
      wins: [
        { rank: 1, username: 'MonthlyMaster', wins: 547, level: 85, avatar: '👑', country: 'US', streak: 15, earnings: 45.67 },
        { rank: 2, username: 'MonthWarrior', wins: 489, level: 82, avatar: '🛡️', country: 'UK', streak: 12, earnings: 38.92 },
        { rank: 3, username: 'ThirtyDayTitan', wins: 456, level: 80, avatar: '⚡', country: 'CA', streak: 10, earnings: 35.23 },
        { rank: 4, username: userData.username || 'You', wins: 287, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      score: [
        { rank: 1, username: 'MonthlyMaster', score: 547293, level: 85, avatar: '👑', country: 'US', streak: 15, earnings: 45.67 },
        { rank: 2, username: 'MonthWarrior', score: 489821, level: 82, avatar: '🛡️', country: 'UK', streak: 12, earnings: 38.92 },
        { rank: 3, username: 'ThirtyDayTitan', score: 456734, level: 80, avatar: '⚡', country: 'CA', streak: 10, earnings: 35.23 },
        { rank: 4, username: userData.username || 'You', score: 287293, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      kills: [
        { rank: 1, username: 'MonthlyMaster', kills: 5847, level: 85, avatar: '👑', country: 'US', streak: 15, earnings: 45.67 },
        { rank: 2, username: 'MonthWarrior', kills: 4892, level: 82, avatar: '🛡️', country: 'UK', streak: 12, earnings: 38.92 },
        { rank: 3, username: 'ThirtyDayTitan', kills: 4567, level: 80, avatar: '⚡', country: 'CA', streak: 10, earnings: 35.23 },
        { rank: 4, username: userData.username || 'You', kills: 2847, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      survival: [
        { rank: 1, username: 'MonthlyMaster', survivalTime: '47h 23m', level: 85, avatar: '👑', country: 'US', streak: 15, earnings: 45.67 },
        { rank: 2, username: 'MonthWarrior', survivalTime: '42h 18m', level: 82, avatar: '🛡️', country: 'UK', streak: 12, earnings: 38.92 },
        { rank: 3, username: 'ThirtyDayTitan', survivalTime: '38h 45m', level: 80, avatar: '⚡', country: 'CA', streak: 10, earnings: 35.23 },
        { rank: 4, username: userData.username || 'You', survivalTime: '28h 12m', level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      earnings: [
        { rank: 1, username: 'MonthlyMaster', earnings: 45.67, level: 85, avatar: '👑', country: 'US', streak: 15, wins: 547 },
        { rank: 2, username: 'MonthWarrior', earnings: 38.92, level: 82, avatar: '🛡️', country: 'UK', streak: 12, wins: 489 },
        { rank: 3, username: 'ThirtyDayTitan', earnings: 35.23, level: 80, avatar: '⚡', country: 'CA', streak: 10, wins: 456 },
        { rank: 4, username: userData.username || 'You', earnings: 18.45, level: 45, avatar: '🎮', country: 'US', streak: 2, wins: 287 }
      ]
    },
    alltime: {
      wins: [
        { rank: 1, username: 'EternalChampion', wins: 8947, level: 99, avatar: '🌟', country: 'US', streak: 45, earnings: 189.67 },
        { rank: 2, username: 'LegendarySnake', wins: 8234, level: 97, avatar: '🐲', country: 'UK', streak: 42, earnings: 167.34 },
        { rank: 3, username: 'ImmortalViper', wins: 7892, level: 95, avatar: '💫', country: 'CA', streak: 38, earnings: 156.89 },
        { rank: 4, username: userData.username || 'You', wins: 1247, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      score: [
        { rank: 1, username: 'EternalChampion', score: 8947293, level: 99, avatar: '🌟', country: 'US', streak: 45, earnings: 189.67 },
        { rank: 2, username: 'LegendarySnake', score: 8234821, level: 97, avatar: '🐲', country: 'UK', streak: 42, earnings: 167.34 },
        { rank: 3, username: 'ImmortalViper', score: 7892734, level: 95, avatar: '💫', country: 'CA', streak: 38, earnings: 156.89 },
        { rank: 4, username: userData.username || 'You', score: 847293, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      kills: [
        { rank: 1, username: 'EternalChampion', kills: 89847, level: 99, avatar: '🌟', country: 'US', streak: 45, earnings: 189.67 },
        { rank: 2, username: 'LegendarySnake', kills: 82234, level: 97, avatar: '🐲', country: 'UK', streak: 42, earnings: 167.34 },
        { rank: 3, username: 'ImmortalViper', kills: 78892, level: 95, avatar: '💫', country: 'CA', streak: 38, earnings: 156.89 },
        { rank: 4, username: userData.username || 'You', kills: 7847, level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      survival: [
        { rank: 1, username: 'EternalChampion', survivalTime: '894h 47m', level: 99, avatar: '🌟', country: 'US', streak: 45, earnings: 189.67 },
        { rank: 2, username: 'LegendarySnake', survivalTime: '823h 34m', level: 97, avatar: '🐲', country: 'UK', streak: 42, earnings: 167.34 },
        { rank: 3, username: 'ImmortalViper', survivalTime: '789h 23m', level: 95, avatar: '💫', country: 'CA', streak: 38, earnings: 156.89 },
        { rank: 4, username: userData.username || 'You', survivalTime: '78h 12m', level: 45, avatar: '🎮', country: 'US', streak: 2, earnings: 18.45 }
      ],
      earnings: [
        { rank: 1, username: 'EternalChampion', earnings: 189.67, level: 99, avatar: '🌟', country: 'US', streak: 45, wins: 8947 },
        { rank: 2, username: 'LegendarySnake', earnings: 167.34, level: 97, avatar: '🐲', country: 'UK', streak: 42, wins: 8234 },
        { rank: 3, username: 'ImmortalViper', earnings: 156.89, level: 95, avatar: '💫', country: 'CA', streak: 38, wins: 7892 },
        { rank: 4, username: userData.username || 'You', earnings: 18.45, level: 45, avatar: '🎮', country: 'US', streak: 2, wins: 1247 }
      ]
    }
  };

  const getLeaderboardData = () => {
    const categoryData = leaderboardData[selectedLeaderboardCategory];
    if (!categoryData) return [];

    const typeData = categoryData[selectedLeaderboardType];
    return typeData || [];
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return '#FFD700'; // Gold
      case 2: return '#C0C0C0'; // Silver
      case 3: return '#CD7F32'; // Bronze
      default: return '#9CA3AF'; // Gray
    }
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return '#9CA3AF';
      case 'rare': return '#3B82F6';
      case 'epic': return '#8B5CF6';
      case 'legendary': return '#F59E0B';
      case 'mythic': return '#EF4444';
      default: return '#9CA3AF';
    }
  };

  const renderIcon = (iconName: string, size: number = 32) => {
    const iconProps = { size, className: "item-icon" };
    switch (iconName) {
      case 'Palette': return <Palette {...iconProps} />;
      case 'Crown': return <Crown {...iconProps} />;
      case 'Target': return <Target {...iconProps} />;
      case 'Star': return <Star {...iconProps} />;
      case 'Sparkles': return <Sparkles {...iconProps} />;
      case 'Zap': return <Zap {...iconProps} />;
      case 'Sword': return <Sword {...iconProps} />;
      case 'Shield': return <Shield {...iconProps} />;
      case 'Users': return <Users {...iconProps} />;
      case 'Trophy': return <Trophy {...iconProps} />;
      case 'Smile': return <Smile {...iconProps} />;
      case 'Medal': return <Medal {...iconProps} />;
      case 'Package': return <Package {...iconProps} />;
      default: return <Target {...iconProps} />;
    }
  };

  const handlePurchase = async (item: any) => {
    if (!userProfile?.id) {
      alert('Please log in to make purchases');
      return;
    }

    if (userProfile.solana_balance < item.price) {
      alert(`Insufficient balance. You need ${item.price} SOL but only have ${userProfile.solana_balance.toFixed(4)} SOL`);
      return;
    }

    setIsPurchasing(item.id);

    try {
      // Here you would integrate with the actual shop purchase API
      // For now, we'll simulate the purchase
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

      setPurchaseSuccess(item.id);
      setTimeout(() => setPurchaseSuccess(null), 3000);

      // In a real implementation, you would:
      // 1. Call the shop purchase API
      // 2. Update user balance
      // 3. Add item to user's inventory
      // 4. Show success message

    } catch (error) {
      console.error('Purchase failed:', error);
      alert('Purchase failed. Please try again.');
    } finally {
      setIsPurchasing(null);
    }
  };

  return (
    <div className="fortnite-lobby-container">
      {/* Apple-style Tab Navigation */}
      <style>{`
        .fortnite-lobby-container {
          background-image: url('/assets/backgrounds/SnakeLavaLairLobbyBackground.png');
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          background-attachment: fixed;
        }

        .apple-tab-nav {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px 30px;
          background: #000000;
          backdrop-filter: blur(20px);
          border-bottom: 3px solid transparent;
          border-image: url("data:image/svg+xml,%3csvg width='100' height='20' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='barbed' x='0' y='0' width='20' height='20' patternUnits='userSpaceOnUse'%3e%3cpath d='M0,10 L5,5 L10,10 L15,5 L20,10 L15,15 L10,10 L5,15 Z' fill='none' stroke='%23ff0080' stroke-width='1'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100' height='20' fill='url(%23barbed)'/%3e%3c/svg%3e") 1;
          position: relative;
        }

        .navbar-logo {
          position: absolute;
          left: 20px;
          top: 50%;
          transform: translateY(-50%);
          z-index: 10;
          padding: 8px 12px;
          background: rgba(0, 0, 0, 0.8);
          border-radius: 8px;
          border: 1px solid rgba(0, 255, 136, 0.3);
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .navbar-logo:hover {
          background: rgba(0, 0, 0, 0.9);
          border-color: rgba(0, 255, 136, 0.5);
          box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
        }

        .navbar-logo:hover .ascii-logo-small {
          text-shadow: 0 0 8px #00ff88;
        }

        .ascii-logo-small {
          font-family: 'Courier New', monospace;
          font-size: 6px;
          line-height: 1.1;
          color: #00ff88;
          text-shadow: 0 0 3px #00ff88;
          white-space: pre;
          margin: 0;
          opacity: 0.9;
          display: block;
        }

        .tab-center-container {
          flex: 1;
          display: flex;
          justify-content: center;
          margin-left: 310px; /* Shift tabs to the right for better centering */
        }

        .apple-tab-nav::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          right: 0;
          height: 2px;
          background: repeating-linear-gradient(
            90deg,
            #ff0080 0px,
            #ff0080 3px,
            transparent 3px,
            transparent 8px,
            #ff0080 8px,
            #ff0080 11px,
            transparent 11px,
            transparent 16px
          );
          background-size: 16px 2px;
          opacity: 0.7;
          filter: drop-shadow(0 0 1px #ff0080);
        }

        .apple-tab-container {
          display: flex;
          background: rgba(255, 255, 255, 0.08);
          border-radius: 12px;
          padding: 4px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.12);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .apple-tab {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 10px 20px;
          border-radius: 8px;
          background: transparent;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          white-space: nowrap;
          min-width: 80px;
          justify-content: center;
        }

        .apple-tab:hover {
          color: rgba(255, 255, 255, 0.9);
          background: rgba(255, 255, 255, 0.05);
        }

        .apple-tab.active {
          background: rgba(255, 255, 255, 0.15);
          color: #ffffff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .apple-tab svg {
          width: 16px;
          height: 16px;
          opacity: 0.8;
        }

        .apple-tab.active svg {
          opacity: 1;
        }

        .nav-right-section {
          display: flex;
          align-items: center;
          gap: 15px;
        }

        .balance-container {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .balance-section {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          transition: all 0.2s ease;
        }

        .balance-section:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
        }

        .clickable-balance {
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .clickable-balance:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(153, 69, 255, 0.3);
        }

        .clickable-balance:active {
          transform: translateY(0);
        }

        .sol-section {
          background: rgba(153, 69, 255, 0.1);
          border-color: rgba(153, 69, 255, 0.3);
        }

        .sol-section:hover {
          background: rgba(153, 69, 255, 0.15);
          border-color: rgba(153, 69, 255, 0.4);
        }

        .usd-section {
          background: rgba(34, 197, 94, 0.1);
          border-color: rgba(34, 197, 94, 0.3);
        }

        .usd-section:hover {
          background: rgba(34, 197, 94, 0.15);
          border-color: rgba(34, 197, 94, 0.4);
        }

        .balance-label {
          font-size: 10px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .sol-label {
          color: #9945FF;
        }

        .usd-label {
          color: #22c55e;
        }

        .balance-value {
          color: #ffffff;
          font-weight: 700;
          font-size: 12px;
        }

        .profile-dropdown-container {
          position: relative;
        }

        .apple-user-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 20px;
          color: #ffffff;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .apple-user-btn:hover {
          background: rgba(255, 255, 255, 0.15);
          box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
        }

        .profile-dropdown {
          position: absolute;
          top: calc(100% + 8px);
          right: 0;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 12px;
          padding: 8px;
          min-width: 160px;
          opacity: 0;
          visibility: hidden;
          transform: translateY(-10px);
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          z-index: 1000;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .profile-dropdown::before {
          content: '';
          position: absolute;
          top: -6px;
          right: 20px;
          width: 12px;
          height: 12px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-bottom: none;
          border-right: none;
          transform: rotate(45deg);
          backdrop-filter: blur(20px);
        }

        .profile-dropdown.visible {
          opacity: 1;
          visibility: visible;
          transform: translateY(0);
        }

        .dropdown-item {
          display: flex;
          align-items: center;
          gap: 10px;
          padding: 10px 12px;
          border-radius: 8px;
          color: rgba(255, 255, 255, 0.8);
          font-size: 13px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          border: none;
          background: transparent;
          width: 100%;
          text-align: left;
        }

        .dropdown-item:hover {
          background: rgba(255, 255, 255, 0.1);
          color: #ffffff;
        }

        .dropdown-item svg {
          opacity: 0.7;
        }

        .dropdown-item:hover svg {
          opacity: 1;
        }

        .user-level {
          background: rgba(0, 255, 136, 0.2);
          color: #00ff88;
          padding: 2px 6px;
          border-radius: 8px;
          font-size: 10px;
          font-weight: 700;
        }

        /* Apple-style Game Mode Card */
        .apple-game-mode-card {
          position: fixed;
          bottom: 20px;
          left: 20px;
          width: 100%;
          max-width: 320px;
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(20px);
          border: 2px solid rgba(255, 255, 255, 0.1);
          border-radius: 15px;
          padding: 0;
          display: block;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: visible;
          z-index: 999;
          height: 200px;
        }

        .apple-game-mode-card.boss-mode {
          background: linear-gradient(135deg,
            rgba(255, 215, 0, 0.2) 0%,
            rgba(255, 140, 0, 0.15) 25%,
            rgba(138, 43, 226, 0.2) 50%,
            rgba(75, 0, 130, 0.15) 75%,
            rgba(0, 0, 0, 0.3) 100%
          );
          border: 3px solid transparent;
          background-clip: padding-box;
          animation: bossCardPulse 3s ease-in-out infinite;
        }

        .apple-game-mode-card.boss-mode::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(45deg,
            #ffd700, #ff8c00, #8a2be2, #4b0082, #000000,
            #4b0082, #8a2be2, #ff8c00, #ffd700
          );
          background-size: 400% 400%;
          border-radius: 17px;
          z-index: -1;
          animation: borderGlow 2s ease-in-out infinite;
        }

        .apple-game-mode-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
          border-radius: 20px;
          pointer-events: none;
        }

        .apple-game-mode-card:hover {
          background: rgba(255, 255, 255, 0.12);
          border-color: #ff0080;
          transform: translateY(-5px);
          box-shadow: 0 10px 30px rgba(255, 0, 128, 0.3);
        }

        .apple-game-mode-card .game-mode-image {
          width: 100%;
          height: 200px;
          border-radius: 15px;
          overflow: hidden;
          position: relative;
        }

        .apple-game-mode-card .game-mode-landscape-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .apple-game-mode-card:hover .game-mode-landscape-image {
          transform: scale(1.05);
        }

        .apple-game-mode-card .game-mode-title {
          position: absolute;
          bottom: 15px;
          left: 15px;
          font-size: 16px;
          font-weight: 700;
          color: #ffffff;
          letter-spacing: -0.3px;
          line-height: 1.2;
          text-shadow: 0 2px 8px rgba(0, 0, 0, 0.8);
          z-index: 2;
        }

        .apple-game-mode-card.boss-mode .game-mode-title {
          background: linear-gradient(45deg, #ffd700, #ff8c00, #8a2be2);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          font-weight: 900;
          font-size: 18px;
          text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
          animation: titleGlow 2s ease-in-out infinite alternate;
        }

        .current-mode-header {
          display: none;
        }

        .current-mode-icon {
          display: none;
        }

        .current-mode-info {
          flex: 1;
          min-width: 0;
        }

        .current-mode-title {
          font-size: 18px;
          font-weight: 700;
          color: #ffffff;
          margin-bottom: 15px;
          letter-spacing: -0.3px;
          line-height: 1.2;
          text-align: center;
        }

        .current-mode-subtitle {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
          font-weight: 500;
          line-height: 1.2;
          text-align: center;
          margin-bottom: 10px;
        }

        .apple-game-mode-card .game-mode-stats {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7), transparent);
          padding: 15px;
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          transform: translateY(100%);
          transition: transform 0.3s ease;
          border-radius: 0 0 15px 15px;
          z-index: 3;
        }

        .apple-game-mode-card:hover .game-mode-stats {
          transform: translateY(0);
        }

        .apple-game-mode-card .game-mode-stat {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 0.75em;
          color: rgba(255, 255, 255, 0.9);
          background: rgba(255, 255, 255, 0.1);
          padding: 4px 8px;
          border-radius: 12px;
          backdrop-filter: blur(10px);
        }

        .apple-game-mode-card .game-mode-stat svg {
          color: rgba(255, 255, 255, 0.8);
          flex-shrink: 0;
        }

        .mode-status-indicator {
          position: absolute;
          bottom: 15px;
          right: 15px;
          background: rgba(0, 255, 0, 0.2);
          border: 2px solid #00ff00;
          border-radius: 20px;
          padding: 6px 12px;
          font-size: 0.75em;
          font-weight: 600;
          color: #00ff00;
          text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
          box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
          z-index: 4;
          animation: readyPulse 2s ease-in-out infinite;
        }

        .mode-status-indicator.not-ready {
          background: rgba(255, 165, 0, 0.2);
          border-color: #ffa500;
          color: #ffa500;
          text-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
          box-shadow: 0 0 15px rgba(255, 165, 0, 0.3);
          animation: none;
        }

        @keyframes readyPulse {
          0%, 100% {
            box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
            transform: scale(1);
          }
          50% {
            box-shadow: 0 0 25px rgba(0, 255, 0, 0.6);
            transform: scale(1.05);
          }
        }
        }

        .mode-rating-section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          padding: 8px 12px;
          position: relative;
          z-index: 1;
        }

        .mode-rating {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .mode-rating-stars {
          color: #ffd700;
          font-size: 12px;
        }

        .mode-rating-text {
          font-size: 12px;
          font-weight: 700;
          color: #ffd700;
        }

        .mode-status {
          font-size: 9px;
          font-weight: 600;
          color: rgba(0, 255, 136, 0.8);
          text-transform: uppercase;
          letter-spacing: 0.3px;
        }

        .apple-ready-button {
          background: linear-gradient(135deg, #007AFF, #0056CC);
          border: none;
          border-radius: 10px;
          padding: 12px 16px;
          color: #ffffff;
          font-size: 14px;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          position: relative;
          z-index: 1;
          box-shadow: 0 3px 12px rgba(0, 122, 255, 0.3);
        }

        .apple-ready-button:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(0, 122, 255, 0.4);
          background: linear-gradient(135deg, #0084FF, #005FD6);
        }

        .apple-ready-button:active:not(:disabled) {
          transform: translateY(0);
          box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
        }

        .apple-ready-button:disabled {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.4);
          cursor: not-allowed;
          box-shadow: none;
        }

        .ready-button-text {
          font-weight: 700;
          letter-spacing: -0.3px;
        }

        /* Apple-style Snake Setup Card */
        .apple-setup-card {
          position: fixed;
          bottom: 20px;
          right: 20px;
          width: 280px;
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.12);
          border-radius: 16px;
          padding: 16px;
          display: flex;
          flex-direction: column;
          gap: 12px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          z-index: 998;
        }

        .apple-setup-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
          border-radius: 20px;
          pointer-events: none;
        }

        .setup-card-header {
          position: relative;
          z-index: 1;
        }

        .setup-card-title {
          font-size: 15px;
          font-weight: 700;
          color: #ffffff;
          margin-bottom: 2px;
          letter-spacing: -0.3px;
          line-height: 1.2;
        }

        .setup-card-subtitle {
          font-size: 11px;
          color: rgba(255, 255, 255, 0.6);
          font-weight: 500;
          line-height: 1.2;
        }

        .apple-input-group {
          position: relative;
          z-index: 1;
        }

        .apple-input-label {
          display: block;
          font-size: 11px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 6px;
          letter-spacing: -0.1px;
        }

        .apple-input {
          width: 100%;
          padding: 10px 12px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 10px;
          color: #ffffff;
          font-size: 13px;
          font-weight: 500;
          outline: none;
          transition: all 0.2s ease;
          letter-spacing: -0.2px;
        }

        .apple-input:focus {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(0, 122, 255, 0.6);
          box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .apple-input::placeholder {
          color: rgba(255, 255, 255, 0.4);
        }

        .wager-section {
          position: relative;
          z-index: 1;
        }

        .wager-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 6px;
          margin-top: 6px;
        }

        .wager-option {
          padding: 8px 10px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          color: rgba(255, 255, 255, 0.7);
          font-size: 11px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 30px;
        }

        .wager-option:hover {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.9);
        }

        .wager-option.selected {
          background: rgba(0, 122, 255, 0.2);
          border-color: rgba(0, 122, 255, 0.4);
          color: #007AFF;
        }

        .admit-pit-button {
          background: linear-gradient(135deg, #FF3B30, #D70015);
          border: none;
          border-radius: 10px;
          padding: 10px 16px;
          color: #ffffff;
          font-size: 13px;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          position: relative;
          z-index: 1;
          box-shadow: 0 2px 8px rgba(255, 59, 48, 0.3);
          margin-top: 4px;
        }

        .admit-pit-button:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 59, 48, 0.4);
          background: linear-gradient(135deg, #FF453A, #E0001A);
        }

        .admit-pit-button:active:not(:disabled) {
          transform: translateY(0);
          box-shadow: 0 1px 4px rgba(255, 59, 48, 0.3);
        }

        .admit-pit-button:disabled {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.4);
          cursor: not-allowed;
          box-shadow: none;
        }

        /* Game Modes Panel */
        .game-modes-panel {
          position: fixed;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.95);
          backdrop-filter: blur(20px);
          border-top: 3px solid #ff0080;
          transform: translateY(100%);
          transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), max-height 0.2s ease;
          z-index: 1000;
          max-height: 70vh;
          display: flex;
          flex-direction: column;
        }

        .game-modes-panel.visible {
          transform: translateY(0);
        }

        .game-modes-panel.scrolled {
          transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), max-height 0.1s ease;
        }

        .game-modes-close-chevron {
          position: absolute;
          top: -20px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(0, 0, 0, 0.9);
          border: 2px solid #ff0080;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          z-index: 1001;
          color: #ff0080;
          backdrop-filter: blur(10px);
          gap: -4px;
        }

        .game-modes-close-chevron:hover {
          background: rgba(255, 0, 128, 0.1);
          color: white;
          border-color: white;
          transform: translateX(-50%) scale(1.1);
        }

        .game-modes-close-chevron svg:first-child {
          margin-bottom: -8px;
        }

        .game-modes-pull-up {
          position: fixed;
          bottom: 30px;
          left: 50%;
          transform: translateX(-50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          z-index: 999;
          transition: all 0.3s ease;
        }

        .game-modes-pull-up:hover {
          transform: translateX(-50%) translateY(-5px);
        }

        .pull-up-chevron {
          background: rgba(0, 0, 0, 0.8);
          border: 2px solid #ff0080;
          border-radius: 50%;
          width: 50px;
          height: 50px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #ff0080;
          animation: bounce 2s infinite;
          backdrop-filter: blur(10px);
          gap: -4px;
        }

        .pull-up-chevron svg:first-child {
          margin-bottom: -8px;
        }

        .pull-up-text {
          color: white;
          font-size: 14px;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
          letter-spacing: 0.5px;
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-8px);
          }
          60% {
            transform: translateY(-4px);
          }
        }

        .game-modes-header {
          padding: 50px 30px 20px 30px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          display: flex;
          align-items: center;
          gap: 20px;
          flex-shrink: 0;
        }

        .search-container {
          flex: 1;
          position: relative;
          max-width: 400px;
        }

        .search-input {
          width: 100%;
          padding: 12px 16px 12px 45px;
          background: rgba(255, 255, 255, 0.1);
          border: 2px solid rgba(255, 255, 255, 0.2);
          border-radius: 25px;
          color: #ffffff;
          font-size: 14px;
          outline: none;
          transition: all 0.3s ease;
        }

        .search-input:focus {
          border-color: #ff0080;
          box-shadow: 0 0 15px rgba(255, 0, 128, 0.3);
        }

        .search-input::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        .search-icon {
          position: absolute;
          left: 15px;
          top: 50%;
          transform: translateY(-50%);
          color: rgba(255, 255, 255, 0.5);
        }

        .filter-container {
          display: flex;
          gap: 10px;
          align-items: center;
        }

        .filter-btn {
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.1);
          border: 2px solid transparent;
          border-radius: 20px;
          color: rgba(255, 255, 255, 0.7);
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .filter-btn:hover {
          background: rgba(255, 255, 255, 0.15);
          color: #ffffff;
        }

        .filter-btn.active {
          background: rgba(255, 0, 128, 0.2);
          border-color: #ff0080;
          color: #ff0080;
        }

        .game-modes-grid {
          flex: 1;
          overflow-y: auto;
          padding: 20px 30px;
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 20px;
        }

        .game-mode-card {
          background: rgba(255, 255, 255, 0.08);
          border: 2px solid rgba(255, 255, 255, 0.1);
          border-radius: 15px;
          padding: 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          min-height: 450px;
          display: flex;
          flex-direction: column;
        }

        .game-mode-card:hover {
          background: rgba(255, 255, 255, 0.12);
          border-color: #ff0080;
          transform: translateY(-5px);
          box-shadow: 0 10px 30px rgba(255, 0, 128, 0.3);
        }

        .game-mode-card.selected {
          background: rgba(255, 0, 128, 0.15);
          border-color: #ff0080;
          box-shadow: 0 0 20px rgba(255, 0, 128, 0.4);
        }

        .game-mode-image {
          width: 100%;
          height: 350px;
          margin-bottom: 15px;
          border-radius: 8px;
          overflow: hidden;
          position: relative;
        }

        .game-mode-landscape-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
          transition: transform 0.3s ease;
        }

        .game-mode-card:hover .game-mode-landscape-image {
          transform: scale(1.05);
        }

        /* LEVIATHON Boss Battle Card Styles */
        .game-mode-card.boss-mode {
          background: linear-gradient(135deg,
            rgba(255, 215, 0, 0.2) 0%,
            rgba(255, 140, 0, 0.15) 25%,
            rgba(138, 43, 226, 0.2) 50%,
            rgba(75, 0, 130, 0.15) 75%,
            rgba(0, 0, 0, 0.3) 100%
          );
          border: 3px solid transparent;
          background-clip: padding-box;
          position: relative;
          overflow: visible;
          animation: bossCardPulse 3s ease-in-out infinite;
          z-index: 5;
        }

        .game-mode-card.boss-mode::before {
          content: '';
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(45deg,
            #ffd700, #ff8c00, #8a2be2, #4b0082, #000000,
            #4b0082, #8a2be2, #ff8c00, #ffd700
          );
          background-size: 400% 400%;
          border-radius: 17px;
          z-index: -1;
          animation: borderGlow 2s ease-in-out infinite;
        }

        .game-mode-card.boss-mode .game-mode-title {
          background: linear-gradient(45deg, #ffd700, #ff8c00, #8a2be2);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          font-weight: 900;
          font-size: 1.4em;
          text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
          animation: titleGlow 2s ease-in-out infinite alternate;
        }

        .boss-prize-badge {
          position: absolute;
          top: -15px;
          width:100%;
          text-align:center;
          left: 50%;
          transform: translateX(-50%);
          background: linear-gradient(45deg, #ffd700, #ff8c00);
          color: #000;
          padding: 10px 16px;
          border-radius: 25px;
          font-weight: 900;
          font-size: 0.9em;
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
          animation: prizeGlow 1.5s ease-in-out infinite alternate;
          z-index: 10;
          border: 3px solid #000;
        }

        .lightning-effect {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
          overflow: hidden;
          border-radius: 15px;
        }

        .lightning-bolt {
          position: absolute;
          width: 2px;
          background: linear-gradient(to bottom, #fff, #ffd700, transparent);
          animation: lightning 0.8s ease-in-out infinite;
          opacity: 0;
        }

        .lightning-bolt:nth-child(1) {
          left: 20%;
          height: 60%;
          animation-delay: 0s;
        }

        .lightning-bolt:nth-child(2) {
          right: 25%;
          height: 80%;
          animation-delay: 0.3s;
        }

        .lightning-bolt:nth-child(3) {
          left: 60%;
          height: 70%;
          animation-delay: 0.6s;
        }

        @keyframes bossCardPulse {
          0%, 100% { transform: scale(1); box-shadow: 0 0 30px rgba(255, 215, 0, 0.3); }
          50% { transform: scale(1.02); box-shadow: 0 0 50px rgba(255, 215, 0, 0.6); }
        }

        @keyframes borderGlow {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }

        @keyframes titleGlow {
          0% { text-shadow: 0 0 20px rgba(255, 215, 0, 0.5); }
          100% { text-shadow: 0 0 30px rgba(255, 215, 0, 0.8), 0 0 40px rgba(138, 43, 226, 0.4); }
        }

        @keyframes prizeGlow {
          0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.6); }
          100% { box-shadow: 0 0 30px rgba(255, 215, 0, 0.9), 0 0 40px rgba(255, 140, 0, 0.5); }
        }

        @keyframes lightning {
          0% { opacity: 0; transform: translateY(-100%); }
          10% { opacity: 1; transform: translateY(0%); }
          20% { opacity: 0; transform: translateY(100%); }
          100% { opacity: 0; transform: translateY(100%); }
        }

        .game-mode-icon-fallback {
          font-size: 3em;
          display: block;
          text-align: center;
          line-height: 350px;
        }

        .current-mode-landscape-image {
          width: 48px;
          height: 48px;
          object-fit: cover;
          border-radius: 8px;
        }

        .current-mode-icon-fallback {
          font-size: 24px;
          display: block;
          text-align: center;
          line-height: 48px;
          width: 48px;
          height: 48px;
        }

        .game-mode-title {
          font-size: 1.2em;
          font-weight: 700;
          color: #ffffff;
          margin-bottom: 8px;
          margin-top: 10px;
        }

        .game-mode-stats {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-top: auto;
          padding-top: 10px;
        }

        .game-mode-stat {
          display: flex;
          align-items: center;
          gap: 5px;
          font-size: 0.8em;
          color: rgba(255, 255, 255, 0.7);
          background: rgba(255, 255, 255, 0.05);
          padding: 4px 8px;
          border-radius: 12px;
        }

        .game-mode-rating {
          display: flex;
          align-items: center;
          gap: 5px;
          color: #ffd700;
          font-weight: 600;
        }



        @media (max-width: 1024px) {
          .apple-tab-nav {
            flex-direction: column;
            gap: 15px;
            padding: 15px 20px;
          }

          .navbar-logo {
            position: static;
            transform: none;
            align-self: center;
            padding: 6px 8px;
          }

          .ascii-logo-small {
            font-size: 4px;
            line-height: 1.2;
          }

          .tab-center-container {
            order: 2;
          }

          .nav-right-section {
            order: 3;
            justify-content: center;
          }
        }

        /* Lobby Layout Styles */
        .lobby-main-content {
          display: grid;
          grid-template-columns: 1fr 2fr 1fr;
          grid-template-rows: auto 1fr;
          gap: 30px;
          padding: 30px;
          height: calc(100vh - 80px);
          overflow: hidden;
        }

        .lobby-top-left-section {
          grid-column: 1;
          grid-row: 1;
          display: flex;
          justify-content: flex-start;
          margin-bottom: 20px;
        }

        .lobby-news-slider {
          width: 100%;
          max-width: 320px;
        }

        .lobby-setup-card {
          grid-column: 1;
          grid-row: 2;
          align-self: start;
        }

        .lobby-center-area {
          grid-column: 2;
          grid-row: 1 / 3;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
        }

        .lobby-game-mode-card {
          grid-column: 3;
          grid-row: 2;
          align-self: start;
        }

        @media (max-width: 768px) {
          .apple-tab-container {
            flex-wrap: wrap;
            justify-content: center;
            gap: 4px;
          }

          .apple-tab {
            padding: 8px 12px;
            font-size: 12px;
            min-width: 60px;
          }

          .apple-tab svg {
            width: 14px;
            height: 14px;
          }

          .game-modes-header {
            flex-direction: column;
            gap: 15px;
            padding: 15px 20px;
          }

          .search-container {
            max-width: none;
          }

          .filter-container {
            flex-wrap: wrap;
            justify-content: center;
          }

          .game-modes-grid {
            grid-template-columns: 1fr;
            padding: 15px 20px;
            gap: 15px;
          }

          .apple-game-mode-card {
            position: fixed;
            bottom: 10px;
            left: 10px;
            right: 10px;
            width: auto;
            max-width: 320px;
            padding: 14px;
            gap: 10px;
          }

          .current-mode-header {
            gap: 10px;
          }

          .current-mode-icon {
            width: 40px;
            height: 40px;
            font-size: 18px;
          }

          .current-mode-title {
            font-size: 14px;
          }

          .current-mode-subtitle {
            font-size: 10px;
          }

          .mode-stats-grid {
            grid-template-columns: 1fr 1fr;
            gap: 6px;
          }

          .mode-stat-item {
            padding: 6px 8px;
          }

          .mode-stat-text {
            font-size: 10px;
          }

          .apple-ready-button {
            padding: 10px 14px;
            font-size: 13px;
            gap: 6px;
          }

          .mode-rating-section {
            padding: 6px 10px;
          }

          .mode-rating-stars,
          .mode-rating-text {
            font-size: 11px;
          }

          .mode-status {
            font-size: 8px;
          }

          .profile-dropdown {
            right: -10px;
            min-width: 140px;
          }

          .dropdown-item {
            padding: 8px 10px;
            font-size: 12px;
          }

          .apple-setup-card {
            position: fixed;
            bottom: 10px;
            right: 10px;
            left: 10px;
            width: auto;
            max-width: 300px;
            margin: 0 auto;
            padding: 14px;
            gap: 10px;
          }

          .setup-card-title {
            font-size: 14px;
          }

          .setup-card-subtitle {
            font-size: 10px;
          }

          .apple-input {
            padding: 8px 12px;
            font-size: 13px;
          }

          .apple-input-label {
            font-size: 10px;
            margin-bottom: 4px;
          }

          .wager-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 4px;
            margin-top: 4px;
          }

          .wager-option {
            padding: 6px 8px;
            font-size: 10px;
            min-height: 28px;
          }

          .admit-pit-button {
            padding: 8px 12px;
            font-size: 12px;
            gap: 4px;
            margin-top: 2px;
          }
        }

        /* Locker Styles */
        .locker-main-content {
          display: flex;
          gap: 20px;
          padding: 20px;
          height: calc(100vh - 120px);
          overflow: hidden;
        }

        .locker-categories {
          display: flex;
          flex-direction: column;
          gap: 10px;
          min-width: 200px;
          background: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
          border-radius: 16px;
          padding: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .locker-category-btn {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          color: rgba(255, 255, 255, 0.7);
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 14px;
          font-weight: 500;
        }

        .locker-category-btn:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
          transform: translateY(-1px);
        }

        .locker-category-btn.active {
          background: linear-gradient(135deg, #007AFF, #5856D6);
          color: white;
          border-color: #007AFF;
          box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        }

        .category-icon {
          flex-shrink: 0;
        }

        .category-name {
          font-weight: 600;
        }

        .locker-items-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          background: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
          border-radius: 16px;
          padding: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          overflow: hidden;
        }

        .locker-items-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .locker-section-title {
          font-size: 24px;
          font-weight: 700;
          color: white;
          margin: 0;
        }

        .locker-stats {
          display: flex;
          gap: 15px;
          align-items: center;
        }

        .owned-count {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          background: rgba(255, 255, 255, 0.05);
          padding: 6px 12px;
          border-radius: 8px;
        }

        .locker-items-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 15px;
          overflow-y: auto;
          padding-right: 10px;
        }

        .locker-item {
          position: relative;
          background: rgba(255, 255, 255, 0.05);
          border: 2px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 15px;
          cursor: pointer;
          transition: all 0.2s ease;
          min-height: 140px;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
        }

        .locker-item:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .locker-item.owned {
          background: rgba(255, 255, 255, 0.08);
        }

        .locker-item.equipped {
          background: linear-gradient(135deg, rgba(0, 122, 255, 0.2), rgba(88, 86, 214, 0.2));
          border-color: #007AFF;
          box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);
        }

        .locker-item.locked {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .item-image {
          margin-bottom: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .item-icon {
          color: rgba(255, 255, 255, 0.8);
        }

        .item-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .item-name {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .item-rarity {
          font-size: 10px;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          opacity: 0.8;
        }

        .item-description {
          font-size: 11px;
          color: rgba(255, 255, 255, 0.6);
          line-height: 1.3;
        }

        .equipped-badge {
          position: absolute;
          top: 8px;
          right: 8px;
          background: #34C759;
          color: white;
          font-size: 8px;
          font-weight: 700;
          padding: 2px 6px;
          border-radius: 4px;
          letter-spacing: 0.3px;
        }

        .locked-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.7);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .lock-icon {
          opacity: 0.8;
          color: rgba(255, 255, 255, 0.6);
        }

        .locker-character-preview {
          min-width: 300px;
          background: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
          border-radius: 16px;
          padding: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          display: flex;
          flex-direction: column;
        }

        .preview-header {
          text-align: center;
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .preview-title {
          font-size: 18px;
          font-weight: 700;
          color: white;
          margin: 0 0 5px 0;
        }

        .preview-subtitle {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
        }

        .character-preview-container {
          flex: 1;
          min-height: 300px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          margin-bottom: 20px;
          position: relative;
          overflow: hidden;
        }

        .character-model-preview {
          width: 100%;
          height: 100%;
          position: relative;
        }

        .avatar-3d-locker {
          width: 100%;
          height: 100%;
        }

        .equipped-items-summary {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 15px;
        }

        .summary-title {
          font-size: 14px;
          font-weight: 600;
          color: white;
          margin: 0 0 10px 0;
        }

        .equipped-items-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .equipped-item-summary {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;
        }

        .equipped-category {
          color: rgba(255, 255, 255, 0.7);
          text-transform: capitalize;
          font-weight: 500;
        }

        .equipped-item-name {
          font-weight: 600;
        }

        @media (max-width: 1024px) {
          .locker-main-content {
            flex-direction: column;
            height: auto;
            gap: 15px;
          }

          .locker-categories {
            flex-direction: row;
            min-width: auto;
            overflow-x: auto;
            padding: 15px;
          }

          .locker-category-btn {
            min-width: 120px;
            flex-shrink: 0;
          }

          .locker-character-preview {
            min-width: auto;
            order: -1;
          }

          .character-preview-container {
            min-height: 200px;
          }
        }

        @media (max-width: 768px) {
          .locker-items-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
          }

          .locker-item {
            min-height: 120px;
            padding: 12px;
          }

          .item-image {
            font-size: 24px;
          }

          .locker-categories {
            gap: 8px;
            padding: 12px;
          }

          .locker-category-btn {
            padding: 10px 12px;
            font-size: 12px;
            min-width: 100px;
          }

          .category-icon {
            font-size: 16px;
          }
        }

        /* Shop Styles */
        .shop-main-content {
          display: flex;
          gap: 20px;
          padding: 20px;
          height: calc(100vh - 120px);
          overflow: hidden;
        }

        .shop-categories {
          display: flex;
          flex-direction: column;
          gap: 10px;
          min-width: 200px;
          background: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
          border-radius: 16px;
          padding: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .shop-category-btn {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          color: rgba(255, 255, 255, 0.7);
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 14px;
          font-weight: 500;
        }

        .shop-category-btn:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
          transform: translateY(-1px);
        }

        .shop-category-btn.active {
          background: linear-gradient(135deg, #FF6B35, #F7931E);
          color: white;
          border-color: #FF6B35;
          box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
        }

        .shop-items-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          background: rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(10px);
          border-radius: 16px;
          padding: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          overflow: hidden;
        }

        .shop-items-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .shop-section-title {
          font-size: 24px;
          font-weight: 700;
          color: white;
          margin: 0;
        }

        .shop-balance {
          display: flex;
          align-items: center;
        }

        .balance-display {
          display: flex;
          align-items: center;
          gap: 8px;
          background: rgba(255, 255, 255, 0.05);
          padding: 8px 16px;
          border-radius: 12px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sol-icon {
          width: 20px;
          height: 20px;
          border-radius: 4px;
        }

        .balance-amount {
          font-size: 16px;
          font-weight: 600;
          color: white;
        }

        .balance-usd {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
        }

        .featured-items-banner {
          background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.1));
          border: 1px solid rgba(255, 107, 53, 0.3);
          border-radius: 12px;
          padding: 15px;
          margin-bottom: 20px;
        }

        .featured-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
        }

        .featured-icon {
          color: #FFD700;
        }

        .featured-title {
          font-size: 16px;
          font-weight: 600;
          color: white;
        }

        .featured-items-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 10px;
        }

        .featured-item {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          padding: 10px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .featured-item-content {
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .featured-item-icon {
          flex-shrink: 0;
        }

        .featured-item-info {
          flex: 1;
        }

        .featured-item-name {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .featured-item-price {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
        }

        .shop-items-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          gap: 20px;
          overflow-y: auto;
          padding-right: 10px;
        }

        .shop-item {
          position: relative;
          background: rgba(255, 255, 255, 0.05);
          border: 2px solid rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          padding: 20px;
          transition: all 0.2s ease;
          display: flex;
          flex-direction: column;
          min-height: 200px;
        }

        .shop-item:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
          background: rgba(255, 255, 255, 0.08);
        }

        .shop-item.featured {
          background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(247, 147, 30, 0.05));
          border-color: rgba(255, 107, 53, 0.4);
        }

        .discount-tag {
          position: absolute;
          top: 10px;
          right: 10px;
          background: #FF4444;
          color: white;
          font-size: 10px;
          font-weight: 700;
          padding: 4px 8px;
          border-radius: 6px;
          letter-spacing: 0.3px;
        }

        .shop-item-header {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 15px;
        }

        .item-rarity {
          font-size: 10px;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-top: 8px;
          opacity: 0.8;
        }

        .shop-item-info {
          flex: 1;
          text-align: center;
          margin-bottom: 15px;
        }

        .shop-item-footer {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .price-section {
          text-align: center;
        }

        .original-price {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.5);
          text-decoration: line-through;
          margin-bottom: 4px;
        }

        .current-price {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          font-size: 16px;
          font-weight: 600;
          color: white;
          margin-bottom: 4px;
        }

        .price-sol-icon {
          width: 16px;
          height: 16px;
          border-radius: 2px;
        }

        .price-usd {
          font-size: 11px;
          color: rgba(255, 255, 255, 0.6);
        }

        .discount-badge {
          background: #FF4444;
          color: white;
          font-size: 10px;
          font-weight: 700;
          padding: 2px 6px;
          border-radius: 4px;
          margin-left: 6px;
        }

        .purchase-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          padding: 12px 16px;
          background: linear-gradient(135deg, #34C759, #30D158);
          border: none;
          border-radius: 12px;
          color: white;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .purchase-btn:hover:not(:disabled) {
          background: linear-gradient(135deg, #30D158, #34C759);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(52, 199, 89, 0.3);
        }

        .purchase-btn:disabled {
          cursor: not-allowed;
          opacity: 0.6;
        }

        .purchase-btn.purchasing {
          background: linear-gradient(135deg, #007AFF, #5856D6);
        }

        .purchase-btn.success {
          background: linear-gradient(135deg, #34C759, #30D158);
        }

        .purchase-btn.insufficient {
          background: linear-gradient(135deg, #FF3B30, #FF6B35);
        }

        .purchase-icon {
          flex-shrink: 0;
        }

        .spinning {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @media (max-width: 1024px) {
          .shop-main-content {
            flex-direction: column;
            height: auto;
            gap: 15px;
          }

          .shop-categories {
            flex-direction: row;
            min-width: auto;
            overflow-x: auto;
            padding: 15px;
          }

          .shop-category-btn {
            min-width: 120px;
            flex-shrink: 0;
          }

          .shop-items-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
          }

          .featured-items-grid {
            grid-template-columns: 1fr;
          }
        }

        @media (max-width: 768px) {
          .shop-items-grid {
            grid-template-columns: 1fr;
            gap: 12px;
          }

          .shop-item {
            min-height: 180px;
            padding: 15px;
          }

          .shop-categories {
            gap: 8px;
            padding: 12px;
          }

          .shop-category-btn {
            padding: 10px 12px;
            font-size: 12px;
            min-width: 100px;
          }

          .balance-display {
            padding: 6px 12px;
          }

          .balance-amount {
            font-size: 14px;
          }

          .shop-section-title {
            font-size: 20px;
          }

          .featured-items-banner {
            padding: 12px;
          }
        }

        /* PitPass Styles - Apple Premium Design */
        .pitpass-main-content {
          height: calc(100vh - 120px);
          overflow-y: auto;
          overflow-x: hidden;
          padding: 0;
          margin: 0;
          background: transparent;
          scroll-behavior: smooth;
        }

        .pitpass-main-content::-webkit-scrollbar {
          width: 8px;
        }

        .pitpass-main-content::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        .pitpass-main-content::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.3);
          border-radius: 4px;
        }

        .pitpass-main-content::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.5);
        }

        .pitpass-header {
          background: linear-gradient(180deg,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.8) 50%,
            rgba(0, 0, 0, 0.95) 100%);
          padding: 80px 60px;
          text-align: center;
          position: relative;
          overflow: hidden;
        }

        .pitpass-header::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background:
            radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%);
          pointer-events: none;
        }

        .pitpass-hero-content {
          position: relative;
          z-index: 1;
          max-width: 800px;
          margin: 0 auto;
        }

        .pitpass-title {
          font-size: 4rem;
          font-weight: 700;
          color: white;
          margin-bottom: 24px;
          letter-spacing: -0.02em;
          line-height: 1.1;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .pitpass-crown {
          display: none;
        }

        .pitpass-subtitle {
          font-size: 1.375rem;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 48px;
          font-weight: 400;
          line-height: 1.4;
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }

        .subscription-status {
          display: inline-flex;
          align-items: center;
          gap: 12px;
          padding: 16px 24px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        .subscription-status.active {
          background: rgba(52, 199, 89, 0.15);
          border-color: rgba(52, 199, 89, 0.3);
        }

        .subscription-status.inactive {
          background: rgba(255, 255, 255, 0.08);
          border-color: rgba(255, 255, 255, 0.15);
        }

        .status-badge {
          font-size: 1rem;
          font-weight: 600;
          color: white;
        }

        .status-details {
          font-size: 0.875rem;
          color: rgba(255, 255, 255, 0.7);
        }

        .pitpass-tiers-section {
          padding: 80px 60px;
          background: rgba(0, 0, 0, 0.4);
        }

        .section-title {
          font-size: 2.5rem;
          font-weight: 700;
          color: white;
          text-align: center;
          margin-bottom: 64px;
          letter-spacing: -0.02em;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .pitpass-tiers-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 24px;
          max-width: 1200px;
          margin: 0 auto;
        }

        .pitpass-tier-card {
          background: rgba(255, 255, 255, 0.08);
          border: 1px solid rgba(255, 255, 255, 0.12);
          border-radius: 16px;
          padding: 40px 32px;
          cursor: pointer;
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          position: relative;
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
          overflow: hidden;
        }

        .pitpass-tier-card:hover {
          transform: translateY(-4px);
          background: rgba(255, 255, 255, 0.12);
          border-color: rgba(255, 255, 255, 0.2);
          box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        .pitpass-tier-card.selected {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(255, 255, 255, 0.3);
          box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.4),
            0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        .pitpass-tier-card.popular {
          background: rgba(255, 255, 255, 0.12);
          border-color: rgba(255, 255, 255, 0.25);
          position: relative;
        }

        .popular-badge {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%) translateY(-50%);
          background: rgba(255, 255, 255, 0.9);
          color: rgba(0, 0, 0, 0.8);
          padding: 6px 16px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        .tier-header {
          text-align: center;
          margin-bottom: 40px;
          padding-top: 16px;
        }

        .tier-name {
          font-size: 1.5rem;
          font-weight: 600;
          color: white;
          margin-bottom: 24px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .tier-pricing {
          display: flex;
          align-items: baseline;
          justify-content: center;
          gap: 4px;
          margin-bottom: 8px;
        }

        .tier-price {
          font-size: 2.5rem;
          font-weight: 700;
          color: white;
          letter-spacing: -0.02em;
        }

        .tier-original-price {
          font-size: 1rem;
          color: rgba(255, 255, 255, 0.5);
          text-decoration: line-through;
          margin-left: 8px;
        }

        .tier-period {
          font-size: 1rem;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 400;
        }

        .tier-features {
          margin-bottom: 40px;
        }

        .features-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: white;
          margin-bottom: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .features-list {
          list-style: none;
          padding: 0;
          margin: 0;
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .feature-item {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          color: rgba(255, 255, 255, 0.85);
          font-size: 0.9375rem;
          line-height: 1.4;
        }

        .feature-check {
          color: rgba(52, 199, 89, 1);
          font-weight: 600;
          font-size: 0.875rem;
          margin-top: 2px;
          flex-shrink: 0;
        }

        .tier-bonuses {
          margin-bottom: 40px;
        }

        .bonuses-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: white;
          margin-bottom: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .bonuses-grid {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .bonus-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          background: rgba(255, 255, 255, 0.06);
          border-radius: 8px;
          border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .bonus-icon {
          font-size: 1rem;
          opacity: 0.8;
        }

        .bonus-text {
          color: rgba(255, 255, 255, 0.85);
          font-size: 0.875rem;
          font-weight: 400;
        }

        .tier-subscribe-btn {
          width: 100%;
          padding: 16px 24px;
          background: rgba(255, 255, 255, 0.9);
          color: rgba(0, 0, 0, 0.8);
          border: none;
          border-radius: 12px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .tier-subscribe-btn:hover {
          background: white;
          transform: translateY(-1px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .tier-subscribe-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .tier-subscribe-btn.purchasing {
          background: rgba(255, 255, 255, 0.7);
        }

        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid rgba(0, 0, 0, 0.2);
          border-top: 2px solid rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .pitpass-benefits-section {
          padding: 80px 60px;
          background: rgba(0, 0, 0, 0.2);
        }

        .benefits-showcase {
          max-width: 1200px;
          margin: 0 auto;
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 32px;
        }

        .benefit-category {
          background: rgba(255, 255, 255, 0.06);
          border-radius: 16px;
          padding: 40px 32px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
          transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .benefit-category:hover {
          background: rgba(255, 255, 255, 0.08);
          transform: translateY(-4px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .category-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: white;
          margin-bottom: 32px;
          display: flex;
          align-items: center;
          gap: 12px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .category-icon {
          font-size: 1.5rem;
          opacity: 0.8;
        }

        .benefit-items {
          display: flex;
          flex-direction: column;
          gap: 24px;
        }

        .benefit-item {
          display: flex;
          gap: 16px;
          padding: 0;
          background: transparent;
          border: none;
          border-radius: 0;
          transition: all 0.3s ease;
        }

        .benefit-item:hover {
          transform: none;
          background: transparent;
          border-color: transparent;
        }

        .benefit-preview {
          flex-shrink: 0;
          width: 48px;
          height: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 12px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .cosmetic-preview {
          font-size: 1.5rem;
        }

        .reward-preview {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
        }

        .bonus-percentage {
          font-size: 1rem;
        }

        .bonus-label {
          font-size: 0.75rem;
          opacity: 0.7;
        }

        .access-preview {
          font-size: 1.5rem;
        }

        .benefit-info h4 {
          font-size: 1.125rem;
          font-weight: 600;
          color: white;
          margin-bottom: 8px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .benefit-info p {
          color: rgba(255, 255, 255, 0.7);
          font-size: 0.875rem;
          margin-bottom: 12px;
          line-height: 1.4;
        }

        .benefit-tier {
          display: inline-block;
          padding: 4px 8px;
          background: rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.8);
          border-radius: 6px;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .pitpass-calendar-section {
          padding: 80px 60px;
          background: rgba(0, 0, 0, 0.3);
        }

        .rewards-calendar {
          max-width: 800px;
          margin: 0 auto;
          background: rgba(255, 255, 255, 0.06);
          border-radius: 16px;
          padding: 48px 40px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        .calendar-header {
          text-align: center;
          margin-bottom: 40px;
        }

        .calendar-header h3 {
          font-size: 2rem;
          font-weight: 600;
          color: white;
          margin-bottom: 12px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .calendar-header p {
          color: rgba(255, 255, 255, 0.7);
          font-size: 1.125rem;
          font-weight: 400;
        }

        .calendar-grid {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          gap: 12px;
          max-width: 560px;
          margin: 0 auto;
        }

        .calendar-day {
          aspect-ratio: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.08);
          border-radius: 12px;
          border: 1px solid rgba(255, 255, 255, 0.12);
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          position: relative;
        }

        .calendar-day.available {
          background: rgba(52, 199, 89, 0.15);
          border-color: rgba(52, 199, 89, 0.3);
        }

        .calendar-day.today {
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(255, 255, 255, 0.3);
          box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
        }

        .calendar-day.locked {
          opacity: 0.4;
          cursor: not-allowed;
        }

        .calendar-day:hover:not(.locked) {
          background: rgba(255, 255, 255, 0.12);
          transform: translateY(-2px);
        }

        .day-number {
          font-size: 0.875rem;
          font-weight: 600;
          color: white;
          margin-bottom: 4px;
        }

        .day-reward {
          font-size: 1.125rem;
          opacity: 0.8;
        }

        .pitpass-management-section {
          padding: 60px;
          background: rgba(0, 0, 0, 0.2);
        }

        .management-card {
          max-width: 800px;
          margin: 0 auto;
          background: rgba(255, 255, 255, 0.06);
          border-radius: 16px;
          padding: 40px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
        }

        .current-subscription-info {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: 32px;
        }

        .subscription-details h3 {
          font-size: 1.5rem;
          font-weight: 600;
          color: white;
          margin-bottom: 12px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .subscription-details p {
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 8px;
          font-size: 0.9375rem;
        }

        .subscription-actions {
          display: flex;
          gap: 12px;
          flex-shrink: 0;
        }

        .management-btn {
          padding: 12px 20px;
          border-radius: 8px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          border: none;
          font-size: 0.875rem;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .management-btn.secondary {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .management-btn.secondary:hover {
          background: rgba(255, 255, 255, 0.15);
          transform: translateY(-1px);
        }

        .management-btn.danger {
          background: rgba(255, 69, 58, 0.15);
          color: rgba(255, 69, 58, 1);
          border: 1px solid rgba(255, 69, 58, 0.25);
        }

        .management-btn.danger:hover {
          background: rgba(255, 69, 58, 0.25);
          transform: translateY(-1px);
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(20px);
          -webkit-backdrop-filter: blur(20px);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          padding: 20px;
        }

        .pitpass-confirmation-modal {
          background: rgba(255, 255, 255, 0.95);
          border-radius: 16px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          max-width: 480px;
          width: 100%;
          max-height: 90vh;
          overflow-y: auto;
          backdrop-filter: blur(40px);
          -webkit-backdrop-filter: blur(40px);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 24px 32px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .modal-header h2 {
          font-size: 1.5rem;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.8);
          margin: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .modal-close-btn {
          background: none;
          border: none;
          color: rgba(0, 0, 0, 0.5);
          font-size: 1.5rem;
          cursor: pointer;
          padding: 8px;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 6px;
          transition: all 0.3s ease;
        }

        .modal-close-btn:hover {
          background: rgba(0, 0, 0, 0.1);
          color: rgba(0, 0, 0, 0.8);
        }

        .modal-content {
          padding: 32px;
        }

        .selected-tier-info {
          text-align: center;
          margin-bottom: 32px;
        }

        .selected-tier-info h3 {
          font-size: 1.375rem;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.8);
          margin-bottom: 8px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .tier-price-large {
          font-size: 2rem;
          font-weight: 700;
          color: rgba(0, 0, 0, 0.8);
        }

        .billing-info {
          margin-bottom: 24px;
        }

        .billing-info h4 {
          font-size: 1.125rem;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.8);
          margin-bottom: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .billing-details {
          background: rgba(0, 0, 0, 0.05);
          border-radius: 8px;
          padding: 20px;
          border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .billing-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          color: rgba(0, 0, 0, 0.7);
          font-size: 0.9375rem;
        }

        .billing-row.total {
          border-top: 1px solid rgba(0, 0, 0, 0.15);
          padding-top: 8px;
          margin-top: 8px;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.8);
          font-size: 1rem;
        }

        .payment-method {
          margin-bottom: 24px;
        }

        .payment-method h4 {
          font-size: 1.125rem;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.8);
          margin-bottom: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .payment-option {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          background: rgba(0, 0, 0, 0.03);
          border-radius: 8px;
          border: 1px solid rgba(0, 0, 0, 0.1);
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .payment-option.selected {
          border-color: rgba(0, 122, 255, 1);
          background: rgba(0, 122, 255, 0.1);
        }

        .payment-icon {
          font-size: 1.25rem;
        }

        .payment-details span {
          display: block;
          color: rgba(0, 0, 0, 0.8);
          font-weight: 600;
          font-size: 0.9375rem;
        }

        .payment-details small {
          color: rgba(0, 0, 0, 0.6);
          font-size: 0.8125rem;
        }

        .terms-agreement {
          margin-bottom: 24px;
        }

        .checkbox-label {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          cursor: pointer;
          color: rgba(0, 0, 0, 0.7);
          font-size: 0.875rem;
          line-height: 1.4;
        }

        .checkbox-label input[type="checkbox"] {
          display: none;
        }

        .checkmark {
          width: 18px;
          height: 18px;
          border: 1px solid rgba(0, 0, 0, 0.3);
          border-radius: 3px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          flex-shrink: 0;
          margin-top: 1px;
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
          background: rgba(0, 122, 255, 1);
          border-color: rgba(0, 122, 255, 1);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
          content: '✓';
          color: white;
          font-weight: bold;
          font-size: 0.75rem;
        }

        .modal-footer {
          display: flex;
          gap: 12px;
          padding: 20px 32px;
          border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .modal-btn {
          flex: 1;
          padding: 14px 20px;
          border-radius: 8px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          border: none;
          font-size: 0.9375rem;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        }

        .modal-btn.secondary {
          background: rgba(0, 0, 0, 0.08);
          color: rgba(0, 0, 0, 0.8);
          border: 1px solid rgba(0, 0, 0, 0.15);
        }

        .modal-btn.secondary:hover {
          background: rgba(0, 0, 0, 0.12);
        }

        .modal-btn.primary {
          background: rgba(0, 122, 255, 1);
          color: white;
        }

        .modal-btn.primary:hover {
          background: rgba(0, 122, 255, 0.9);
        }

        @media (max-width: 1024px) {
          .pitpass-tiers-grid {
            grid-template-columns: 1fr;
            max-width: 500px;
            margin: 0 auto;
          }

          .benefits-showcase {
            grid-template-columns: 1fr;
          }
        }

        @media (max-width: 768px) {
          .pitpass-header,
          .pitpass-tiers-section,
          .pitpass-benefits-section,
          .pitpass-calendar-section,
          .pitpass-management-section {
            padding: 40px 20px;
          }

          .pitpass-title {
            font-size: 2.5rem;
          }

          .section-title {
            font-size: 2rem;
          }

          .calendar-grid {
            gap: 8px;
          }

          .current-subscription-info {
            flex-direction: column;
            align-items: flex-start;
            gap: 20px;
          }

          .subscription-actions {
            flex-direction: column;
            width: 100%;
          }

          .modal-footer {
            flex-direction: column;
          }
        }

        /* Leaderboard Styles */
        .leaderboard-main-content {
          padding: 30px;
          height: calc(100vh - 120px);
          overflow-y: auto;
          background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
          backdrop-filter: blur(20px);
        }

        .leaderboard-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 40px;
          padding: 30px;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
          border-radius: 24px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
        }

        .leaderboard-title-section {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .leaderboard-main-title {
          display: flex;
          align-items: center;
          gap: 16px;
          font-size: 36px;
          font-weight: 800;
          background: linear-gradient(135deg, #FFD700, #FFA500, #FF6B35);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          margin: 0;
        }

        .leaderboard-title-icon {
          color: #FFD700;
          filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
        }

        .leaderboard-subtitle {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.7);
          margin: 0;
        }

        .leaderboard-user-rank {
          background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
          border: 1px solid rgba(0, 122, 255, 0.3);
          border-radius: 20px;
          padding: 24px;
          min-width: 280px;
        }

        .user-rank-card {
          text-align: center;
        }

        .rank-header {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          margin-bottom: 12px;
        }

        .rank-icon {
          color: #007AFF;
        }

        .rank-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
        }

        .rank-display {
          margin-bottom: 8px;
        }

        .rank-number {
          font-size: 28px;
          font-weight: 700;
          color: #FFD700;
          display: block;
        }

        .rank-category {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
        }

        .rank-stats {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
        }

        /* Two Panel Layout Styles */
        .leaderboard-two-panel-container {
          display: flex;
          gap: 24px;
          height: calc(100vh - 300px);
        }

        .leaderboard-left-panel {
          width: 280px;
          min-width: 280px;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
          border-radius: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          padding: 24px;
          backdrop-filter: blur(20px);
          overflow-y: auto;
        }

        .leaderboard-right-panel {
          flex: 1;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
          border-radius: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          padding: 24px;
          backdrop-filter: blur(20px);
          overflow-y: auto;
        }

        .filter-buttons-vertical {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .filter-buttons-vertical .filter-btn {
          width: 100%;
          justify-content: flex-start;
          padding: 12px 16px;
          font-size: 14px;
        }

        .leaderboard-filters {
          display: flex;
          gap: 40px;
          margin-bottom: 40px;
          padding: 24px;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
          border-radius: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .filter-section {
          flex: 1;
          margin-bottom: 32px;
        }

        .leaderboard-left-panel .filter-section:last-child {
          margin-bottom: 0;
        }

        .filter-title {
          font-size: 16px;
          font-weight: 600;
          color: white;
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .filter-buttons {
          display: flex;
          gap: 12px;
          flex-wrap: wrap;
        }

        .filter-btn {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 10px 16px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          color: rgba(255, 255, 255, 0.7);
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .filter-btn:hover {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          transform: translateY(-1px);
        }

        .filter-btn.active {
          background: linear-gradient(135deg, #FFD700, #FFA500);
          color: #000;
          border-color: #FFD700;
          box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
        }

        .leaderboard-table-container {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.04));
          border-radius: 20px;
          padding: 24px;
          border: 1px solid rgba(255, 255, 255, 0.15);
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .leaderboard-table-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
        }

        .table-title {
          font-size: 24px;
          font-weight: 700;
          color: white;
          margin: 0;
        }

        .table-stats {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .total-players {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.6);
          background: rgba(255, 255, 255, 0.05);
          padding: 6px 12px;
          border-radius: 8px;
        }

        /* Podium Styles */
        .leaderboard-podium {
          display: flex;
          justify-content: center;
          align-items: end;
          gap: 20px;
          margin-bottom: 40px;
          padding: 40px 20px;
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.05), rgba(255, 165, 0, 0.02));
          border-radius: 20px;
          border: 1px solid rgba(255, 215, 0, 0.1);
        }

        .podium-position {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
        }

        .position-1 {
          order: 2;
          transform: scale(1.1);
        }

        .position-2 {
          order: 1;
          transform: scale(0.95);
        }

        .position-3 {
          order: 3;
          transform: scale(0.95);
        }

        .podium-player {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          position: relative;
          padding: 20px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 16px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          min-width: 180px;
        }

        .player-avatar-large {
          font-size: 48px;
          margin-bottom: 12px;
          position: relative;
        }

        .player-rank-badge {
          position: absolute;
          top: -8px;
          right: -8px;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: 700;
          color: white;
          border: 2px solid white;
        }

        .player-info-podium {
          width: 100%;
        }

        .player-name-large {
          font-size: 16px;
          font-weight: 700;
          color: white;
          margin-bottom: 8px;
        }

        .player-country {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          margin-bottom: 12px;
        }

        .country-flag {
          font-size: 16px;
        }

        .country-code {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
          font-weight: 500;
        }

        .player-stat-main {
          font-size: 18px;
          font-weight: 700;
          color: #FFD700;
          margin-bottom: 8px;
        }

        .player-level {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
          background: rgba(255, 255, 255, 0.1);
          padding: 4px 8px;
          border-radius: 6px;
          display: inline-block;
        }

        /* Table Styles */
        .leaderboard-table {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 16px;
          overflow: hidden;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table-header {
          display: grid;
          grid-template-columns: 80px 1fr 120px 80px 80px 100px;
          gap: 16px;
          padding: 16px 20px;
          background: rgba(255, 255, 255, 0.05);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header-cell {
          font-size: 12px;
          font-weight: 700;
          color: rgba(255, 255, 255, 0.8);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .table-body {
          flex: 1;
          overflow-y: auto;
          min-height: 0;
        }

        .table-row {
          display: grid;
          grid-template-columns: 80px 1fr 120px 80px 80px 100px;
          gap: 16px;
          padding: 16px 20px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);
          transition: all 0.2s ease;
        }

        .table-row:hover {
          background: rgba(255, 255, 255, 0.05);
        }

        .table-row.current-user {
          background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.05));
          border: 1px solid rgba(0, 122, 255, 0.3);
          border-radius: 12px;
          margin: 4px 8px;
          padding: 16px 12px;
        }

        .table-row.top-three {
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.05));
        }

        .table-cell {
          display: flex;
          align-items: center;
        }

        .rank-cell {
          justify-content: center;
        }

        .rank-display-table {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .rank-number {
          font-size: 16px;
          font-weight: 700;
        }

        .rank-glow {
          position: absolute;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          opacity: 0.3;
          z-index: -1;
        }

        .player-cell {
          gap: 12px;
        }

        .player-info-table {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .player-avatar-small {
          font-size: 24px;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
        }

        .player-details {
          flex: 1;
        }

        .player-name-table {
          font-size: 14px;
          font-weight: 600;
          color: white;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .you-badge {
          background: linear-gradient(135deg, #007AFF, #5856D6);
          color: white;
          font-size: 10px;
          font-weight: 700;
          padding: 2px 6px;
          border-radius: 4px;
          letter-spacing: 0.3px;
        }

        .player-country-small {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-top: 2px;
        }

        .country-flag-small {
          font-size: 12px;
        }

        .country-code-small {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.5);
        }

        .stat-cell {
          justify-content: center;
        }

        .stat-value {
          text-align: center;
        }

        .stat-number {
          font-size: 16px;
          font-weight: 700;
          color: #FFD700;
        }

        .level-cell,
        .streak-cell,
        .earnings-cell {
          justify-content: center;
        }

        .level-badge,
        .streak-display,
        .earnings-display {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          background: rgba(255, 255, 255, 0.05);
          padding: 4px 8px;
          border-radius: 6px;
        }

        .level-badge {
          color: #34C759;
        }

        .streak-display {
          color: #FF6B35;
        }

        .earnings-display {
          color: #FFD700;
        }

        /* Responsive Design for Leaderboard */
        @media (max-width: 1200px) {
          .leaderboard-header {
            flex-direction: column;
            gap: 20px;
            text-align: center;
          }

          .leaderboard-user-rank {
            min-width: auto;
            width: 100%;
          }

          .leaderboard-filters {
            flex-direction: column;
            gap: 20px;
          }

          .table-header,
          .table-row {
            grid-template-columns: 60px 1fr 100px 60px 60px 80px;
            gap: 12px;
          }

          .leaderboard-podium {
            flex-direction: column;
            align-items: center;
            gap: 16px;
          }

          .position-1,
          .position-2,
          .position-3 {
            order: initial;
            transform: none;
          }

          .podium-player {
            min-width: 200px;
          }
        }

        @media (max-width: 768px) {
          .leaderboard-main-content {
            padding: 20px;
          }

          .leaderboard-header {
            padding: 20px;
          }

          .leaderboard-main-title {
            font-size: 28px;
          }

          .filter-buttons {
            justify-content: center;
          }

          .filter-btn {
            flex: 1;
            min-width: 100px;
            justify-content: center;
          }

          .table-header {
            display: none;
          }

          .table-row {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            margin-bottom: 8px;
          }

          .table-cell {
            justify-content: space-between;
            padding: 4px 0;
          }

          .player-cell {
            order: -1;
            justify-content: flex-start;
          }

          .rank-cell {
            position: absolute;
            top: 16px;
            right: 16px;
            justify-content: flex-end;
          }

          .leaderboard-podium {
            padding: 20px 10px;
          }

          .podium-player {
            min-width: 160px;
            padding: 16px;
          }

          .player-avatar-large {
            font-size: 36px;
          }

          .player-name-large {
            font-size: 14px;
          }

          .player-stat-main {
            font-size: 16px;
          }
        }

        @media (max-width: 480px) {
          .leaderboard-main-title {
            font-size: 24px;
          }

          .leaderboard-filters {
            padding: 16px;
          }

          .filter-buttons {
            flex-direction: column;
          }

          .filter-btn {
            width: 100%;
          }

          .leaderboard-table-container {
            padding: 20px;
          }

          .podium-player {
            min-width: 140px;
            padding: 12px;
          }

          .player-avatar-large {
            font-size: 32px;
          }

          .table-row {
            padding: 12px;
          }
        }
      `}</style>

      <div className="apple-tab-nav">
        {/* Logo in top left */}
        <div className="navbar-logo" onClick={onBackToStartScreen} title="Back to Start Screen">
          <pre className="ascii-logo-small">{`╔══════════════════════════════════════════════════════════════════════╗
║  ~~o                                                            o~~  ║
║                                                                      ║
║      ██████  ███    ██  █████  ██   ██ ███████ ██████  ██ ████████   ║
║     ██       ████   ██ ██   ██ ██  ██  ██      ██   ██ ██    ██      ║
║     ███████  ██ ██  ██ ███████ █████   █████   █████   ██    ██      ║
║          ██  ██  ██ ██ ██   ██ ██  ██  ██      ██      ██    ██      ║
║     ███████  ██   ████ ██   ██ ██   ██ ███████ ██      ██    ██      ║
║                                                                      ║
║  ~~o                                                            o~~  ║
╚══════════════════════════════════════════════════════════════════════╝`}</pre>
        </div>

        {/* Centered tabs */}
        <div className="tab-center-container">
          <div className="apple-tab-container">
            <button className={`apple-tab ${activeTab === 'play' ? 'active' : ''}`} onClick={() => setActiveTab('play')}>
              <Gamepad2 size={16} /> PLAY
            </button>
            <button className={`apple-tab ${activeTab === 'locker' ? 'active' : ''}`} onClick={() => setActiveTab('locker')}>
              <Backpack size={16} /> LOCKER
            </button>
            <button className={`apple-tab ${activeTab === 'shop' ? 'active' : ''}`} onClick={() => setActiveTab('shop')}>
              <ShoppingCart size={16} /> SHOP
            </button>
            <button className={`apple-tab ${activeTab === 'pitpass' ? 'active' : ''}`} onClick={() => setActiveTab('pitpass')}>
              <Crown size={16} /> PITPASS
            </button>
            <button className={`apple-tab ${activeTab === 'leaderboard' ? 'active' : ''}`} onClick={() => setActiveTab('leaderboard')}>
              <Medal size={16} /> LEADERBOARD
            </button>
            <button className={`apple-tab ${activeTab === 'compete' ? 'active' : ''}`} onClick={() => setActiveTab('compete')}>
              <Trophy size={16} /> COMPETE
            </button>
            <button className={`apple-tab ${activeTab === 'career' ? 'active' : ''}`} onClick={() => setActiveTab('career')}>
              <BarChart3 size={16} /> CAREER
            </button>
          </div>
        </div>

        {/* Right section with balance and user */}
        <div className="nav-right-section">
          <div className="balance-container">
            <div
              className="balance-section sol-section clickable-balance"
              onClick={() => setIsSolBalanceModalOpen(true)}
              title="Click to manage SOL balance"
            >
              <img
                src="/assets/solana-icon.png"
                alt="SOL"
                style={{
                  width: '14px',
                  height: '14px',
                  borderRadius: '2px'
                }}
              />
              <span className="balance-label sol-label">SOL</span>
              <span className="balance-value">
                {walletBalance !== null ? walletBalance.toFixed(3) : userData.solanaBalance.toFixed(3)}
              </span>
              <button
                className="refresh-btn sol-refresh"
                onClick={(e) => {
                  e.stopPropagation();
                  refreshBalance();
                }}
                disabled={balanceLoading}
                title="Refresh SOL balance from blockchain"
              >
                <RefreshCw size={12} className={balanceLoading ? 'spinning' : ''} />
              </button>
            </div>
            {solPrice && (
              <div className="balance-section usd-section">
                <span className="balance-label usd-label">USD</span>
                <span className="balance-value">
                  {walletUsdBalance !== null
                    ? formatUsdAmount(walletUsdBalance)
                    : formatUsdAmount(convertSolToUsd(userData.solanaBalance) || 0)
                  }
                </span>
                <button
                  className="refresh-btn usd-refresh"
                  onClick={(e) => {
                    e.stopPropagation();
                    refreshPrice();
                  }}
                  disabled={priceLoading}
                  title="Refresh USD price from Pyth Network"
                >
                  <RefreshCw size={12} className={priceLoading ? 'spinning' : ''} />
                </button>
              </div>
            )}
          </div>
          <div
            className="profile-dropdown-container"
            onMouseEnter={() => setIsProfileDropdownVisible(true)}
            onMouseLeave={() => setIsProfileDropdownVisible(false)}
          >
            <button className="apple-user-btn" onClick={onShowProfile}>
              <User size={18} />
              {userProfile && (
                <span className="user-level">LVL {Math.floor(userProfile.total_games_played / 10) + 1}</span>
              )}
            </button>

            <div className={`profile-dropdown ${isProfileDropdownVisible ? 'visible' : ''}`}>
              <button className="dropdown-item" onClick={onShowSettings}>
                <SettingsIcon size={16} />
                Settings
              </button>
              <button className="dropdown-item" onClick={onShowHelp}>
                <HelpCircle size={16} />
                Help
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Lobby Content */}
      {activeTab === 'play' && (
      <div className="lobby-main-content">
        {/* News Slider in top-left area */}
        <div className="lobby-top-left-section">
          <NewsSlider className="lobby-news-slider" />
        </div>

        {/* Apple-style Game Mode Card */}
        <div className={`apple-game-mode-card lobby-game-mode-card ${(currentModeDetails as any).isBossMode ? 'boss-mode' : ''}`}>
          {/* Boss Mode Prize Badge */}
          {(currentModeDetails as any).isBossMode && (
            <div className="boss-prize-badge">
              {(currentModeDetails as any).prize} BOSS BATTLE
            </div>
          )}

          {/* Lightning Effects for Boss Mode */}
          {(currentModeDetails as any).isBossMode && (
            <div className="lightning-effect">
              <div className="lightning-bolt"></div>
              <div className="lightning-bolt"></div>
              <div className="lightning-bolt"></div>
            </div>
          )}

          <div className="game-mode-image">
            <img
              src={currentModeDetails.image}
              alt={currentModeDetails.name}
              className="game-mode-landscape-image"
              onError={(e) => {
                // Fallback to emoji if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                const fallback = target.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'block';
              }}
            />
            <span className="game-mode-icon-fallback" style={{ display: 'none' }}>
              {selectedMode === 'classic' ? '🐍' : selectedMode === 'warfare' ? '⚔️' : selectedMode === 'speed' ? '⚡' : selectedMode === 'leviathon' ? '🐲' : '🎮'}
            </span>
          </div>
          <h3 className="game-mode-title">{currentModeDetails.name}</h3>

          <div className="game-mode-stats">
            <div className="game-mode-stat">
              <Users size={12} />
              {currentModeDetails.players}
            </div>
            <div className="game-mode-stat">
              <Clock size={12} />
              {currentModeDetails.duration}
            </div>
            <div className="game-mode-stat">
              <Target size={12} />
              {currentModeDetails.difficulty}
            </div>
            <div className="game-mode-stat">
              <Gamepad2 size={12} />
              {currentModeDetails.online} online
            </div>
            <div className="game-mode-stat game-mode-rating">
              <Star size={12} />
              {currentModeDetails.rating}
            </div>
          </div>

          {/* Ready Status Indicator */}
          <div className={`mode-status-indicator ${userData.username.trim() && selectedMode ? '' : 'not-ready'}`}>
            {userData.username.trim() && selectedMode ? 'READY' : 'SETUP'}
          </div>
        </div>

        {/* Central Character Display Area */}
        <div className="lobby-center-area">
          <div className="character-display">
            {/* Character Info */}
            <div className="character-info">
              <h3 className="character-name">{userData.username || 'Enter Snake Name'}</h3>
              <div className="character-stats">
                <span className="stat">
                  <Trophy size={16} />
                  Wins: {userProfile?.total_wins || 0}
                </span>
                <span className="stat">
                  <Target size={16} />
                  Games: {userProfile?.total_games_played || 0}
                </span>
              </div>
            </div>

            <div className="character-platform">
              <div className="character-model">
                {/* 3D Avatar */}
                <Avatar3D
                  className="avatar-3d-seamless"
                  style={{
                    width: '100%',
                    height: '100%'
                  }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Apple-style Snake Setup Card */}
        <div className="apple-setup-card lobby-setup-card">
          <div className="setup-card-header">
            <div className="setup-card-title">Snake Setup</div>
            <div className="setup-card-subtitle">Configure your battle settings</div>
          </div>

          <div className="apple-input-group">
            <label className="apple-input-label">Snake Name</label>
            <input
              type="text"
              value={userData.username}
              onChange={(e) => setUserData({...userData, username: e.target.value})}
              placeholder="Enter your snake name"
              className="apple-input"
              maxLength={20}
            />
          </div>

          <div className="wager-section">
            <label className="apple-input-label">Wager Amount (USD)</label>
            <div className="wager-grid">
              {[10, 15, 25, 50, 75, 100, 150, 250, 500].map((amount) => (
                <button
                  key={amount}
                  className={`wager-option ${userData.wager === amount ? 'selected' : ''}`}
                  onClick={() => setUserData({...userData, wager: amount})}
                >
                  ${amount}
                </button>
              ))}
            </div>
          </div>

          <button
            className="admit-pit-button"
            onClick={onEnterBattle}
            disabled={!selectedMode || userData.username.trim() === ''}
          >
            <Gamepad2 size={16} />
            Admit to the Pit
          </button>
        </div>

        {/* Pull-up chevron for game modes - only visible when drawer is closed */}
        {!isGameModesPanelVisible && (
          <div className="game-modes-pull-up" onClick={toggleGameModesPanel}>
            <div className="pull-up-chevron">
              <ChevronUp size={16} />
              <ChevronUp size={16} />
            </div>
            <div className="pull-up-text">Modes</div>
          </div>
        )}
      </div>
      )}

      {/* Locker Content */}
      {activeTab === 'locker' && (
        <div className="locker-main-content">
          {/* Locker Categories */}
          <div className="locker-categories">
            <button
              className={`locker-category-btn ${selectedLockerCategory === 'skins' ? 'active' : ''}`}
              onClick={() => setSelectedLockerCategory('skins')}
            >
              <Palette className="category-icon" size={18} />
              <span className="category-name">Skins</span>
            </button>
            <button
              className={`locker-category-btn ${selectedLockerCategory === 'weapons' ? 'active' : ''}`}
              onClick={() => setSelectedLockerCategory('weapons')}
            >
              <Sword className="category-icon" size={18} />
              <span className="category-name">Weapons</span>
            </button>
            <button
              className={`locker-category-btn ${selectedLockerCategory === 'powerups' ? 'active' : ''}`}
              onClick={() => setSelectedLockerCategory('powerups')}
            >
              <Zap className="category-icon" size={18} />
              <span className="category-name">Power-ups</span>
            </button>
            <button
              className={`locker-category-btn ${selectedLockerCategory === 'emotes' ? 'active' : ''}`}
              onClick={() => setSelectedLockerCategory('emotes')}
            >
              <Smile className="category-icon" size={18} />
              <span className="category-name">Emotes</span>
            </button>
            <button
              className={`locker-category-btn ${selectedLockerCategory === 'trails' ? 'active' : ''}`}
              onClick={() => setSelectedLockerCategory('trails')}
            >
              <Sparkles className="category-icon" size={18} />
              <span className="category-name">Trails</span>
            </button>
          </div>

          {/* Locker Items Grid */}
          <div className="locker-items-container">
            <div className="locker-items-header">
              <h2 className="locker-section-title">
                {selectedLockerCategory.charAt(0).toUpperCase() + selectedLockerCategory.slice(1)}
              </h2>
              <div className="locker-stats">
                <span className="owned-count">
                  {lockerItems[selectedLockerCategory].filter(item => item.owned).length} / {lockerItems[selectedLockerCategory].length} Owned
                </span>
              </div>
            </div>

            <div className="locker-items-grid">
              {lockerItems[selectedLockerCategory].map((item) => (
                <div
                  key={item.id}
                  className={`locker-item ${item.owned ? 'owned' : 'locked'} ${item.equipped ? 'equipped' : ''}`}
                  onClick={() => item.owned && setSelectedSkin(item.id)}
                  style={{ borderColor: getRarityColor(item.rarity) }}
                >
                  <div className="item-image">
                    {renderIcon(item.icon, 32)}
                  </div>
                  <div className="item-info">
                    <div className="item-name" style={{ color: getRarityColor(item.rarity) }}>
                      {item.name}
                    </div>
                    <div className="item-rarity">
                      {item.rarity.toUpperCase()}
                    </div>
                    <div className="item-description">
                      {item.description}
                    </div>
                  </div>
                  {item.equipped && (
                    <div className="equipped-badge">
                      <Check size={12} /> EQUIPPED
                    </div>
                  )}
                  {!item.owned && (
                    <div className="locked-overlay">
                      <Lock className="lock-icon" size={24} />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Character Preview */}
          <div className="locker-character-preview">
            <div className="preview-header">
              <h3 className="preview-title">Preview</h3>
              <div className="preview-subtitle">
                {userData.username || 'Your Snake'}
              </div>
            </div>

            <div className="character-preview-container">
              <div className="character-model-preview">
                <Avatar3D
                  className="avatar-3d-locker"
                  style={{
                    width: '100%',
                    height: '100%'
                  }}
                />
              </div>
            </div>

            <div className="equipped-items-summary">
              <h4 className="summary-title">Currently Equipped</h4>
              <div className="equipped-items-list">
                {Object.entries(lockerItems).map(([category, items]) => {
                  const equippedItem = items.find(item => item.equipped);
                  return equippedItem ? (
                    <div key={category} className="equipped-item-summary">
                      <span className="equipped-category">{category}:</span>
                      <span className="equipped-item-name" style={{ color: getRarityColor(equippedItem.rarity) }}>
                        {equippedItem.name}
                      </span>
                    </div>
                  ) : null;
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Shop Content */}
      {activeTab === 'shop' && (
        <div className="shop-main-content">
          {/* Shop Categories */}
          <div className="shop-categories">
            <button
              className={`shop-category-btn ${selectedShopCategory === 'skins' ? 'active' : ''}`}
              onClick={() => setSelectedShopCategory('skins')}
            >
              <Palette className="category-icon" size={18} />
              <span className="category-name">Skins</span>
            </button>
            <button
              className={`shop-category-btn ${selectedShopCategory === 'weapons' ? 'active' : ''}`}
              onClick={() => setSelectedShopCategory('weapons')}
            >
              <Sword className="category-icon" size={18} />
              <span className="category-name">Weapons</span>
            </button>
            <button
              className={`shop-category-btn ${selectedShopCategory === 'powerups' ? 'active' : ''}`}
              onClick={() => setSelectedShopCategory('powerups')}
            >
              <Zap className="category-icon" size={18} />
              <span className="category-name">Power-ups</span>
            </button>
            <button
              className={`shop-category-btn ${selectedShopCategory === 'emotes' ? 'active' : ''}`}
              onClick={() => setSelectedShopCategory('emotes')}
            >
              <Smile className="category-icon" size={18} />
              <span className="category-name">Emotes</span>
            </button>
            <button
              className={`shop-category-btn ${selectedShopCategory === 'trails' ? 'active' : ''}`}
              onClick={() => setSelectedShopCategory('trails')}
            >
              <Sparkles className="category-icon" size={18} />
              <span className="category-name">Trails</span>
            </button>
            <button
              className={`shop-category-btn ${selectedShopCategory === 'bundles' ? 'active' : ''}`}
              onClick={() => setSelectedShopCategory('bundles')}
            >
              <Gift className="category-icon" size={18} />
              <span className="category-name">Bundles</span>
            </button>
          </div>

          {/* Shop Items */}
          <div className="shop-items-container">
            <div className="shop-items-header">
              <h2 className="shop-section-title">
                {selectedShopCategory.charAt(0).toUpperCase() + selectedShopCategory.slice(1)}
              </h2>
              <div className="shop-balance">
                <div className="balance-display">
                  <img src="/assets/solana-icon.png" alt="SOL" className="sol-icon" />
                  <span className="balance-amount">{userProfile?.solana_balance?.toFixed(4) || '0.0000'} SOL</span>
                  {solPrice && (
                    <span className="balance-usd">
                      ≈ {formatUsdAmount(convertSolToUsd(userProfile?.solana_balance ?? 0) ?? 0)}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Featured Items Banner */}
            {shopItems[selectedShopCategory].some(item => item.featured) && (
              <div className="featured-items-banner">
                <div className="featured-header">
                  <Star className="featured-icon" size={20} />
                  <span className="featured-title">Featured Items</span>
                </div>
                <div className="featured-items-grid">
                  {shopItems[selectedShopCategory]
                    .filter(item => item.featured)
                    .map((item) => (
                      <div key={item.id} className="featured-item">
                        <div className="featured-item-content">
                          <div className="featured-item-icon">
                            {renderIcon(item.icon, 24)}
                          </div>
                          <div className="featured-item-info">
                            <div className="featured-item-name" style={{ color: getRarityColor(item.rarity) }}>
                              {item.name}
                            </div>
                            <div className="featured-item-price">
                              {item.discount > 0 && (
                                <span className="original-price">{item.price.toFixed(3)} SOL</span>
                              )}
                              <span className="current-price">
                                {(item.price * (1 - item.discount / 100)).toFixed(3)} SOL
                              </span>
                              {item.discount > 0 && (
                                <span className="discount-badge">-{item.discount}%</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}

            {/* All Items Grid */}
            <div className="shop-items-grid">
              {shopItems[selectedShopCategory].map((item) => (
                <div
                  key={item.id}
                  className={`shop-item ${item.featured ? 'featured' : ''}`}
                  style={{ borderColor: getRarityColor(item.rarity) }}
                >
                  {item.discount > 0 && (
                    <div className="discount-tag">
                      -{item.discount}%
                    </div>
                  )}

                  <div className="shop-item-header">
                    <div className="item-image">
                      {renderIcon(item.icon, 40)}
                    </div>
                    <div className="item-rarity" style={{ color: getRarityColor(item.rarity) }}>
                      {item.rarity.toUpperCase()}
                    </div>
                  </div>

                  <div className="shop-item-info">
                    <div className="item-name" style={{ color: getRarityColor(item.rarity) }}>
                      {item.name}
                    </div>
                    <div className="item-description">
                      {item.description}
                    </div>
                  </div>

                  <div className="shop-item-footer">
                    <div className="price-section">
                      {item.discount > 0 && (
                        <div className="original-price">{item.price.toFixed(3)} SOL</div>
                      )}
                      <div className="current-price">
                        <img src="/assets/solana-icon.png" alt="SOL" className="price-sol-icon" />
                        {(item.price * (1 - item.discount / 100)).toFixed(3)} SOL
                      </div>
                      {solPrice && (
                        <div className="price-usd">
                          ≈ {formatUsdAmount(convertSolToUsd(item.price * (1 - item.discount / 100)) ?? 0)}
                        </div>
                      )}
                    </div>

                    <button
                      className={`purchase-btn ${
                        isPurchasing === item.id ? 'purchasing' :
                        purchaseSuccess === item.id ? 'success' :
                        (userProfile?.solana_balance || 0) < (item.price * (1 - item.discount / 100)) ? 'insufficient' : ''
                      }`}
                      onClick={() => handlePurchase(item)}
                      disabled={
                        isPurchasing === item.id ||
                        purchaseSuccess === item.id ||
                        (userProfile?.solana_balance || 0) < (item.price * (1 - item.discount / 100))
                      }
                    >
                      {isPurchasing === item.id ? (
                        <>
                          <Loader2 className="purchase-icon spinning" size={16} />
                          Purchasing...
                        </>
                      ) : purchaseSuccess === item.id ? (
                        <>
                          <CheckCircle className="purchase-icon" size={16} />
                          Purchased!
                        </>
                      ) : (userProfile?.solana_balance || 0) < (item.price * (1 - item.discount / 100)) ? (
                        <>
                          <AlertCircle className="purchase-icon" size={16} />
                          Insufficient Funds
                        </>
                      ) : (
                        <>
                          <DollarSign className="purchase-icon" size={16} />
                          Purchase
                        </>
                      )}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* PitPass Content */}
      {activeTab === 'pitpass' && (
        <div className="pitpass-main-content">
          {/* PitPass Header */}
          <div className="pitpass-header">
            <div className="pitpass-hero">
              <div className="pitpass-hero-content">
                <h1 className="pitpass-title">
                  <Crown size={32} className="pitpass-crown" />
                  PitPass Monthly
                </h1>
                <p className="pitpass-subtitle">
                  Unlock exclusive rewards, bonuses, and premium features
                </p>
                {userSubscription.isActive ? (
                  <div className="subscription-status active">
                    <div className="status-badge">
                      ✨ {pitPassTiers.find(t => t.id === userSubscription.currentTier)?.name || 'Active'}
                    </div>
                    <div className="status-details">
                      {userSubscription.daysRemaining} days remaining
                    </div>
                  </div>
                ) : (
                  <div className="subscription-status inactive">
                    <div className="status-badge">
                      🔒 No Active Subscription
                    </div>
                    <div className="status-details">
                      Subscribe to unlock premium benefits
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Subscription Tiers */}
          <div className="pitpass-tiers-section">
            <h2 className="section-title">Choose Your PitPass</h2>
            <div className="pitpass-tiers-grid">
              {pitPassTiers.map((tier) => (
                <div
                  key={tier.id}
                  className={`pitpass-tier-card ${selectedPitPassTier === tier.id ? 'selected' : ''} ${tier.popular ? 'popular' : ''}`}
                  onClick={() => setSelectedPitPassTier(tier.id as 'basic' | 'premium' | 'elite')}
                >
                  {tier.popular && (
                    <div className="popular-badge">
                      ⭐ Most Popular
                    </div>
                  )}

                  <div className="tier-header">
                    <h3 className="tier-name">{tier.name}</h3>
                    <div className="tier-pricing">
                      <span className="tier-price">${tier.price}</span>
                      {tier.originalPrice && (
                        <span className="tier-original-price">${tier.originalPrice}</span>
                      )}
                      <span className="tier-period">/month</span>
                    </div>
                  </div>

                  <div className="tier-features">
                    <h4 className="features-title">Features</h4>
                    <ul className="features-list">
                      {tier.features.map((feature, index) => (
                        <li key={index} className="feature-item">
                          <span className="feature-check">✓</span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="tier-bonuses">
                    <h4 className="bonuses-title">Monthly Bonuses</h4>
                    <div className="bonuses-grid">
                      <div className="bonus-item">
                        <span className="bonus-icon">💰</span>
                        <span className="bonus-text">+{(tier.monthlyBonuses.solBonus * 100).toFixed(0)}% SOL</span>
                      </div>
                      <div className="bonus-item">
                        <span className="bonus-icon">⚡</span>
                        <span className="bonus-text">{tier.monthlyBonuses.xpMultiplier}x XP</span>
                      </div>
                      <div className="bonus-item">
                        <span className="bonus-icon">🎨</span>
                        <span className="bonus-text">{tier.monthlyBonuses.exclusiveSkins} Skins</span>
                      </div>
                      {tier.monthlyBonuses.priorityQueue && (
                        <div className="bonus-item">
                          <span className="bonus-icon">🚀</span>
                          <span className="bonus-text">Priority Queue</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <button
                    className={`tier-subscribe-btn ${isPitPassPurchasing === tier.id ? 'purchasing' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsPitPassPurchasing(tier.id);
                      setShowPitPassConfirmation(true);
                    }}
                    disabled={isPitPassPurchasing === tier.id}
                  >
                    {isPitPassPurchasing === tier.id ? (
                      <>
                        <div className="loading-spinner"></div>
                        Processing...
                      </>
                    ) : userSubscription.isActive && userSubscription.currentTier === tier.id ? (
                      'Current Plan'
                    ) : userSubscription.isActive ? (
                      tier.price > (pitPassTiers.find(t => t.id === userSubscription.currentTier)?.price || 0) ? 'Upgrade' : 'Downgrade'
                    ) : (
                      'Subscribe Now'
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Benefits Showcase */}
          <div className="pitpass-benefits-section">
            <h2 className="section-title">What's Included</h2>

            <div className="benefits-showcase">
              <div className="benefit-category">
                <h3 className="category-title">
                  <span className="category-icon">🎨</span>
                  Exclusive Content
                </h3>
                <div className="benefit-items">
                  <div className="benefit-item">
                    <div className="benefit-preview">
                      <div className="cosmetic-preview">🐍</div>
                    </div>
                    <div className="benefit-info">
                      <h4>Monthly Exclusive Skins</h4>
                      <p>Unique designs available only to PitPass subscribers</p>
                      <span className="benefit-tier">All Tiers</span>
                    </div>
                  </div>
                  <div className="benefit-item">
                    <div className="benefit-preview">
                      <div className="cosmetic-preview">🏆</div>
                    </div>
                    <div className="benefit-info">
                      <h4>Exclusive Tournaments</h4>
                      <p>Compete in subscriber-only events with bigger prizes</p>
                      <span className="benefit-tier">Premium+</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="benefit-category">
                <h3 className="category-title">
                  <span className="category-icon">⚡</span>
                  Enhanced Experience
                </h3>
                <div className="benefit-items">
                  <div className="benefit-item">
                    <div className="benefit-preview">
                      <div className="reward-preview">
                        <span className="bonus-percentage">+25%</span>
                        <span className="bonus-label">SOL</span>
                      </div>
                    </div>
                    <div className="benefit-info">
                      <h4>Bonus Rewards</h4>
                      <p>Earn more SOL and XP from every match</p>
                      <span className="benefit-tier">All Tiers</span>
                    </div>
                  </div>
                  <div className="benefit-item">
                    <div className="benefit-preview">
                      <div className="access-preview">🚀</div>
                    </div>
                    <div className="benefit-info">
                      <h4>Priority Access</h4>
                      <p>Skip queues and get early access to new features</p>
                      <span className="benefit-tier">Premium+</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="benefit-category">
                <h3 className="category-title">
                  <span className="category-icon">🎯</span>
                  Premium Support
                </h3>
                <div className="benefit-items">
                  <div className="benefit-item">
                    <div className="benefit-preview">
                      <div className="access-preview">📊</div>
                    </div>
                    <div className="benefit-info">
                      <h4>Advanced Analytics</h4>
                      <p>Detailed performance insights and improvement tips</p>
                      <span className="benefit-tier">Premium+</span>
                    </div>
                  </div>
                  <div className="benefit-item">
                    <div className="benefit-preview">
                      <div className="access-preview">💬</div>
                    </div>
                    <div className="benefit-info">
                      <h4>Priority Support</h4>
                      <p>Get help faster with dedicated subscriber support</p>
                      <span className="benefit-tier">All Tiers</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Monthly Rewards Calendar */}
          <div className="pitpass-calendar-section">
            <h2 className="section-title">Monthly Rewards</h2>
            <div className="rewards-calendar">
              <div className="calendar-header">
                <h3>December 2024</h3>
                <p>Unlock daily rewards with your PitPass subscription</p>
              </div>
              <div className="calendar-grid">
                {Array.from({ length: 31 }, (_, i) => i + 1).map((day) => (
                  <div
                    key={day}
                    className={`calendar-day ${day <= new Date().getDate() ? 'available' : 'locked'} ${day === new Date().getDate() ? 'today' : ''}`}
                  >
                    <span className="day-number">{day}</span>
                    <div className="day-reward">
                      {day % 7 === 0 ? '🎁' : day % 5 === 0 ? '💎' : day % 3 === 0 ? '🎨' : '💰'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Subscription Management */}
          {userSubscription.isActive && (
            <div className="pitpass-management-section">
              <h2 className="section-title">Manage Your Subscription</h2>
              <div className="management-card">
                <div className="current-subscription-info">
                  <div className="subscription-details">
                    <h3>Current Plan: {pitPassTiers.find(t => t.id === userSubscription.currentTier)?.name}</h3>
                    <p>Next billing: {userSubscription.expiresAt?.toLocaleDateString()}</p>
                    <p>Auto-renewal: {userSubscription.autoRenew ? 'Enabled' : 'Disabled'}</p>
                  </div>
                  <div className="subscription-actions">
                    <button className="management-btn secondary">
                      {userSubscription.autoRenew ? 'Disable Auto-Renewal' : 'Enable Auto-Renewal'}
                    </button>
                    <button className="management-btn danger">
                      Cancel Subscription
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* PitPass Confirmation Modal */}
      {showPitPassConfirmation && (
        <div className="modal-overlay" onClick={() => setShowPitPassConfirmation(false)}>
          <div className="pitpass-confirmation-modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>Confirm Subscription</h2>
              <button
                className="modal-close-btn"
                onClick={() => setShowPitPassConfirmation(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-content">
              {isPitPassPurchasing && (
                <div className="confirmation-details">
                  <div className="selected-tier-info">
                    <h3>{pitPassTiers.find(t => t.id === isPitPassPurchasing)?.name}</h3>
                    <div className="tier-price-large">
                      ${pitPassTiers.find(t => t.id === isPitPassPurchasing)?.price}/month
                    </div>
                  </div>

                  <div className="billing-info">
                    <h4>Billing Information</h4>
                    <div className="billing-details">
                      <div className="billing-row">
                        <span>Subscription:</span>
                        <span>${pitPassTiers.find(t => t.id === isPitPassPurchasing)?.price}</span>
                      </div>
                      <div className="billing-row">
                        <span>Tax:</span>
                        <span>$0.00</span>
                      </div>
                      <div className="billing-row total">
                        <span>Total:</span>
                        <span>${pitPassTiers.find(t => t.id === isPitPassPurchasing)?.price}</span>
                      </div>
                    </div>
                  </div>

                  <div className="payment-method">
                    <h4>Payment Method</h4>
                    <div className="payment-option selected">
                      <div className="payment-icon">💳</div>
                      <div className="payment-details">
                        <span>Solana Wallet</span>
                        <small>Pay with SOL from your connected wallet</small>
                      </div>
                    </div>
                  </div>

                  <div className="terms-agreement">
                    <label className="checkbox-label">
                      <input type="checkbox" defaultChecked />
                      <span className="checkmark"></span>
                      I agree to the Terms of Service and Privacy Policy
                    </label>
                  </div>
                </div>
              )}
            </div>

            <div className="modal-footer">
              <button
                className="modal-btn secondary"
                onClick={() => setShowPitPassConfirmation(false)}
              >
                Cancel
              </button>
              <button
                className="modal-btn primary"
                onClick={() => {
                  // Handle subscription purchase
                  console.log('Processing subscription for:', isPitPassPurchasing);
                  setShowPitPassConfirmation(false);
                  setIsPitPassPurchasing(null);
                }}
              >
                Confirm Subscription
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Leaderboard Content */}
      {activeTab === 'leaderboard' && (
        <div className="leaderboard-main-content">
          {/* Leaderboard Header */}
          <div className="leaderboard-header">
            <div className="leaderboard-title-section">
              <h1 className="leaderboard-main-title">
                <Trophy className="leaderboard-title-icon" size={32} />
                Global Leaderboards
              </h1>
              <p className="leaderboard-subtitle">Compete with the best players worldwide</p>
            </div>

            <div className="leaderboard-user-rank">
              <div className="user-rank-card">
                <div className="rank-header">
                  <Medal className="rank-icon" size={20} />
                  <span className="rank-label">Your Rank</span>
                </div>
                <div className="rank-display">
                  <span className="rank-number">#10</span>
                  <span className="rank-category">in {selectedLeaderboardType}</span>
                </div>
                <div className="rank-stats">
                  <span className="rank-stat">Level {userProfile?.total_games_played ? Math.floor(userProfile.total_games_played / 10) + 1 : 45}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Two Panel Layout */}
          <div className="leaderboard-two-panel-container">
            {/* Left Panel - Small Column */}
            <div className="leaderboard-left-panel">
              {/* Category Filters */}
              <div className="filter-section">
                <h3 className="filter-title">Category</h3>
                <div className="filter-buttons-vertical">
                  <button
                    className={`filter-btn ${selectedLeaderboardCategory === 'global' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardCategory('global')}
                  >
                    <Users size={16} />
                    Global
                  </button>
                  <button
                    className={`filter-btn ${selectedLeaderboardCategory === 'friends' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardCategory('friends')}
                  >
                    <Users size={16} />
                    Friends
                  </button>
                  <button
                    className={`filter-btn ${selectedLeaderboardCategory === 'weekly' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardCategory('weekly')}
                  >
                    <Clock size={16} />
                    Weekly
                  </button>
                  <button
                    className={`filter-btn ${selectedLeaderboardCategory === 'monthly' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardCategory('monthly')}
                  >
                    <BarChart3 size={16} />
                    Monthly
                  </button>
                  <button
                    className={`filter-btn ${selectedLeaderboardCategory === 'alltime' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardCategory('alltime')}
                  >
                    <Crown size={16} />
                    All Time
                  </button>
                </div>
              </div>

              {/* Ranking Type Filters */}
              <div className="filter-section">
                <h3 className="filter-title">Ranking Type</h3>
                <div className="filter-buttons-vertical">
                  <button
                    className={`filter-btn ${selectedLeaderboardType === 'wins' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardType('wins')}
                  >
                    <Trophy size={16} />
                    Wins
                  </button>
                  <button
                    className={`filter-btn ${selectedLeaderboardType === 'score' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardType('score')}
                  >
                    <Target size={16} />
                    Score
                  </button>
                  <button
                    className={`filter-btn ${selectedLeaderboardType === 'kills' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardType('kills')}
                  >
                    <Sword size={16} />
                    Kills
                  </button>
                  <button
                    className={`filter-btn ${selectedLeaderboardType === 'survival' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardType('survival')}
                  >
                    <Clock size={16} />
                    Survival
                  </button>
                  <button
                    className={`filter-btn ${selectedLeaderboardType === 'earnings' ? 'active' : ''}`}
                    onClick={() => setSelectedLeaderboardType('earnings')}
                  >
                    <DollarSign size={16} />
                    Earnings
                  </button>
                </div>
              </div>
            </div>

            {/* Right Panel - Main Content */}
            <div className="leaderboard-right-panel">

          {/* Leaderboard Table */}
          <div className="leaderboard-table-container">
            <div className="leaderboard-table-header">
              <h2 className="table-title">
                {selectedLeaderboardCategory.charAt(0).toUpperCase() + selectedLeaderboardCategory.slice(1)} - {selectedLeaderboardType.charAt(0).toUpperCase() + selectedLeaderboardType.slice(1)}
              </h2>
              <div className="table-stats">
                <span className="total-players">
                  {getLeaderboardData().length} Players
                </span>
              </div>
            </div>

            {/* Top 3 Podium */}
            <div className="leaderboard-podium">
              {getLeaderboardData().slice(0, 3).map((player, index) => (
                <div key={player.rank} className={`podium-position position-${player.rank}`}>
                  <div className="podium-player">
                    <div className="player-avatar-large">
                      {player.avatar}
                    </div>
                    <div className="player-rank-badge" style={{ backgroundColor: getRankColor(player.rank) }}>
                      {getRankIcon(player.rank)}
                    </div>
                    <div className="player-info-podium">
                      <div className="player-name-large">{player.username}</div>
                      <div className="player-country">
                        <span className="country-flag">{player.country === 'US' ? '🇺🇸' : player.country === 'UK' ? '🇬🇧' : player.country === 'CA' ? '🇨🇦' : player.country === 'DE' ? '🇩🇪' : player.country === 'FR' ? '🇫🇷' : player.country === 'JP' ? '🇯🇵' : player.country === 'AU' ? '🇦🇺' : player.country === 'BR' ? '🇧🇷' : '🇰🇷'}</span>
                        <span className="country-code">{player.country}</span>
                      </div>
                      <div className="player-stat-main">
                        {selectedLeaderboardType === 'wins' && `${(player as any).wins} wins`}
                        {selectedLeaderboardType === 'score' && `${(player as any).score?.toLocaleString()} pts`}
                        {selectedLeaderboardType === 'kills' && `${(player as any).kills} kills`}
                        {selectedLeaderboardType === 'survival' && `${(player as any).survivalTime}`}
                        {selectedLeaderboardType === 'earnings' && `${player.earnings} SOL`}
                      </div>
                      <div className="player-level">Level {player.level}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Full Leaderboard Table */}
            <div className="leaderboard-table">
              <div className="table-header">
                <div className="header-cell rank-header">Rank</div>
                <div className="header-cell player-header">Player</div>
                <div className="header-cell stat-header">
                  {selectedLeaderboardType === 'wins' && 'Wins'}
                  {selectedLeaderboardType === 'score' && 'Score'}
                  {selectedLeaderboardType === 'kills' && 'Kills'}
                  {selectedLeaderboardType === 'survival' && 'Survival Time'}
                  {selectedLeaderboardType === 'earnings' && 'Earnings (SOL)'}
                </div>
                <div className="header-cell level-header">Level</div>
                <div className="header-cell streak-header">Streak</div>
                <div className="header-cell earnings-header">Earnings</div>
              </div>

              <div className="table-body">
                {getLeaderboardData().map((player, index) => (
                  <div
                    key={player.rank}
                    className={`table-row ${player.username === (userData.username || 'You') ? 'current-user' : ''} ${player.rank <= 3 ? 'top-three' : ''}`}
                  >
                    <div className="table-cell rank-cell">
                      <div className="rank-display-table">
                        <span className="rank-number" style={{ color: getRankColor(player.rank) }}>
                          {getRankIcon(player.rank)}
                        </span>
                        {player.rank <= 3 && (
                          <div className="rank-glow" style={{ backgroundColor: `${getRankColor(player.rank)}20` }}></div>
                        )}
                      </div>
                    </div>

                    <div className="table-cell player-cell">
                      <div className="player-info-table">
                        <div className="player-avatar-small">
                          {player.avatar}
                        </div>
                        <div className="player-details">
                          <div className="player-name-table">
                            {player.username}
                            {player.username === (userData.username || 'You') && (
                              <span className="you-badge">YOU</span>
                            )}
                          </div>
                          <div className="player-country-small">
                            <span className="country-flag-small">
                              {player.country === 'US' ? '🇺🇸' :
                               player.country === 'UK' ? '🇬🇧' :
                               player.country === 'CA' ? '🇨🇦' :
                               player.country === 'DE' ? '🇩🇪' :
                               player.country === 'FR' ? '🇫🇷' :
                               player.country === 'JP' ? '🇯🇵' :
                               player.country === 'AU' ? '🇦🇺' :
                               player.country === 'BR' ? '🇧🇷' : '🇰🇷'}
                            </span>
                            <span className="country-code-small">{player.country}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="table-cell stat-cell">
                      <div className="stat-value">
                        {selectedLeaderboardType === 'wins' && (
                          <span className="stat-number">{(player as any).wins?.toLocaleString()}</span>
                        )}
                        {selectedLeaderboardType === 'score' && (
                          <span className="stat-number">{(player as any).score?.toLocaleString()}</span>
                        )}
                        {selectedLeaderboardType === 'kills' && (
                          <span className="stat-number">{(player as any).kills?.toLocaleString()}</span>
                        )}
                        {selectedLeaderboardType === 'survival' && (
                          <span className="stat-number">{(player as any).survivalTime}</span>
                        )}
                        {selectedLeaderboardType === 'earnings' && (
                          <span className="stat-number">{player.earnings} SOL</span>
                        )}
                      </div>
                    </div>

                    <div className="table-cell level-cell">
                      <div className="level-badge">
                        <Star size={12} />
                        <span>{player.level}</span>
                      </div>
                    </div>

                    <div className="table-cell streak-cell">
                      <div className="streak-display">
                        <Flame size={12} />
                        <span>{player.streak}</span>
                      </div>
                    </div>

                    <div className="table-cell earnings-cell">
                      <div className="earnings-display">
                        <DollarSign size={12} />
                        <span>{player.earnings}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
            </div>
          </div>
        </div>
      )}
      <div
        className={`game-modes-panel ${isGameModesPanelVisible ? 'visible' : ''} ${drawerScrollOffset > 0 ? 'scrolled' : ''}`}
        style={{
          maxHeight: isGameModesPanelVisible
            ? `${70 + drawerScrollOffset}vh`
            : '70vh'
        }}
      >
        {/* Close chevron at top center - only visible when drawer is open */}
        {isGameModesPanelVisible && (
          <div className="game-modes-close-chevron" onClick={toggleGameModesPanel}>
            <ChevronDown size={16} />
            <ChevronDown size={16} />
          </div>
        )}

        <div className="game-modes-header">
          <div className="search-container">
            <Search className="search-icon" size={16} />
            <input
              type="text"
              placeholder="Search game modes..."
              className="search-input"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className="filter-container">
            <Filter size={16} style={{ color: 'rgba(255, 255, 255, 0.5)' }} />
            <button
              className={`filter-btn ${selectedFilter === 'all' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('all')}
            >
              All
            </button>
            <button
              className={`filter-btn ${selectedFilter === 'classic' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('classic')}
            >
              Classic
            </button>
            <button
              className={`filter-btn ${selectedFilter === 'warfare' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('warfare')}
            >
              Warfare
            </button>
            <button
              className={`filter-btn ${selectedFilter === 'speed' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('speed')}
            >
              Speed
            </button>
            <button
              className={`filter-btn ${selectedFilter === 'tournament' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('tournament')}
            >
              Tournament
            </button>
            <button
              className={`filter-btn ${selectedFilter === 'custom' ? 'active' : ''}`}
              onClick={() => setSelectedFilter('custom')}
            >
              Custom
            </button>
          </div>
        </div>

        <div className="game-modes-grid">
          {filteredGameModes.map((mode) => (
            <div
              key={mode.id}
              className={`game-mode-card ${selectedCardId === mode.id ? 'selected' : ''} ${(mode as any).isBossMode ? 'boss-mode' : ''}`}
              onClick={() => {
                setSelectedCardId(mode.id);
                setSelectedMode(mode.type as GameMode);
                setIsGameModesPanelVisible(false); // Auto-close drawer on selection
              }}
            >
              {/* Boss Mode Prize Badge */}
              {(mode as any).isBossMode && (
                <div className="boss-prize-badge">
                  {(mode as any).prize} BOSS BATTLE
                </div>
              )}

              {/* Lightning Effects for Boss Mode */}
              {(mode as any).isBossMode && (
                <div className="lightning-effect">
                  <div className="lightning-bolt"></div>
                  <div className="lightning-bolt"></div>
                  <div className="lightning-bolt"></div>
                </div>
              )}

              <div className="game-mode-image">
                <img
                  src={mode.image}
                  alt={mode.name}
                  className="game-mode-landscape-image"
                  onError={(e) => {
                    // Fallback to emoji if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const fallback = target.nextElementSibling as HTMLElement;
                    if (fallback) fallback.style.display = 'block';
                  }}
                />
                <span className="game-mode-icon-fallback" style={{ display: 'none' }}>
                  {mode.type === 'classic' ? '🐍' : mode.type === 'warfare' ? '⚔️' : mode.type === 'speed' ? '⚡' : mode.type === 'tournament' ? '🏆' : mode.type === 'leviathon' ? '🐲' : '🎮'}
                </span>
              </div>
              <h3 className="game-mode-title">{mode.name}</h3>
              <div className="game-mode-stats">
                <div className="game-mode-stat">
                  <Users size={12} />
                  {mode.players}
                </div>
                <div className="game-mode-stat">
                  <Clock size={12} />
                  {mode.duration}
                </div>
                <div className="game-mode-stat">
                  <Target size={12} />
                  {mode.difficulty}
                </div>
                <div className="game-mode-stat">
                  <Gamepad2 size={12} />
                  {mode.online} online
                </div>
                <div className="game-mode-stat game-mode-rating">
                  <Star size={12} />
                  {mode.rating}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Sol Balance Modal */}
      <SolBalanceModal
        isOpen={isSolBalanceModalOpen}
        onClose={() => setIsSolBalanceModalOpen(false)}
      />

    </div>
  );
};

export default GameModeSelectLobby;
