import React, { useEffect, useRef, useState } from 'react';

// Standalone Snake Builder that reuses ClientGame draw methods to preview designs without connecting to server.
// It creates an off-network ClientGame instance that only renders a single local snake using the same rendering
// pipeline: drawSmoothSnakeBody, drawSnakePattern, drawSnakeHead, etc.

type HeadShape = 'cobra' | 'python' | 'anaconda' | 'viper' | 'boa' | 'leviathan' | 'dragon' | 'mechanical' | 'alien' | 'behemoth';
type Skin = 'gold' | 'emerald' | 'amethyst' | 'ruby' | 'onyx' | 'aqua';
type SegmentDesign = 'none' | 'hex_scales' | 'dots' | 'rings' | 'cyber_plating';

type SnakeDesign = {
  headShape: HeadShape;
  skin: Skin;
  segmentDesign: SegmentDesign;
  baseColor: string; // resulting color used by ClientGame renderer
  eyeColor: string;
  accentColor: string;
  size: number; // base size passed to renderer
};

// Map UI skins to the color used by ClientGame snake.color
const SKIN_COLOR_MAP: Record<Skin, string> = {
  gold: '#FFD700',
  emerald: '#00e676',
  amethyst: '#9c27b0',
  ruby: '#e53935',
  onyx: '#444444',
  aqua: '#00e5ff',
};

// Initial design defaults
const DEFAULT_DESIGN: SnakeDesign = {
  headShape: 'cobra',
  skin: 'gold',
  segmentDesign: 'hex_scales',
  baseColor: SKIN_COLOR_MAP['gold'],
  eyeColor: '#00ffff',
  accentColor: '#ff3366',
  size: 14,
};

// Minimal snake state compatible with ClientGame renderer
function buildPreviewSnake(design: SnakeDesign, timeMs?: number) {
  // Generate a smooth curved line of segments that oscillates in place vertically (wave motion)
  const segments: { x: number; y: number }[] = [];
  const segCount = 28;
  const headX = 3000; // render space uses world coords, camera will transform to screen
  const headY = 3000;
  const t = (timeMs ?? Date.now()) * 0.001;

  // Oscillation parameters for an in-place "breathing/wave" motion
  const baseAmp = design.size * 1.1;   // vertical amplitude
  const travel = design.size * 2.2;    // spacing between segments along X axis
  const waveSpeed = 2.6;               // how fast the wave animates
  const wavelength = 0.55;             // spatial frequency of the sine wave

  for (let i = 0; i < segCount; i++) {
    const x = headX - i * travel;
    // Phase progresses along the body and over time to simulate wave motion
    const phase = (i * wavelength) + t * waveSpeed;
    const y = headY + Math.sin(phase) * baseAmp;
    segments.push({ x, y });
  }

  // Compute head angle from first two segments
  let angle = 0;
  if (segments.length > 1) {
    const h = segments[0];
    const n = segments[1];
    angle = Math.atan2(h.y - n.y, h.x - n.x);
  }

  // activePowerups left empty for preview; we can emulate effects later if desired
  return {
    id: 'preview',
    username: 'Builder',
    cash: 0,
    alive: true,
    color: design.baseColor,
    size: design.size,
    angle,
    segments,
    boosting: false,
    activePowerups: [],
    // additional fields that ClientGame may read
    invincible: false,
  };
}

type SnakeBuilderProps = {
  value?: Partial<SnakeDesign>;
  onChange?: (design: SnakeDesign) => void;
  height?: number;
  width?: number;
  className?: string;
};

const SnakeBuilder: React.FC<SnakeBuilderProps> = ({ value, onChange, height = 340, width = 640, className }) => {
  const [design, setDesign] = useState<SnakeDesign>(() => ({
    ...DEFAULT_DESIGN,
    ...value,
    baseColor: (value?.baseColor ?? SKIN_COLOR_MAP[value?.skin || DEFAULT_DESIGN.skin]),
  }));
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  // Keep baseColor in sync with skin selection unless the user has overridden it with a custom picker.
  // Also react to external preset changes via `value` prop and merge into local design.
  useEffect(() => {
    setDesign((prev) => {
      // If parent provided a new preset (value), merge it.
      // Priority: incoming value.* -> previous local -> defaults
      const merged: SnakeDesign = {
        ...DEFAULT_DESIGN,
        ...prev,
        ...value,
      };
      // If baseColor wasn't explicitly provided in value, derive from skin.
      if (!value?.baseColor) {
        merged.baseColor = SKIN_COLOR_MAP[merged.skin];
      }
      return merged;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value?.headShape, value?.skin, value?.segmentDesign, value?.baseColor, value?.eyeColor, value?.accentColor, value?.size]);

  // Standalone canvas render loop with in-file drawing utilities (no ClientGame import)
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Setup canvas resize based on element size
    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width || 640;
      canvas.height = rect.height || 340;
    };
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Virtual world and camera
    const worldWidth = 6000;
    const worldHeight = 6000;
    const camera = { x: 3000 - canvas.width / 2, y: 3000 - canvas.height / 2, zoom: 0.9 };

    let raf: number;
    const loop = () => {
      const current = latestDesignRef.current;
      const now = performance.now();
      const snake: any = buildPreviewSnake(current, now);
      const pulse = 0.03 * Math.sin(Date.now() * 0.004);
      snake.size = Math.max(6, current.size * (1 + pulse));
      snake.angle += Math.sin(Date.now() * 0.002) * 0.02;
      snake.color = current.baseColor;

      // Clear
      ctx.fillStyle = '#0a0a0a';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      ctx.save();
      ctx.scale(camera.zoom, camera.zoom);

      // Grid and world bounds
      drawGrid(ctx, camera, canvas);
      drawWorldBounds(ctx, camera, worldWidth, worldHeight);

      // Draw snake
      if (snake?.segments?.length) {
        drawRealisticSnake(ctx, camera, snake);
      }

      ctx.restore();

      raf = requestAnimationFrame(loop);
    };
    raf = requestAnimationFrame(loop);

    return () => {
      cancelAnimationFrame(raf);
      window.removeEventListener('resize', resizeCanvas);
    };
  }, []);

  // Keep a ref of the latest design so RAF loop always renders the newest values
  const latestDesignRef = useRef<SnakeDesign>(design);
  useEffect(() => {
    latestDesignRef.current = design;
    if (onChange) onChange(design);
  }, [design, onChange]);

  // Resize canvas element to provided props (pure canvas handling, no ClientGame dependency)
  useEffect(() => {
    const c = canvasRef.current;
    if (!c) return;
    // Set CSS size via style; actual pixel size is handled in the local resize routine inside the render loop effect.
    c.style.width = `${width}px`;
    c.style.height = `${height}px`;
  }, [width, height]);

  const update = <K extends keyof SnakeDesign>(key: K, value: SnakeDesign[K]) => {
    setDesign((d) => ({ ...d, [key]: value }));
  };

  const skinOptions: Skin[] = ['gold', 'emerald', 'amethyst', 'ruby', 'onyx', 'aqua'];
  const headOptions: HeadShape[] = ['cobra', 'python', 'anaconda', 'viper', 'boa', 'leviathan', 'dragon', 'mechanical', 'alien', 'behemoth'];
  const segmentOptions: SegmentDesign[] = ['none', 'hex_scales', 'dots', 'rings', 'cyber_plating'];

  // Drawing utilities replicated from ClientGame (subset needed for preview)
  function drawGrid(ctx: CanvasRenderingContext2D, camera: any, canvas: HTMLCanvasElement) {
    ctx.strokeStyle = '#1a1a1a';
    ctx.lineWidth = 1;
    const gridSize = 50;
    const startX = Math.floor(camera.x / gridSize) * gridSize;
    const startY = Math.floor(camera.y / gridSize) * gridSize;
    const endX = camera.x + canvas.width / camera.zoom;
    const endY = camera.y + canvas.height / camera.zoom;
    for (let x = startX; x < endX; x += gridSize) {
      ctx.beginPath();
      ctx.moveTo(x - camera.x, 0);
      ctx.lineTo(x - camera.x, canvas.height / camera.zoom);
      ctx.stroke();
    }
    for (let y = startY; y < endY; y += gridSize) {
      ctx.beginPath();
      ctx.moveTo(0, y - camera.y);
      ctx.lineTo(canvas.width / camera.zoom, y - camera.y);
      ctx.stroke();
    }
  }

  function drawWorldBounds(ctx: CanvasRenderingContext2D, camera: any, worldWidth: number, worldHeight: number) {
    ctx.strokeStyle = '#444444';
    ctx.lineWidth = 3;
    ctx.strokeRect(-camera.x, -camera.y, worldWidth, worldHeight);
  }

  function drawRealisticSnake(ctx: CanvasRenderingContext2D, camera: any, snake: any) {
    if (!snake.segments || snake.segments.length < 1) return;
    const isInvincible = snake.invincible;
    const shouldBlink = isInvincible && Math.sin(snake.blinkPhase || 0) < 0;
    if (shouldBlink) {
      drawWagerDisplay(ctx, camera, snake);
      return;
    }
    drawSmoothSnakeBody(ctx, camera, snake);
    drawSnakePattern(ctx, camera, snake);
    drawSnakeHead(ctx, camera, snake);
    drawWagerDisplay(ctx, camera, snake);
  }

  function drawSmoothSnakeBody(ctx: CanvasRenderingContext2D, camera: any, snake: any) {
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    const currentDesign = latestDesignRef.current;
    const headShape = currentDesign.headShape;
    
    // For leviathan, use black body with dark purple outline
    if (headShape === 'leviathan') {
      // Dark purple body outline
      ctx.strokeStyle = '#4a0080';
      if (snake.segments.length > 1) {
        ctx.lineWidth = snake.size * 2.2;
        ctx.beginPath();
        for (let i = 1; i < snake.segments.length; i++) {
          const seg = snake.segments[i];
          const x = seg.x - camera.x;
          const y = seg.y - camera.y;
          if (i === 1) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }
        ctx.stroke();
      }
      
      // Draw head
      const head = snake.segments[0];
      const headX = head.x - camera.x;
      const headY = head.y - camera.y;
      let headAngle = snake.angle || 0;
      if (!snake.angle && snake.segments.length > 1) {
        const neck = snake.segments[1];
        headAngle = Math.atan2(head.y - neck.y, head.x - neck.x);
      }
      
      drawShapedHead(ctx, headX, headY, snake.size, headAngle, headShape, snake.color);
      
      // Interior black body (smaller)
      if (snake.segments.length > 1) {
        ctx.strokeStyle = '#111111';
        ctx.lineWidth = snake.size * 1.8;
        ctx.beginPath();
        for (let i = 1; i < snake.segments.length; i++) {
          const seg = snake.segments[i];
          const x = seg.x - camera.x;
          const y = seg.y - camera.y;
          if (i === 1) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }
        ctx.stroke();
      }
      
      // Add lightning patterns along the body
      drawLightningPattern(ctx, camera, snake);
      
    } else {
      // Regular snake body rendering
      // Outline
      ctx.strokeStyle = snake.color;
      if (snake.segments.length > 1) {
        ctx.lineWidth = snake.size * 2.0;
        ctx.beginPath();
        for (let i = 1; i < snake.segments.length; i++) {
          const seg = snake.segments[i];
          const x = seg.x - camera.x;
          const y = seg.y - camera.y;
          if (i === 1) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }
        ctx.stroke();
      }
      
      // Draw head with shape-specific outline
      const head = snake.segments[0];
      const headX = head.x - camera.x;
      const headY = head.y - camera.y;
      let headAngle = snake.angle || 0;
      if (!snake.angle && snake.segments.length > 1) {
        const neck = snake.segments[1];
        headAngle = Math.atan2(head.y - neck.y, head.x - neck.x);
      }
      
      drawShapedHead(ctx, headX, headY, snake.size, headAngle, headShape, snake.color);
      
      // Interior black body
      if (snake.segments.length > 1) {
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = snake.size * 1.6;
        ctx.beginPath();
        for (let i = 1; i < snake.segments.length; i++) {
          const seg = snake.segments[i];
          const x = seg.x - camera.x;
          const y = seg.y - camera.y;
          if (i === 1) ctx.moveTo(x, y);
          else ctx.lineTo(x, y);
        }
        ctx.stroke();
      }
    }
  }

  function drawShapedHead(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, angle: number, headShape: HeadShape, color: string, sizeMultiplier: number = 1.4) {
    ctx.save();
    ctx.translate(x, y);
    ctx.rotate(angle);
    
    ctx.strokeStyle = color;
    ctx.lineWidth = size * 0.4;
    ctx.lineCap = 'round';
    ctx.fillStyle = '#000000';
    
    switch (headShape) {
      case 'cobra':
        // Cobra with distinct hood and pointed nose
        ctx.beginPath();
        // Start at nose tip
        ctx.moveTo(size * 3.0, 0);
        // Upper jaw line
        ctx.quadraticCurveTo(size * 1.5, -size * 0.8, size * 0.3, -size * 1.4);
        // Hood flare - upper
        ctx.quadraticCurveTo(-size * 0.4, -size * 2.4, -size * 1.6, -size * 1.1);
        // Back of head
        ctx.quadraticCurveTo(-size * 1.8, 0, -size * 1.6, size * 1.1);
        // Hood flare - lower
        ctx.quadraticCurveTo(-size * 0.4, size * 2.4, size * 0.3, size * 1.4);
        // Lower jaw line back to nose
        ctx.quadraticCurveTo(size * 1.5, size * 0.8, size * 3.0, 0);
        ctx.closePath();
        ctx.stroke();
        ctx.fill();
        break;
        
      case 'anaconda':
        // Broad diamond shape with prominent nose
        ctx.beginPath();
        ctx.moveTo(size * 3.2, 0);
        ctx.quadraticCurveTo(size * 1.1, -size * 1.8, -size * 1.1, -size * 0.7);
        ctx.quadraticCurveTo(-size * 1.6, 0, -size * 1.1, size * 0.7);
        ctx.quadraticCurveTo(size * 1.1, size * 1.8, size * 3.2, 0);
        ctx.closePath();
        ctx.stroke();
        ctx.fill();
        break;
        
      case 'viper':
        // Sharp triangular head with pointed nose
        ctx.beginPath();
        ctx.moveTo(size * 2.8, 0);
        ctx.lineTo(-size * 0.3, -size * 1.6);
        ctx.lineTo(-size * 1.1, 0);
        ctx.lineTo(-size * 0.3, size * 1.6);
        ctx.closePath();
        ctx.stroke();
        ctx.fill();
        break;
        
      case 'boa':
        // Rounded rectangular head with blunt nose
        ctx.beginPath();
        ctx.moveTo(size * 2.8, -size * 1.1);
        ctx.lineTo(-size * 0.6, -size * 1.1);
        ctx.quadraticCurveTo(-size * 1.1, -size * 1.1, -size * 1.1, -size * 0.6);
        ctx.lineTo(-size * 1.1, size * 0.6);
        ctx.quadraticCurveTo(-size * 1.1, size * 1.1, -size * 0.6, size * 1.1);
        ctx.lineTo(size * 2.8, size * 1.1);
        ctx.quadraticCurveTo(size * 3.0, size * 0.8, size * 3.0, 0);
        ctx.quadraticCurveTo(size * 3.0, -size * 0.8, size * 2.8, -size * 1.1);
        ctx.closePath();
        ctx.stroke();
        ctx.fill();
        break;
        
      case 'leviathan':
        // Menacing cyberpunk serpent head with angular, armored design
        ctx.fillStyle = '#000000';
        ctx.strokeStyle = '#4a0080'; // Dark purple outline
        ctx.lineWidth = size * 0.3;
        
        // Main armored head structure - angular and aggressive
        ctx.beginPath();
        // Start at nose tip (sharp point)
        ctx.moveTo(size * 3.5, 0);
        // Upper jaw with armor plating
        ctx.lineTo(size * 2.2, -size * 1.8);
        ctx.lineTo(size * 0.8, -size * 2.2);
        ctx.lineTo(-size * 0.5, -size * 1.8);
        ctx.lineTo(-size * 1.4, -size * 1.2);
        // Back of head with tech details
        ctx.lineTo(-size * 1.8, -size * 0.4);
        ctx.lineTo(-size * 1.8, size * 0.4);
        ctx.lineTo(-size * 1.4, size * 1.2);
        ctx.lineTo(-size * 0.5, size * 1.8);
        // Lower jaw armor
        ctx.lineTo(size * 0.8, size * 2.2);
        ctx.lineTo(size * 2.2, size * 1.8);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Add armored plating details
        ctx.strokeStyle = addAlpha(color, 0.8);
        ctx.lineWidth = size * 0.15;
        
        // Upper armor segments
        for (let i = 0; i < 3; i++) {
          const segX = size * (1.8 - i * 0.7);
          const segY1 = -size * (1.5 - i * 0.2);
          const segY2 = -size * (1.1 - i * 0.2);
          ctx.beginPath();
          ctx.moveTo(segX, segY1);
          ctx.lineTo(segX + size * 0.4, segY2);
          ctx.lineTo(segX - size * 0.4, segY2);
          ctx.closePath();
          ctx.stroke();
        }
        
        // Lower armor segments (mirrored)
        for (let i = 0; i < 3; i++) {
          const segX = size * (1.8 - i * 0.7);
          const segY1 = size * (1.5 - i * 0.2);
          const segY2 = size * (1.1 - i * 0.2);
          ctx.beginPath();
          ctx.moveTo(segX, segY1);
          ctx.lineTo(segX + size * 0.4, segY2);
          ctx.lineTo(segX - size * 0.4, segY2);
          ctx.closePath();
          ctx.stroke();
        }
        
        // Add glowing energy channels
        const energyTime = Date.now() * 0.003;
        const glowIntensity = 0.7 + Math.sin(energyTime) * 0.3;
        ctx.strokeStyle = `rgba(0, 255, 150, ${glowIntensity})`;
        ctx.lineWidth = size * 0.1;
        
        // Energy channels running along the head
        ctx.beginPath();
        ctx.moveTo(size * 2.5, -size * 0.8);
        ctx.quadraticCurveTo(size * 1.0, -size * 1.2, -size * 0.8, -size * 0.6);
        ctx.stroke();
        
        ctx.beginPath();
        ctx.moveTo(size * 2.5, size * 0.8);
        ctx.quadraticCurveTo(size * 1.0, size * 1.2, -size * 0.8, size * 0.6);
        ctx.stroke();
        
        break;
        
      case 'dragon':
        // Fierce dragon head with horns and spikes
        ctx.fillStyle = '#000000';
        ctx.strokeStyle = color;
        ctx.lineWidth = size * 0.3;
        
        // Main dragon skull shape
        ctx.beginPath();
        ctx.moveTo(size * 3.2, 0); // Nose tip
        ctx.quadraticCurveTo(size * 1.8, -size * 1.2, size * 0.5, -size * 1.8);
        ctx.lineTo(-size * 0.8, -size * 1.4);
        ctx.quadraticCurveTo(-size * 1.5, -size * 0.8, -size * 1.5, 0);
        ctx.quadraticCurveTo(-size * 1.5, size * 0.8, -size * 0.8, size * 1.4);
        ctx.lineTo(size * 0.5, size * 1.8);
        ctx.quadraticCurveTo(size * 1.8, size * 1.2, size * 3.2, 0);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Dragon horns
        const hornPositions = [
          {x: -size * 0.6, y: -size * 1.2, angle: -Math.PI/3},
          {x: -size * 0.6, y: size * 1.2, angle: Math.PI/3}
        ];
        
        hornPositions.forEach(horn => {
          ctx.strokeStyle = addAlpha(color, 0.9);
          ctx.lineWidth = size * 0.2;
          ctx.lineCap = 'round';
          
          const hornLength = size * 1.2;
          const hornEndX = horn.x + Math.cos(horn.angle) * hornLength;
          const hornEndY = horn.y + Math.sin(horn.angle) * hornLength;
          
          ctx.beginPath();
          ctx.moveTo(horn.x, horn.y);
          ctx.lineTo(hornEndX, hornEndY);
          ctx.stroke();
          
          // Horn tip spikes
          const spikeLength = hornLength * 0.3;
          for (let s = -1; s <= 1; s += 2) {
            const spikeAngle = horn.angle + s * Math.PI/4;
            ctx.beginPath();
            ctx.moveTo(hornEndX, hornEndY);
            ctx.lineTo(
              hornEndX + Math.cos(spikeAngle) * spikeLength,
              hornEndY + Math.sin(spikeAngle) * spikeLength
            );
            ctx.stroke();
          }
        });
        
        // Dragon ridges along the snout
        for (let i = 0; i < 4; i++) {
          const ridgeX = size * (2.5 - i * 0.6);
          const ridgeHeight = size * (0.3 - i * 0.05);
          ctx.strokeStyle = addAlpha(color, 0.7);
          ctx.lineWidth = size * 0.1;
          
          ctx.beginPath();
          ctx.moveTo(ridgeX, -ridgeHeight);
          ctx.lineTo(ridgeX, ridgeHeight);
          ctx.stroke();
        }
        break;
        
      case 'mechanical':
        // Robotic/mechanical head with tech details
        ctx.fillStyle = '#1a1a1a';
        ctx.strokeStyle = color;
        ctx.lineWidth = size * 0.25;
        
        // Main chassis - angular and geometric
        ctx.beginPath();
        ctx.moveTo(size * 3.0, 0);
        ctx.lineTo(size * 1.8, -size * 1.5);
        ctx.lineTo(size * 0.2, -size * 1.8);
        ctx.lineTo(-size * 1.2, -size * 1.0);
        ctx.lineTo(-size * 1.5, 0);
        ctx.lineTo(-size * 1.2, size * 1.0);
        ctx.lineTo(size * 0.2, size * 1.8);
        ctx.lineTo(size * 1.8, size * 1.5);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Mechanical vents and details
        ctx.strokeStyle = addAlpha('#00ff88', 0.8);
        ctx.lineWidth = size * 0.08;
        
        // Side vents
        for (let side = -1; side <= 1; side += 2) {
          for (let i = 0; i < 3; i++) {
            const ventX = size * (1.2 - i * 0.4);
            const ventY = side * size * (1.2 - i * 0.1);
            const ventLength = size * 0.6;
            
            ctx.beginPath();
            ctx.moveTo(ventX - ventLength/2, ventY);
            ctx.lineTo(ventX + ventLength/2, ventY);
            ctx.stroke();
          }
        }
        
        // Central processing unit indicator
        const cpuTime = Date.now() * 0.003;
        const cpuGlow = 0.7 + Math.sin(cpuTime) * 0.3;
        ctx.fillStyle = `rgba(0, 255, 136, ${cpuGlow})`;
        ctx.beginPath();
        ctx.arc(size * 0.5, 0, size * 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        // Angular antenna/sensors
        ctx.strokeStyle = addAlpha(color, 0.9);
        ctx.lineWidth = size * 0.15;
        const antennaPositions = [
          {x: -size * 0.8, y: -size * 0.8},
          {x: -size * 0.8, y: size * 0.8}
        ];
        
        antennaPositions.forEach(pos => {
          ctx.beginPath();
          ctx.moveTo(pos.x, pos.y);
          ctx.lineTo(pos.x - size * 0.8, pos.y * 1.5);
          ctx.stroke();
          
          // Sensor tip
          ctx.fillStyle = '#ff4444';
          ctx.beginPath();
          ctx.arc(pos.x - size * 0.8, pos.y * 1.5, size * 0.1, 0, Math.PI * 2);
          ctx.fill();
        });
        break;
        
      case 'alien':
        // Alien/xenomorph-inspired head
        ctx.fillStyle = '#0d0d0d';
        ctx.strokeStyle = color;
        ctx.lineWidth = size * 0.25;
        
        // Elongated alien skull
        ctx.beginPath();
        ctx.moveTo(size * 3.5, 0); // Extended nose
        ctx.quadraticCurveTo(size * 2.0, -size * 0.8, size * 0.8, -size * 1.6);
        ctx.quadraticCurveTo(-size * 0.2, -size * 2.0, -size * 2.0, -size * 1.2);
        // Elongated back of head
        ctx.quadraticCurveTo(-size * 2.8, -size * 0.4, -size * 2.8, 0);
        ctx.quadraticCurveTo(-size * 2.8, size * 0.4, -size * 2.0, size * 1.2);
        ctx.quadraticCurveTo(-size * 0.2, size * 2.0, size * 0.8, size * 1.6);
        ctx.quadraticCurveTo(size * 2.0, size * 0.8, size * 3.5, 0);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Alien biomechanical details
        ctx.strokeStyle = addAlpha('#444444', 0.8);
        ctx.lineWidth = size * 0.1;
        
        // Ridged skull pattern
        for (let i = 0; i < 5; i++) {
          const ridgeX = size * (1.5 - i * 0.6);
          const ridgeWidth = size * (1.2 - i * 0.15);
          
          ctx.beginPath();
          ctx.arc(ridgeX, 0, ridgeWidth, -Math.PI/3, Math.PI/3);
          ctx.stroke();
        }
        
        // Alien mandibles/jaw extensions
        for (let side = -1; side <= 1; side += 2) {
          ctx.strokeStyle = addAlpha(color, 0.7);
          ctx.lineWidth = size * 0.2;
          
          const mandibleStartX = size * 2.2;
          const mandibleStartY = side * size * 0.6;
          const mandibleEndX = size * 3.2;
          const mandibleEndY = side * size * 1.4;
          
          ctx.beginPath();
          ctx.moveTo(mandibleStartX, mandibleStartY);
          ctx.quadraticCurveTo(size * 2.8, side * size * 0.2, mandibleEndX, mandibleEndY);
          ctx.stroke();
        }
        break;
        
      case 'behemoth':
        // Massive fantasy creature head with multiple features
        ctx.fillStyle = '#0a0a0a';
        ctx.strokeStyle = color;
        ctx.lineWidth = size * 0.35;
        
        // Oversized brutal head shape
        ctx.beginPath();
        ctx.moveTo(size * 3.8, 0);
        ctx.quadraticCurveTo(size * 2.5, -size * 2.2, size * 0.0, -size * 2.5);
        ctx.quadraticCurveTo(-size * 1.8, -size * 1.8, -size * 2.2, 0);
        ctx.quadraticCurveTo(-size * 1.8, size * 1.8, size * 0.0, size * 2.5);
        ctx.quadraticCurveTo(size * 2.5, size * 2.2, size * 3.8, 0);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Multiple bone spikes/tusks
        const spikePositions = [
          {x: size * 1.5, y: -size * 1.8, angle: -Math.PI/6},
          {x: size * 1.5, y: size * 1.8, angle: Math.PI/6},
          {x: -size * 0.5, y: -size * 2.0, angle: -Math.PI/2.5},
          {x: -size * 0.5, y: size * 2.0, angle: Math.PI/2.5}
        ];
        
        ctx.strokeStyle = '#cccccc';
        ctx.lineWidth = size * 0.25;
        ctx.lineCap = 'round';
        
        spikePositions.forEach(spike => {
          const spikeLength = size * 1.5;
          const spikeEndX = spike.x + Math.cos(spike.angle) * spikeLength;
          const spikeEndY = spike.y + Math.sin(spike.angle) * spikeLength;
          
          ctx.beginPath();
          ctx.moveTo(spike.x, spike.y);
          ctx.lineTo(spikeEndX, spikeEndY);
          ctx.stroke();
        });
        
        // Battle scars
        ctx.strokeStyle = addAlpha('#aa0000', 0.6);
        ctx.lineWidth = size * 0.08;
        
        const scarPositions = [
          {start: {x: size * 0.8, y: -size * 0.5}, end: {x: size * 1.8, y: -size * 1.2}},
          {start: {x: size * 0.2, y: size * 0.8}, end: {x: size * 1.0, y: size * 1.5}},
          {start: {x: -size * 0.8, y: -size * 0.3}, end: {x: -size * 0.2, y: -size * 0.9}}
        ];
        
        scarPositions.forEach(scar => {
          ctx.beginPath();
          ctx.moveTo(scar.start.x, scar.start.y);
          ctx.lineTo(scar.end.x, scar.end.y);
          ctx.stroke();
        });
        
        // Armored plating
        ctx.strokeStyle = addAlpha(color, 0.5);
        ctx.lineWidth = size * 0.15;
        
        for (let i = 0; i < 3; i++) {
          const plateX = size * (1.5 - i * 0.8);
          const plateSize = size * (0.8 - i * 0.1);
          
          ctx.beginPath();
          ctx.arc(plateX, 0, plateSize, -Math.PI/4, Math.PI/4);
          ctx.stroke();
        }
        break;
        
      case 'python':
      default:
        // Classic oval head with defined nose
        ctx.beginPath();
        ctx.moveTo(size * 2.8, 0);
        ctx.quadraticCurveTo(size * 1.1, -size * 1.4, -size * 0.8, -size * 0.6);
        ctx.quadraticCurveTo(-size * 1.1, 0, -size * 0.8, size * 0.6);
        ctx.quadraticCurveTo(size * 1.1, size * 1.4, size * 2.8, 0);
        ctx.closePath();
        ctx.stroke();
        ctx.fill();
        break;
    }
    
    ctx.restore();
  }
  
  function drawFangs(ctx: CanvasRenderingContext2D, x: number, y: number, headSize: number, angle: number, headShape: HeadShape, color: string) {
    const fangLength = headSize * 0.8;
    const fangWidth = headSize * 0.15;
    
    // Position fangs at the front corners of the mouth
    for (let i = -1; i <= 1; i += 2) {
      const fangX = x + Math.cos(angle) * headSize * 0.8 + Math.cos(angle + Math.PI/2) * headSize * 0.3 * i;
      const fangY = y + Math.sin(angle) * headSize * 0.8 + Math.sin(angle + Math.PI/2) * headSize * 0.3 * i;
      
      ctx.save();
      ctx.translate(fangX, fangY);
      ctx.rotate(angle);
      
      // Draw sharp, curved fang
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.moveTo(0, -fangWidth/2);
      ctx.quadraticCurveTo(fangLength * 0.3, -fangWidth/4, fangLength * 0.8, 0);
      ctx.quadraticCurveTo(fangLength * 0.3, fangWidth/4, 0, fangWidth/2);
      ctx.closePath();
      ctx.fill();
      
      // Add a subtle shadow/outline
      ctx.strokeStyle = 'rgba(0, 0, 0, 0.3)';
      ctx.lineWidth = 1;
      ctx.stroke();
      
      ctx.restore();
    }
  }
  
  function drawLightningBolt(ctx: CanvasRenderingContext2D, x: number, y: number, headSize: number, angle: number, accentColor: string) {
    const lightningLength = headSize * 3.5;
    const noseOffset = headSize * 2.2;
    const time = Date.now() * 0.01;
    
    const startX = x + Math.cos(angle) * noseOffset;
    const startY = y + Math.sin(angle) * noseOffset;
    
    ctx.save();
    ctx.translate(startX, startY);
    ctx.rotate(angle);
    
    // Create jagged lightning bolt path
    const segments = 8;
    const segmentLength = lightningLength / segments;
    let currentX = 0;
    let currentY = 0;
    
    // Main lightning bolt - purple
    ctx.strokeStyle = '#aa00ff';
    ctx.lineWidth = headSize * 0.2;
    ctx.lineCap = 'round';
    ctx.shadowColor = '#aa00ff';
    ctx.shadowBlur = 10;
    
    ctx.beginPath();
    ctx.moveTo(currentX, currentY);
    
    for (let i = 1; i <= segments; i++) {
      const nextX = i * segmentLength;
      const zigzag = Math.sin(time + i) * headSize * 0.8 * (Math.random() - 0.5);
      currentY += zigzag;
      
      ctx.lineTo(nextX, currentY);
      
      // Add random branches
      if (i % 3 === 0 && Math.random() > 0.5) {
        const branchX = nextX;
        const branchY = currentY;
        const branchLength = segmentLength * 0.6;
        const branchAngle = (Math.random() - 0.5) * Math.PI * 0.8;
        
        ctx.moveTo(branchX, branchY);
        ctx.lineTo(
          branchX + Math.cos(branchAngle) * branchLength,
          branchY + Math.sin(branchAngle) * branchLength
        );
        ctx.moveTo(nextX, currentY);
      }
    }
    ctx.stroke();
    
    // Secondary green lightning
    ctx.strokeStyle = '#00ff88';
    ctx.lineWidth = headSize * 0.1;
    ctx.shadowColor = '#00ff88';
    ctx.shadowBlur = 8;
    
    currentX = 0;
    currentY = 0;
    ctx.beginPath();
    ctx.moveTo(currentX, currentY);
    
    for (let i = 1; i <= segments; i++) {
      const nextX = i * segmentLength;
      const zigzag = Math.sin(time * 1.5 + i + Math.PI/3) * headSize * 0.6 * (Math.random() - 0.5);
      currentY += zigzag;
      ctx.lineTo(nextX, currentY);
    }
    ctx.stroke();
    
    ctx.shadowBlur = 0;
    ctx.restore();
  }
  
  function drawLightningPattern(ctx: CanvasRenderingContext2D, camera: any, snake: any) {
    const segments = snake.segments;
    if (!segments || segments.length < 3) return;
    
    const time = Date.now() * 0.005;
    
    // Draw lightning veins along the body
    for (let i = 2; i < segments.length - 1; i += 3) {
      const seg = segments[i];
      const prevSeg = segments[i - 1];
      const nextSeg = segments[i + 1];
      
      const x = seg.x - camera.x;
      const y = seg.y - camera.y;
      const prevX = prevSeg.x - camera.x;
      const prevY = prevSeg.y - camera.y;
      const nextX = nextSeg.x - camera.x;
      const nextY = nextSeg.y - camera.y;
      
      // Calculate perpendicular direction for lightning branches
      const bodyAngle = Math.atan2(nextY - prevY, nextX - prevX);
      const perpAngle1 = bodyAngle + Math.PI / 2;
      const perpAngle2 = bodyAngle - Math.PI / 2;
      
      const branchLength = snake.size * (0.8 + Math.sin(time + i * 0.5) * 0.3);
      const alpha = 0.6 + Math.sin(time * 2 + i) * 0.4;
      
      // Purple lightning veins
      ctx.strokeStyle = `rgba(170, 0, 255, ${alpha})`;
      ctx.lineWidth = 2;
      ctx.shadowColor = '#aa00ff';
      ctx.shadowBlur = 6;
      
      // Branch 1
      if (i % 6 === 2) {
        const endX1 = x + Math.cos(perpAngle1) * branchLength;
        const endY1 = y + Math.sin(perpAngle1) * branchLength;
        
        ctx.beginPath();
        ctx.moveTo(x, y);
        // Add zigzag to make it look like lightning
        const midX = x + Math.cos(perpAngle1) * branchLength * 0.5;
        const midY = y + Math.sin(perpAngle1) * branchLength * 0.5;
        const zigzagX = midX + Math.cos(bodyAngle) * snake.size * 0.3 * Math.sin(time * 3 + i);
        const zigzagY = midY + Math.sin(bodyAngle) * snake.size * 0.3 * Math.sin(time * 3 + i);
        
        ctx.lineTo(zigzagX, zigzagY);
        ctx.lineTo(endX1, endY1);
        ctx.stroke();
      }
      
      // Branch 2 (other side)
      if (i % 6 === 5) {
        const endX2 = x + Math.cos(perpAngle2) * branchLength;
        const endY2 = y + Math.sin(perpAngle2) * branchLength;
        
        ctx.beginPath();
        ctx.moveTo(x, y);
        const midX = x + Math.cos(perpAngle2) * branchLength * 0.5;
        const midY = y + Math.sin(perpAngle2) * branchLength * 0.5;
        const zigzagX = midX + Math.cos(bodyAngle) * snake.size * 0.3 * Math.sin(time * 3 + i + Math.PI);
        const zigzagY = midY + Math.sin(bodyAngle) * snake.size * 0.3 * Math.sin(time * 3 + i + Math.PI);
        
        ctx.lineTo(zigzagX, zigzagY);
        ctx.lineTo(endX2, endY2);
        ctx.stroke();
      }
      
      // Green accents (smaller, more frequent)
      if (i % 4 === 0) {
        ctx.strokeStyle = `rgba(0, 255, 136, ${alpha * 0.8})`;
        ctx.lineWidth = 1;
        ctx.shadowColor = '#00ff88';
        ctx.shadowBlur = 4;
        
        const shortLength = branchLength * 0.4;
        const shortEndX = x + Math.cos(perpAngle1 + Math.PI/4) * shortLength;
        const shortEndY = y + Math.sin(perpAngle1 + Math.PI/4) * shortLength;
        
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(shortEndX, shortEndY);
        ctx.stroke();
      }
    }
    
    // Central lightning spine running along the body
    if (segments.length > 4) {
      const spineAlpha = 0.4 + Math.sin(time * 1.5) * 0.3;
      ctx.strokeStyle = `rgba(0, 255, 200, ${spineAlpha})`;
      ctx.lineWidth = 3;
      ctx.shadowColor = '#00ffc8';
      ctx.shadowBlur = 8;
      
      ctx.beginPath();
      for (let i = 1; i < segments.length; i += 2) {
        const seg = segments[i];
        const x = seg.x - camera.x;
        const y = seg.y - camera.y;
        
        if (i === 1) ctx.moveTo(x, y);
        else ctx.lineTo(x, y);
      }
      ctx.stroke();
    }
    
    ctx.shadowBlur = 0;
  }

  function drawSnakePattern(ctx: CanvasRenderingContext2D, camera: any, snake: any) {
    const currentDesign = latestDesignRef.current;
    if (currentDesign.segmentDesign === 'none') return;
    
    const segments = snake.segments;
    if (!segments || segments.length < 2) return;
    
    for (let i = 1; i < segments.length; i++) {
      const seg = segments[i];
      const x = seg.x - camera.x;
      const y = seg.y - camera.y;
      const segmentSize = snake.size * (1 - i * 0.02); // Gradually decrease size
      
      if (currentDesign.segmentDesign === 'hex_scales') {
        drawHexagonScale(ctx, x, y, segmentSize, currentDesign.accentColor, i);
      } else if (currentDesign.segmentDesign === 'dots') {
        drawDotPattern(ctx, x, y, segmentSize, currentDesign.accentColor, i);
      } else if (currentDesign.segmentDesign === 'rings') {
        drawRingPattern(ctx, x, y, segmentSize, currentDesign.accentColor, i);
      } else if (currentDesign.segmentDesign === 'cyber_plating') {
        drawCyberPlating(ctx, x, y, segmentSize, currentDesign.accentColor, i);
      }
    }
  }
  
  function drawHexagonScale(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string, segmentIndex: number) {
    const hexSize = size * 0.6;
    const alpha = Math.max(0.2, 1 - segmentIndex * 0.03);
    
    ctx.save();
    ctx.translate(x, y);
    
    // Main hexagon
    ctx.strokeStyle = addAlpha(color, alpha);
    ctx.fillStyle = addAlpha(color, alpha * 0.3);
    ctx.lineWidth = 2;
    
    ctx.beginPath();
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI) / 3;
      const hx = Math.cos(angle) * hexSize;
      const hy = Math.sin(angle) * hexSize;
      if (i === 0) ctx.moveTo(hx, hy);
      else ctx.lineTo(hx, hy);
    }
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // Inner smaller hexagon for detail
    const innerSize = hexSize * 0.5;
    ctx.strokeStyle = addAlpha(darkenColor(color, 0.3), alpha);
    ctx.lineWidth = 1;
    
    ctx.beginPath();
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI) / 3;
      const hx = Math.cos(angle) * innerSize;
      const hy = Math.sin(angle) * innerSize;
      if (i === 0) ctx.moveTo(hx, hy);
      else ctx.lineTo(hx, hy);
    }
    ctx.closePath();
    ctx.stroke();
    
    ctx.restore();
  }
  
  function drawDotPattern(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string, segmentIndex: number) {
    const dotSize = size * 0.25;
    const alpha = Math.max(0.2, 1 - segmentIndex * 0.03);
    
    ctx.fillStyle = addAlpha(color, alpha);
    ctx.beginPath();
    ctx.arc(x, y, dotSize, 0, Math.PI * 2);
    ctx.fill();
    
    // Smaller center dot
    ctx.fillStyle = addAlpha(darkenColor(color, 0.4), alpha);
    ctx.beginPath();
    ctx.arc(x, y, dotSize * 0.4, 0, Math.PI * 2);
    ctx.fill();
  }
  
  function drawRingPattern(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string, segmentIndex: number) {
    const ringSize = size * 0.7;
    const alpha = Math.max(0.2, 1 - segmentIndex * 0.03);
    
    ctx.strokeStyle = addAlpha(color, alpha);
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(x, y, ringSize, 0, Math.PI * 2);
    ctx.stroke();
    
    // Inner ring
    ctx.strokeStyle = addAlpha(color, alpha * 0.6);
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.arc(x, y, ringSize * 0.6, 0, Math.PI * 2);
    ctx.stroke();
  }
  
  function drawCyberPlating(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string, segmentIndex: number) {
    const plateSize = size * 0.8;
    const alpha = Math.max(0.3, 1 - segmentIndex * 0.02);
    const time = Date.now() * 0.002;
    const energyPulse = 0.6 + Math.sin(time + segmentIndex * 0.5) * 0.4;
    
    ctx.save();
    ctx.translate(x, y);
    
    // Main armored plate (diamond shape)
    ctx.fillStyle = addAlpha('#001122', alpha * 0.8);
    ctx.strokeStyle = addAlpha(color, alpha);
    ctx.lineWidth = 2;
    
    ctx.beginPath();
    ctx.moveTo(plateSize * 0.8, 0);
    ctx.lineTo(0, -plateSize * 0.6);
    ctx.lineTo(-plateSize * 0.8, 0);
    ctx.lineTo(0, plateSize * 0.6);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // Energy channels on the plate
    ctx.strokeStyle = `rgba(0, 255, 150, ${energyPulse * alpha})`;
    ctx.lineWidth = 1;
    
    // Horizontal energy line
    ctx.beginPath();
    ctx.moveTo(-plateSize * 0.5, 0);
    ctx.lineTo(plateSize * 0.5, 0);
    ctx.stroke();
    
    // Vertical energy line
    ctx.beginPath();
    ctx.moveTo(0, -plateSize * 0.4);
    ctx.lineTo(0, plateSize * 0.4);
    ctx.stroke();
    
    // Corner tech details
    const cornerSize = plateSize * 0.15;
    for (let corner = 0; corner < 4; corner++) {
      const cornerAngle = (corner * Math.PI) / 2;
      const cornerX = Math.cos(cornerAngle) * plateSize * 0.5;
      const cornerY = Math.sin(cornerAngle) * plateSize * 0.5;
      
      ctx.fillStyle = `rgba(0, 255, 150, ${energyPulse * alpha * 0.8})`;
      ctx.beginPath();
      ctx.arc(cornerX, cornerY, cornerSize, 0, Math.PI * 2);
      ctx.fill();
    }
    
    ctx.restore();
  }

  function drawSnakeHead(ctx: CanvasRenderingContext2D, camera: any, snake: any) {
    const head = snake.segments[0];
    const x = head.x - camera.x;
    const y = head.y - camera.y;
    const time = Date.now() * 0.001;
    const headSize = snake.size * 1.2;
    let eyeAngle = snake.angle || 0;
    if (!snake.angle && snake.segments.length > 1) {
      const neck = snake.segments[1];
      eyeAngle = Math.atan2(head.y - neck.y, head.x - neck.x);
    }

    const currentDesign = latestDesignRef.current;
    const headShape = currentDesign.headShape;
    
    // Draw eyes (position varies by head shape)
    drawSnakeEyes(ctx, x, y, headSize, eyeAngle, headShape, currentDesign.eyeColor);
    
    // Draw menacing fangs (but not for leviathan)
    if (headShape !== 'leviathan') {
      drawFangs(ctx, x, y, headSize, eyeAngle, headShape, '#ffffff');
    }
    
    // Draw aggressive tongue or lightning
    if (headShape === 'leviathan') {
      // Leviathan spits lightning instead of tongue
      if (Math.sin(time * 8) > 0.6) {
        drawLightningBolt(ctx, x, y, headSize, eyeAngle, currentDesign.accentColor);
      }
    } else {
      // Regular aggressive tongue for other head shapes
      if (Math.sin(time * 6) > 0.5) {
        const tongueLength = headSize * 2.5;
        const tongueAngle = eyeAngle;
        // Position tongue at the nose tip based on head shape
        let noseOffset = headSize * 1.4;
        switch (headShape) {
          case 'cobra':
            noseOffset = headSize * 1.8;
            break;
          case 'anaconda':
            noseOffset = headSize * 2.0;
            break;
          case 'viper':
            noseOffset = headSize * 1.6;
            break;
          case 'boa':
            noseOffset = headSize * 1.7;
            break;
          case 'python':
          default:
            noseOffset = headSize * 1.6;
            break;
        }
        
        const tongueStartX = x + Math.cos(tongueAngle) * noseOffset;
        const tongueStartY = y + Math.sin(tongueAngle) * noseOffset;
        const tongueEndX = tongueStartX + Math.cos(tongueAngle) * tongueLength;
        const tongueEndY = tongueStartY + Math.sin(tongueAngle) * tongueLength;
        
        // Thicker, more aggressive tongue
        ctx.strokeStyle = '#ff0040';
        ctx.lineWidth = headSize * 0.18;
        ctx.lineCap = 'round';
        ctx.beginPath();
        ctx.moveTo(tongueStartX, tongueStartY);
        ctx.lineTo(tongueEndX, tongueEndY);
        ctx.stroke();
        
        // Forked tongue ends - more pronounced
        for (let i = -1; i <= 1; i += 2) {
          ctx.beginPath();
          ctx.moveTo(tongueEndX, tongueEndY);
          ctx.lineTo(tongueEndX + Math.cos(tongueAngle + Math.PI/3 * i) * tongueLength * 0.4, tongueEndY + Math.sin(tongueAngle + Math.PI/3 * i) * tongueLength * 0.4);
          ctx.stroke();
        }
      }
    }
    drawActivePowerupEffects(ctx, camera, snake, x, y, headSize);
  }

  function drawSnakeEyes(ctx: CanvasRenderingContext2D, x: number, y: number, headSize: number, angle: number, headShape: HeadShape, eyeColor: string) {
    let eyeDistance = headSize * 0.5;
    let eyeSize = headSize * 0.35;
    let eyeOffset = 0;
    
    // Adjust eye position based on head shape and larger head sizes
    switch (headShape) {
      case 'cobra':
        eyeDistance = headSize * 0.8;
        eyeOffset = headSize * 0.4;
        eyeSize = headSize * 0.3;
        break;
      case 'anaconda':
        eyeDistance = headSize * 0.9;
        eyeOffset = headSize * 0.6;
        eyeSize = headSize * 0.35;
        break;
      case 'viper':
        eyeDistance = headSize * 0.6;
        eyeOffset = headSize * 0.5;
        eyeSize = headSize * 0.28;
        break;
      case 'boa':
        eyeDistance = headSize * 0.7;
        eyeOffset = headSize * 0.5;
        eyeSize = headSize * 0.32;
        break;
      case 'leviathan':
        eyeDistance = headSize * 1.1;
        eyeOffset = headSize * 0.8;
        eyeSize = headSize * 0.4;
        break;
      case 'dragon':
        eyeDistance = headSize * 0.9;
        eyeOffset = headSize * 0.6;
        eyeSize = headSize * 0.35;
        break;
      case 'mechanical':
        eyeDistance = headSize * 1.0;
        eyeOffset = headSize * 0.5;
        eyeSize = headSize * 0.3;
        break;
      case 'alien':
        eyeDistance = headSize * 1.2;
        eyeOffset = headSize * 0.9;
        eyeSize = headSize * 0.25;
        break;
      case 'behemoth':
        eyeDistance = headSize * 1.3;
        eyeOffset = headSize * 0.7;
        eyeSize = headSize * 0.4;
        break;
      case 'python':
      default:
        eyeDistance = headSize * 0.7;
        eyeOffset = headSize * 0.4;
        eyeSize = headSize * 0.3;
        break;
    }
    
    for (let i = -1; i <= 1; i += 2) {
      const eyeX = x + Math.cos(angle + Math.PI/2) * eyeDistance * i + Math.cos(angle) * eyeOffset;
      const eyeY = y + Math.sin(angle + Math.PI/2) * eyeDistance * i + Math.sin(angle) * eyeOffset;
      
      if (headShape === 'leviathan') {
        // Cyberpunk Leviathan eyes with energy effects
        const time = Date.now() * 0.004;
        const energyPulse = 0.6 + Math.sin(time + i) * 0.4;
        
        // Outer energy field
        ctx.fillStyle = `rgba(0, 255, 200, ${energyPulse * 0.3})`;
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 1.5, 0, Math.PI * 2);
        ctx.fill();
        
        // Tech hexagon frame around eye
        ctx.strokeStyle = `rgba(0, 255, 150, ${energyPulse})`;
        ctx.lineWidth = eyeSize * 0.15;
        ctx.beginPath();
        for (let h = 0; h < 6; h++) {
          const hexAngle = (h * Math.PI) / 3;
          const hx = eyeX + Math.cos(hexAngle) * eyeSize * 1.2;
          const hy = eyeY + Math.sin(hexAngle) * eyeSize * 1.2;
          if (h === 0) ctx.moveTo(hx, hy);
          else ctx.lineTo(hx, hy);
        }
        ctx.closePath();
        ctx.stroke();
        
        // Main cybernetic eye
        ctx.fillStyle = '#001122';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize, 0, Math.PI * 2);
        ctx.fill();
        
        // Glowing iris
        ctx.fillStyle = `rgba(0, 255, 200, ${energyPulse})`;
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 0.8, 0, Math.PI * 2);
        ctx.fill();
        
        // Central core
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        // Scanning lines
        for (let s = 0; s < 3; s++) {
          const scanY = eyeY - eyeSize + (s * eyeSize * 0.6);
          ctx.strokeStyle = `rgba(0, 255, 150, ${energyPulse * 0.7})`;
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.moveTo(eyeX - eyeSize * 0.8, scanY);
          ctx.lineTo(eyeX + eyeSize * 0.8, scanY);
          ctx.stroke();
        }
        
      } else if (headShape === 'dragon') {
        // Dragon eyes - fierce and glowing
        const dragonGlow = 0.8 + Math.sin(Date.now() * 0.004 + i) * 0.2;
        
        // Glowing orange/red dragon eye
        ctx.fillStyle = `rgba(255, 100, 0, ${dragonGlow})`;
        ctx.shadowColor = '#ff6400';
        ctx.shadowBlur = 15;
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 1.3, 0, Math.PI * 2);
        ctx.fill();
        
        // Main eye
        ctx.fillStyle = '#ff4400';
        ctx.shadowBlur = 8;
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize, 0, Math.PI * 2);
        ctx.fill();
        
        // Dragon slit pupil
        ctx.fillStyle = '#000000';
        ctx.shadowBlur = 0;
        ctx.save();
        ctx.translate(eyeX, eyeY);
        ctx.rotate(angle + Math.PI/2);
        ctx.fillRect(-eyeSize * 0.08, -eyeSize * 0.9, eyeSize * 0.16, eyeSize * 1.8);
        ctx.restore();
        
      } else if (headShape === 'mechanical') {
        // Mechanical camera/sensor eyes
        const scanPulse = 0.6 + Math.sin(Date.now() * 0.008 + i * Math.PI) * 0.4;
        
        // Outer sensor ring
        ctx.strokeStyle = `rgba(0, 255, 88, ${scanPulse})`;
        ctx.lineWidth = eyeSize * 0.2;
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 1.2, 0, Math.PI * 2);
        ctx.stroke();
        
        // Main sensor lens
        ctx.fillStyle = '#001122';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize, 0, Math.PI * 2);
        ctx.fill();
        
        // Inner lens elements
        for (let ring = 0.3; ring <= 0.8; ring += 0.25) {
          ctx.strokeStyle = `rgba(0, 255, 88, ${scanPulse * ring})`;
          ctx.lineWidth = 1;
          ctx.beginPath();
          ctx.arc(eyeX, eyeY, eyeSize * ring, 0, Math.PI * 2);
          ctx.stroke();
        }
        
        // Center LED indicator
        ctx.fillStyle = `rgba(255, 0, 0, ${scanPulse})`;
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 0.15, 0, Math.PI * 2);
        ctx.fill();
        
      } else if (headShape === 'alien') {
        // Alien eyes - large, black, menacing
        // Outer alien glow
        ctx.fillStyle = 'rgba(100, 0, 150, 0.3)';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Main black alien eye
        ctx.fillStyle = '#000000';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize, 0, Math.PI * 2);
        ctx.fill();
        
        // Alien eye reflection/depth
        ctx.fillStyle = 'rgba(80, 0, 120, 0.8)';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 0.7, 0, Math.PI * 2);
        ctx.fill();
        
        // Small highlight for alien menace
        ctx.fillStyle = 'rgba(200, 0, 255, 0.6)';
        ctx.beginPath();
        ctx.arc(eyeX - eyeSize * 0.3, eyeY - eyeSize * 0.3, eyeSize * 0.1, 0, Math.PI * 2);
        ctx.fill();
        
      } else if (headShape === 'behemoth') {
        // Behemoth eyes - massive, brutal, scarred
        const brutalGlow = 0.7 + Math.sin(Date.now() * 0.003 + i) * 0.3;
        
        // Outer scarred tissue
        ctx.fillStyle = `rgba(120, 0, 0, ${brutalGlow * 0.5})`;
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 1.4, 0, Math.PI * 2);
        ctx.fill();
        
        // Main eye
        ctx.fillStyle = '#440000';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize, 0, Math.PI * 2);
        ctx.fill();
        
        // Bloodshot veins
        ctx.strokeStyle = `rgba(200, 0, 0, ${brutalGlow})`;
        ctx.lineWidth = 1;
        for (let vein = 0; vein < 4; vein++) {
          const veinAngle = (vein * Math.PI) / 2;
          ctx.beginPath();
          ctx.moveTo(eyeX, eyeY);
          ctx.lineTo(
            eyeX + Math.cos(veinAngle) * eyeSize * 0.8,
            eyeY + Math.sin(veinAngle) * eyeSize * 0.8
          );
          ctx.stroke();
        }
        
        // Brutal pupil
        ctx.fillStyle = '#000000';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 0.4, 0, Math.PI * 2);
        ctx.fill();
        
      } else {
        // Regular aggressive eyes for other head shapes
        // Aggressive glowing outer eye ring
        ctx.fillStyle = '#ff0000';
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize * 1.2, 0, Math.PI * 2);
        ctx.fill();
        
        // Main eye
        ctx.fillStyle = eyeColor;
        ctx.beginPath();
        ctx.arc(eyeX, eyeY, eyeSize, 0, Math.PI * 2);
        ctx.fill();
        
        // Aggressive slit pupil (vertical)
        ctx.fillStyle = '#000000';
        ctx.save();
        ctx.translate(eyeX, eyeY);
        ctx.rotate(angle + Math.PI/2);
        ctx.fillRect(-eyeSize * 0.1, -eyeSize * 0.8, eyeSize * 0.2, eyeSize * 1.6);
        ctx.restore();
        
        // Eye highlight for menacing look
        ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
        ctx.beginPath();
        ctx.arc(eyeX - eyeSize * 0.2, eyeY - eyeSize * 0.2, eyeSize * 0.15, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  }

  function drawWagerDisplay(ctx: CanvasRenderingContext2D, camera: any, snake: any) {
    if (!snake.segments?.length) return;
    const headX = snake.segments[0].x - camera.x;
    const headY = snake.segments[0].y - camera.y;
    const username = snake.username || 'Player';
    const cash = snake.cash || 0;

    ctx.save();
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.font = 'bold 12px "Orbitron", monospace';
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillText(username, headX + 1, headY - snake.size * 3.2 - 5);
    ctx.lineWidth = 3;
    ctx.strokeStyle = snake.color;
    ctx.strokeText(username, headX, headY - snake.size * 3.2 - 6);
    ctx.fillStyle = '#333333';
    ctx.fillText(username, headX, headY - snake.size * 3.2 - 6);

    const formattedCash = cash >= 1000 ? `$${(cash / 1000).toFixed(1)}K` : `$${Math.floor(cash)}`;
    ctx.font = 'bold 11px "Orbitron", monospace';
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillText(formattedCash, headX + 1, headY - snake.size * 2.6 + 9);
    ctx.fillStyle = '#00FF00';
    ctx.fillText(formattedCash, headX, headY - snake.size * 2.6 + 8);
    ctx.restore();
  }

  function addAlpha(color: string, alpha: number) {
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }
    return color;
  }

  function darkenColor(color: string, factor: number) {
    if (color.startsWith('#')) {
      const r = Math.floor(parseInt(color.slice(1, 3), 16) * (1 - factor));
      const g = Math.floor(parseInt(color.slice(3, 5), 16) * (1 - factor));
      const b = Math.floor(parseInt(color.slice(5, 7), 16) * (1 - factor));
      return `rgb(${r}, ${g}, ${b})`;
    }
    return color;
  }

  function drawVacuumIndicator(ctx: CanvasRenderingContext2D, snake: any, headX: number, headY: number) {
    const vacuumRadius = snake.size * (snake.boosting ? 8 : 6);
    const alpha = (snake.boosting ? 0.25 : 0.15) * (0.7 + Math.sin(Date.now() * 0.004) * 0.3);
    ctx.strokeStyle = addAlpha(snake.color, alpha);
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.arc(headX, headY, vacuumRadius, 0, Math.PI * 2);
    ctx.stroke();
    ctx.setLineDash([]);
  }

  function isKing(players: any[], snake: any) {
    if (!players?.length) return false;
    const king = players.reduce((p, c) => (c.cash > p.cash) ? c : p);
    return king.id === snake.id;
  }

  function drawActivePowerupEffects(ctx: CanvasRenderingContext2D, camera: any, snake: any, headX: number, headY: number, headSize: number) {
    snake.activePowerups?.forEach((powerup: any) => {
      const screenSegments = snake.segments.map((seg: any) => ({...seg, x: seg.x - camera.x, y: seg.y - camera.y}));
      switch (powerup.type) {
        case 'helmet': {
          const activeHelmet = snake.activePowerups?.find((p: any) => p.type === 'helmet');
          if (activeHelmet) {
            ctx.strokeStyle = '#AAAAAA';
            ctx.lineWidth = headSize * 0.4;
            ctx.beginPath();
            ctx.arc(headX, headY, headSize, Math.PI, Math.PI * 2);
            ctx.closePath();
        ctx.stroke();
        ctx.fill();
          }
          break;
        }
        case 'forcefield': {
          ctx.strokeStyle = 'rgba(0, 255, 255, 0.5)';
          ctx.lineWidth = 4;
          ctx.beginPath();
          screenSegments.forEach((seg: any, i: number) => {
            if (i === 0) ctx.moveTo(seg.x, seg.y);
            else ctx.lineTo(seg.x, seg.y);
          });
          ctx.closePath();
        ctx.stroke();
        ctx.fill();
          break;
        }
        case 'battering_ram': {
          if (snake.boosting) {
            ctx.fillStyle = 'rgba(255, 100, 0, 0.7)';
            ctx.beginPath();
            const angle = snake.angle || 0;
            const ramHeadX = headX + Math.cos(angle) * headSize;
            const ramHeadY = headY + Math.sin(angle) * headSize;
            ctx.moveTo(ramHeadX + Math.cos(angle) * headSize, ramHeadY + Math.sin(angle) * headSize);
            ctx.lineTo(ramHeadX + Math.cos(angle - 1.2) * headSize, ramHeadY + Math.sin(angle - 1.2) * headSize);
            ctx.lineTo(ramHeadX + Math.cos(angle + 1.2) * headSize, ramHeadY + Math.sin(angle + 1.2) * headSize);
            ctx.closePath();
            ctx.fill();
          }
          break;
        }
        case 'shield_generator': {
          ctx.fillStyle = 'rgba(0, 170, 255, 0.3)';
          ctx.beginPath();
          ctx.arc(headX, headY, headSize * 2, 0, Math.PI * 2);
          ctx.fill();
          break;
        }
        case 'speed_boost': {
          const screenCoordsSegments = snake.segments.map((s: any) => ({...s, x: s.x - camera.x, y: s.y - camera.y}));
          drawSpeedBoostEffect(ctx, screenCoordsSegments, headSize, snake.boosting || false);
          break;
        }
        case 'damage_amplifier': {
          const glow = ctx.createRadialGradient(headX, headY, 0, headX, headY, headSize * 2);
          glow.addColorStop(0, 'rgba(255, 68, 68, 0.5)');
          glow.addColorStop(1, 'transparent');
          ctx.fillStyle = glow;
          ctx.beginPath();
          ctx.arc(headX, headY, headSize * 2, 0, Math.PI * 2);
          ctx.fill();
          break;
        }
      }
    });
  }

  function drawSpeedBoostEffect(ctx: CanvasRenderingContext2D, segments: any[], headSize: number, isBoosting: boolean) {
    if (!segments || segments.length === 0) return;
    const time = Date.now() * 0.005;
    ctx.save();
    const trailColor = 'rgba(255, 221, 0, 0.5)';
    const headX = segments[0].x;
    const headY = segments[0].y;
    const baseAuraSize = headSize * (isBoosting ? 1.5 : 1.2);
    const auraPulse = (Math.sin(time * (isBoosting ? 10 : 5)) + 1) / 2;
    const currentAuraSize = baseAuraSize * (0.8 + auraPulse * 0.4);
    const headAngle = Math.atan2(segments[0].y - segments[1].y, segments[0].x - segments[1].x);
    ctx.beginPath();
    ctx.moveTo(headX + Math.cos(headAngle - Math.PI / 2) * headSize * 0.5, headY + Math.sin(headAngle - Math.PI / 2) * headSize * 0.5);
    for (let i = 0; i < 3; i++) {
      const flareAngle = headAngle + (i - 1) * (isBoosting ? 0.5 : 0.3);
      const flareLength = currentAuraSize * (1 + Math.random() * 0.3);
      ctx.lineTo(headX + Math.cos(flareAngle) * flareLength, headY + Math.sin(flareAngle) * flareLength);
    }
    ctx.lineTo(headX + Math.cos(headAngle + Math.PI / 2) * headSize * 0.5, headY + Math.sin(headAngle + Math.PI / 2) * headSize * 0.5);
    ctx.closePath();
    const headGradient = ctx.createRadialGradient(headX, headY, headSize * 0.2, headX, headY, currentAuraSize);
    headGradient.addColorStop(0, `rgba(255, 221, 0, ${0.8 + auraPulse * 0.2})`);
    headGradient.addColorStop(0.5, `rgba(255, 170, 0, ${0.5 + auraPulse * 0.3})`);
    headGradient.addColorStop(1, 'transparent');
    ctx.fillStyle = headGradient;
    ctx.fill();
    if (segments.length > 1) {
      ctx.beginPath();
      ctx.moveTo(segments[0].x, segments[0].y);
      for (let i = 1; i < segments.length; i++) {
        const seg = segments[i];
        const prevSeg = segments[i - 1];
        const controlX = (prevSeg.x + seg.x) / 2;
        const controlY = (prevSeg.y + seg.y) / 2;
        ctx.quadraticCurveTo(prevSeg.x, prevSeg.y, controlX, controlY);
      }
      ctx.lineWidth = headSize * 0.3 * (0.5 + auraPulse);
      ctx.strokeStyle = trailColor;
      ctx.globalAlpha = 0.3 + auraPulse * 0.3;
      ctx.stroke();
      ctx.globalAlpha = 1.0;
    }
    ctx.restore();
  }

  return (
    <div className={className} style={{ display: 'grid', gridTemplateColumns: '320px 1fr', gap: 16, alignItems: 'start' }}>
      <div style={{ background: '#111', border: '1px solid #333', borderRadius: 8, padding: 12, color: '#ddd' }}>
        <h3 style={{ marginTop: 0 }}>Snake Builder</h3>

        <label style={{ display: 'block', marginBottom: 8 }}>
          <div style={{ fontSize: 12, opacity: 0.85 }}>Head Shape</div>
          <select
            value={design.headShape}
            onChange={(e) => update('headShape', e.target.value as HeadShape)}
            style={{ width: '100%', background: '#1a1a1a', color: '#eee', border: '1px solid #333', padding: 8, borderRadius: 6 }}
          >
            {headOptions.map((h) => (
              <option key={h} value={h}>{h}</option>
            ))}
          </select>
        </label>

        <label style={{ display: 'block', marginBottom: 8 }}>
          <div style={{ fontSize: 12, opacity: 0.85 }}>Skin</div>
          <select
            value={design.skin}
            onChange={(e) => {
              const skin = e.target.value as Skin;
              setDesign((d) => ({
                ...d,
                skin,
                baseColor: SKIN_COLOR_MAP[skin],
              }));
            }}
            style={{ width: '100%', background: '#1a1a1a', color: '#eee', border: '1px solid #333', padding: 8, borderRadius: 6 }}
          >
            {skinOptions.map((s) => (
              <option key={s} value={s}>{s}</option>
            ))}
          </select>
        </label>

        <label style={{ display: 'block', marginBottom: 8 }}>
          <div style={{ fontSize: 12, opacity: 0.85 }}>Segment Design</div>
          <select
            value={design.segmentDesign}
            onChange={(e) => update('segmentDesign', e.target.value as SegmentDesign)}
            style={{ width: '100%', background: '#1a1a1a', color: '#eee', border: '1px solid #333', padding: 8, borderRadius: 6 }}
          >
            {segmentOptions.map((s) => (
              <option key={s} value={s}>{s}</option>
            ))}
          </select>
        </label>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 8 }}>
          <label style={{ display: 'block', marginBottom: 8 }}>
            <div style={{ fontSize: 12, opacity: 0.85 }}>Base Color</div>
            <input
              type="color"
              value={design.baseColor}
              onChange={(e) => update('baseColor', e.target.value)}
              style={{ width: '100%', height: 36, background: '#1a1a1a', border: '1px solid #333', borderRadius: 6 }}
            />
          </label>
          <label style={{ display: 'block', marginBottom: 8 }}>
            <div style={{ fontSize: 12, opacity: 0.85 }}>Eye Color</div>
            <input
              type="color"
              value={design.eyeColor}
              onChange={(e) => update('eyeColor', e.target.value)}
              style={{ width: '100%', height: 36, background: '#1a1a1a', border: '1px solid #333', borderRadius: 6 }}
            />
          </label>
        </div>

        <label style={{ display: 'block', marginBottom: 8 }}>
          <div style={{ fontSize: 12, opacity: 0.85 }}>Accent Color</div>
          <input
            type="color"
            value={design.accentColor}
            onChange={(e) => update('accentColor', e.target.value)}
            style={{ width: '100%', height: 36, background: '#1a1a1a', border: '1px solid #333', borderRadius: 6 }}
          />
        </label>

        <label style={{ display: 'block', marginBottom: 8 }}>
          <div style={{ fontSize: 12, opacity: 0.85 }}>Size</div>
          <input
            type="range"
            min={8}
            max={24}
            value={design.size}
            onChange={(e) => update('size', Number(e.target.value))}
            style={{ width: '100%' }}
          />
          <div style={{ fontSize: 12, opacity: 0.8, marginTop: 4 }}>{design.size}</div>
        </label>

        <div style={{ marginTop: 12, fontSize: 12, color: '#9bd' }}>
          This builder is standalone and uses the same renderer as in-game.
        </div>
      </div>

      <div style={{ background: '#0b0b0b', border: '1px solid #333', borderRadius: 8, position: 'relative' }}>
        <canvas ref={canvasRef} style={{ display: 'block', width, height, borderRadius: 8 }} />
      </div>
    </div>
  );
};

export default SnakeBuilder;
