import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ExternalLink } from 'lucide-react';

interface NewsItem {
  id: number;
  title: string;
  image: string;
  url: string;
  description?: string;
  date?: string;
}

interface NewsSliderProps {
  className?: string;
}

const NewsSlider: React.FC<NewsSliderProps> = ({ className = '' }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Sample news data - in a real app, this would come from an API
  const newsItems: NewsItem[] = [
    {
      id: 1,
      title: "New Tournament Mode Released!",
      image: "/assets/classicMode.png",
      url: "https://snakepit.game/news/tournament-mode",
      description: "Experience competitive snake battles like never before",
      date: "2024-01-15"
    },
    {
      id: 2,
      title: "Weekly Leaderboard Reset",
      image: "/assets/warfaremode.png",
      url: "https://snakepit.game/news/leaderboard-reset",
      description: "New week, new chances to climb the ranks",
      date: "2024-01-14"
    },
    {
      id: 3,
      title: "SOL Rewards Program Launch",
      image: "/assets/solana-icon.png",
      url: "https://snakepit.game/news/rewards-program",
      description: "Earn SOL tokens by playing and winning games",
      date: "2024-01-13"
    },
    {
      id: 4,
      title: "New Snake Skins Available",
      image: "/assets/classicMode.png",
      url: "https://snakepit.game/shop/skins",
      description: "Check out the latest cosmetic additions to the shop",
      date: "2024-01-12"
    },
    {
      id: 5,
      title: "Community Event: Snake Wars",
      image: "/assets/warfaremode.png",
      url: "https://snakepit.game/events/snake-wars",
      description: "Join the biggest community event of the year",
      date: "2024-01-11"
    }
  ];

  // Auto-advance slides
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % newsItems.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(interval);
  }, [isAutoPlaying, newsItems.length]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
    // Resume auto-play after 10 seconds of inactivity
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + newsItems.length) % newsItems.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % newsItems.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const handleNewsClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className={`news-slider ${className}`}>
      <div className="news-slider-header">
        <h3 className="news-slider-title">Latest News</h3>
        <div className="news-slider-controls">
          <button 
            className="news-control-btn" 
            onClick={goToPrevious}
            aria-label="Previous news item"
          >
            <ChevronLeft size={16} />
          </button>
          <button 
            className="news-control-btn" 
            onClick={goToNext}
            aria-label="Next news item"
          >
            <ChevronRight size={16} />
          </button>
        </div>
      </div>

      <div className="news-slider-container">
        <div 
          className="news-slider-track"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {newsItems.map((item) => (
            <div 
              key={item.id} 
              className="news-slide"
              onClick={() => handleNewsClick(item.url)}
            >
              <div className="news-slide-image">
                <img 
                  src={item.image} 
                  alt={item.title}
                  onError={(e) => {
                    // Fallback to a default news image if the specific image fails to load
                    const target = e.target as HTMLImageElement;
                    target.src = '/assets/classicMode.png';
                  }}
                />
                <div className="news-slide-overlay">
                  <ExternalLink size={16} className="news-external-icon" />
                </div>
              </div>
              <div className="news-slide-content">
                <h4 className="news-slide-title">{item.title}</h4>
                {item.description && (
                  <p className="news-slide-description">{item.description}</p>
                )}
                {item.date && (
                  <span className="news-slide-date">
                    {new Date(item.date).toLocaleDateString()}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Slide indicators */}
      <div className="news-slider-indicators">
        {newsItems.map((_, index) => (
          <button
            key={index}
            className={`news-indicator ${index === currentSlide ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default NewsSlider;

// Add styles to the component
const styles = `
  .news-slider {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 320px;
  }

  .news-slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .news-slider-title {
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
  }

  .news-slider-controls {
    display: flex;
    gap: 4px;
  }

  .news-control-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .news-control-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.3);
  }

  .news-slider-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 12px;
  }

  .news-slider-track {
    display: flex;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .news-slide {
    flex: 0 0 100%;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
  }

  .news-slide:hover {
    transform: scale(1.02);
  }

  .news-slide-image {
    position: relative;
    width: 100%;
    height: 160px;
    border-radius: 8px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
  }

  .news-slide-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .news-slide:hover .news-slide-image img {
    transform: scale(1.05);
  }

  .news-slide-overlay {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px;
    padding: 4px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .news-slide:hover .news-slide-overlay {
    opacity: 1;
  }

  .news-external-icon {
    color: #ffffff;
  }

  .news-slide-content {
    padding: 12px 0 0 0;
  }

  .news-slide-title {
    font-size: 14px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 6px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .news-slide-description {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 6px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .news-slide-date {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 500;
  }

  .news-slider-indicators {
    display: flex;
    justify-content: center;
    gap: 6px;
  }

  .news-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: none;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .news-indicator:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  .news-indicator.active {
    background: #ffffff;
    transform: scale(1.2);
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .news-slider {
      max-width: 280px;
    }

    .news-slide-image {
      height: 140px;
    }

    .news-slider-title {
      font-size: 14px;
    }

    .news-slide-title {
      font-size: 13px;
    }

    .news-slide-description {
      font-size: 11px;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
