import React, { useState, useEffect, useCallback } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { getUserTransactions, Transaction } from '../lib/supabase'
import WalletBalance from './WalletBalance'
import { useSolanaPrice } from '../hooks/useSolanaPrice'

interface UserProfileProps {
  isOpen: boolean
  onClose: () => void
}

interface ProfileTab {
  id: string
  name: string
  icon: string
}

const UserProfile: React.FC<UserProfileProps> = ({ isOpen, onClose }) => {
  const { user, userProfile, updateProfile, signOut } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [formData, setFormData] = useState({
    username: '',
    audioMuted: false,
    audioVolume: 0.6
  })
  
  const {
    solPrice,
    convertSolToUsd,
    formatUsdAmount
  } = useSolanaPrice({ autoRefresh: true })

  const tabs: ProfileTab[] = [
    { id: 'overview', name: 'Overview', icon: 'icon-user' },
    { id: 'inventory', name: 'Inventory', icon: 'icon-bag' },
    { id: 'transactions', name: 'Transactions', icon: 'icon-dollar' },
    { id: 'settings', name: 'Settings', icon: 'icon-settings' },
    { id: 'achievements', name: 'Achievements', icon: 'icon-trophy' }
  ]

  useEffect(() => {
    if (userProfile) {
      setFormData({
        username: userProfile.username,
        audioMuted: userProfile.audio_muted,
        audioVolume: userProfile.audio_volume
      })
    }
  }, [userProfile])

  const loadTransactions = useCallback(async () => {
    if (!user) return
    setLoading(true)
    try {
      const data = await getUserTransactions(user.id, 50)
      setTransactions(data)
    } catch (error) {
      console.error('Error loading transactions:', error)
    } finally {
      setLoading(false)
    }
  }, [user])

  useEffect(() => {
    if (isOpen && activeTab === 'transactions' && user) {
      loadTransactions()
    }
  }, [isOpen, activeTab, user, loadTransactions])

  const handleSaveProfile = async () => {
    if (!user || !userProfile) return

    const success = await updateProfile({
      username: formData.username,
      audio_muted: formData.audioMuted,
      audio_volume: formData.audioVolume
    })

    if (success) {
      setEditMode(false)
      console.log('Profile updated successfully')
    } else {
      console.error('Failed to update profile')
    }
  }

  const handleSignOut = async () => {
    await signOut()
    onClose()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit': return 'icon-credit-card'
      case 'withdrawal': return 'icon-bank'
      case 'wager': return 'icon-dice'
      case 'cashout': return 'icon-dollar'
      default: return 'icon-file'
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'deposit': return 'neon-green'
      case 'withdrawal': return 'neon-orange'
      case 'wager': return 'neon-red'
      case 'cashout': return 'neon-yellow'
      default: return 'neon-dim'
    }
  }

  if (!isOpen || !user || !userProfile) return null

  return (
    <div className="profile-overlay">
      <div className="profile-modal">
        <div className="profile-header">
          <div className="profile-title">
            <h2 className="neon-text neon-cyan"><span className="icon-snake"></span> Snake Profile</h2>
            <div className="profile-subtitle neon-text neon-dim">
              Welcome back, {userProfile.username}!
            </div>
          </div>
          <button className="close-button neon-button neon-orange" onClick={onClose}>
            <span className="icon-close"></span>
          </button>
        </div>

        <div className="profile-content">
          {/* Tab Navigation */}
          <div className="profile-tabs">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`profile-tab ${activeTab === tab.id ? 'active' : ''} neon-button neon-purple`}
                onClick={() => setActiveTab(tab.id)}
              >
                <span className={tab.icon}></span> {tab.name}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="profile-tab-content">
            {activeTab === 'overview' && (
              <div className="overview-content">
                <div className="profile-stats-grid">
                  <div className="stat-card">
                    <div className="stat-icon neon-text neon-green">
                      <img src="/assets/solana-icon.png" alt="Solana" style={{ width: '50px', height: '50px', border: '2px solid transparent', background: 'linear-gradient(45deg, #9945FF, #14F195, #00D4AA, #9945FF) border-box', backgroundClip: 'padding-box, border-box', backgroundOrigin: 'padding-box, border-box', borderRadius: '8px' }} />
                    </div>
                    <div className="stat-info">
                      <div className="stat-label neon-text neon-dim">SOL Balance</div>
                      <div className="stat-value">
                        <WalletBalance 
                          showRefreshButton={true}
                          showFaucetLink={true}
                          autoRefresh={true}
                          refreshInterval={15000}
                        />
                        {solPrice && userProfile.solana_balance && (
                          <div className="stat-usd neon-text neon-dim" style={{ fontSize: '0.7em', marginTop: '2px' }}>
                            {formatUsdAmount(convertSolToUsd(userProfile.solana_balance) || 0)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon neon-text neon-yellow"><span className="icon-gamepad"></span></div>
                    <div className="stat-info">
                      <div className="stat-label neon-text neon-dim">Games Played</div>
                      <div className="stat-value neon-text neon-yellow">
                        {userProfile.total_games_played}
                      </div>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon neon-text neon-cyan"><span className="icon-trophy"></span></div>
                    <div className="stat-info">
                      <div className="stat-label neon-text neon-dim">Wins</div>
                      <div className="stat-value neon-text neon-cyan">
                        {userProfile.total_wins}
                      </div>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon neon-text neon-pink"><span className="icon-dollar"></span></div>
                    <div className="stat-info">
                      <div className="stat-label neon-text neon-dim">Total Earnings</div>
                      <div className="stat-value neon-text neon-pink">
                        {userProfile.total_earnings.toFixed(2)} SOL
                        {solPrice && (
                          <div className="earning-usd neon-text neon-dim" style={{ fontSize: '0.8em', marginTop: '2px' }}>
                            {formatUsdAmount(convertSolToUsd(userProfile.total_earnings) || 0)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="profile-info-card">
                  <h3 className="neon-text neon-purple">Account Information</h3>
                  <div className="info-grid">
                    <div className="info-item">
                      <span className="info-label neon-text neon-dim">Username:</span>
                      <span className="info-value neon-text neon-cyan">{userProfile.username}</span>
                    </div>
                    <div className="info-item">
                      <span className="info-label neon-text neon-dim">Member Since:</span>
                      <span className="info-value neon-text neon-green">
                        {formatDate(userProfile.created_at)}
                      </span>
                    </div>
                    <div className="info-item">
                      <span className="info-label neon-text neon-dim">Win Rate:</span>
                      <span className="info-value neon-text neon-yellow">
                        {userProfile.total_games_played > 0 
                          ? ((userProfile.total_wins / userProfile.total_games_played) * 100).toFixed(1)
                          : '0'}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'inventory' && (
              <div className="inventory-content">
                <h3 className="neon-text neon-purple"><span className="icon-bag"></span> Your Collection</h3>
                <div className="inventory-grid">
                  <div className="inventory-item coming-soon">
                    <div className="item-icon"><span className="icon-snake"></span></div>
                    <div className="item-name neon-text neon-green">Snake Skins</div>
                    <div className="item-description neon-text neon-dim">Coming Soon</div>
                  </div>
                  <div className="inventory-item coming-soon">
                    <div className="item-icon"><span className="icon-sword"></span></div>
                    <div className="item-name neon-text neon-red">Weapons</div>
                    <div className="item-description neon-text neon-dim">Coming Soon</div>
                  </div>
                  <div className="inventory-item coming-soon">
                    <div className="item-icon"><span className="icon-shield"></span></div>
                    <div className="item-name neon-text neon-cyan">Power-ups</div>
                    <div className="item-description neon-text neon-dim">Coming Soon</div>
                  </div>
                  <div className="inventory-item coming-soon">
                    <div className="item-icon"><span className="icon-trophy"></span></div>
                    <div className="item-name neon-text neon-yellow">Badges</div>
                    <div className="item-description neon-text neon-dim">Coming Soon</div>
                  </div>
                </div>
                <div className="coming-soon-note neon-text neon-dim">
                  <span className="icon-rocket"></span> NFT items and collectibles will be available with Solana integration!
                </div>
              </div>
            )}

            {activeTab === 'transactions' && (
              <div className="transactions-content">
                <div className="transactions-header">
                  <h3 className="neon-text neon-purple"><span className="icon-dollar"></span> Transaction History</h3>
                  <button 
                    className="refresh-btn neon-button neon-cyan"
                    onClick={loadTransactions}
                    disabled={loading}
                  >
                    {loading ? <span className="icon-clock"></span> : <span className="icon-refresh"></span>} Refresh
                  </button>
                </div>
                
                {loading ? (
                  <div className="loading-state neon-text neon-cyan">
                    <span className="icon-clock"></span> Loading transactions...
                  </div>
                ) : transactions.length === 0 ? (
                  <div className="empty-state neon-text neon-dim">
                    <span className="icon-file"></span> No transactions yet. Start playing to see your history!
                  </div>
                ) : (
                  <div className="transactions-list">
                    {transactions.map((transaction) => (
                      <div key={transaction.id} className="transaction-item">
                        <div className="transaction-icon">
                          <span className={`neon-text ${getTransactionColor(transaction.type)} ${getTransactionIcon(transaction.type)}`}>
                          </span>
                        </div>
                        <div className="transaction-details">
                          <div className="transaction-type neon-text neon-cyan">
                            {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                          </div>
                          <div className="transaction-description neon-text neon-dim">
                            {transaction.description}
                          </div>
                          <div className="transaction-date neon-text neon-dim">
                            {formatDate(transaction.created_at)}
                          </div>
                        </div>
                        <div className={`transaction-amount neon-text ${getTransactionColor(transaction.type)}`}>
                          {transaction.type === 'deposit' || transaction.type === 'cashout' ? '+' : '-'}
                          {transaction.amount.toFixed(6)} SOL
                          {solPrice && (
                            <div className="transaction-usd neon-text neon-dim" style={{ fontSize: '0.75em' }}>
                              {formatUsdAmount(convertSolToUsd(transaction.amount) || 0)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="settings-content">
                <h3 className="neon-text neon-purple"><span className="icon-settings"></span> Account Settings</h3>
                
                <div className="settings-section">
                  <h4 className="neon-text neon-cyan">Profile Settings</h4>
                  <div className="setting-item">
                    <label className="setting-label neon-text neon-green">Username</label>
                    <input
                      type="text"
                      value={formData.username}
                      onChange={(e) => setFormData({...formData, username: e.target.value})}
                      disabled={!editMode}
                      className="setting-input neon-input neon-green"
                      maxLength={20}
                    />
                  </div>
                </div>

                <div className="settings-section">
                  <h4 className="neon-text neon-cyan">Audio Settings</h4>
                  <div className="setting-item">
                    <label className="setting-label neon-text neon-yellow">
                      <input
                        type="checkbox"
                        checked={formData.audioMuted}
                        onChange={(e) => setFormData({...formData, audioMuted: e.target.checked})}
                        disabled={!editMode}
                        className="setting-checkbox"
                      />
                      Mute Audio
                    </label>
                  </div>
                  <div className="setting-item">
                    <label className="setting-label neon-text neon-yellow">Volume</label>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={formData.audioVolume}
                      onChange={(e) => setFormData({...formData, audioVolume: parseFloat(e.target.value)})}
                      disabled={!editMode}
                      className="setting-slider"
                    />
                    <span className="volume-display neon-text neon-yellow">
                      {Math.round(formData.audioVolume * 100)}%
                    </span>
                  </div>
                </div>

                <div className="settings-actions">
                  {editMode ? (
                    <>
                      <button 
                        className="save-btn neon-button neon-green"
                        onClick={handleSaveProfile}
                      >
                        <span className="icon-save"></span> Save Changes
                      </button>
                      <button 
                        className="cancel-btn neon-button neon-orange"
                        onClick={() => setEditMode(false)}
                      >
                        <span className="icon-close"></span> Cancel
                      </button>
                    </>
                  ) : (
                    <button 
                      className="edit-btn neon-button neon-cyan"
                      onClick={() => setEditMode(true)}
                    >
                      <span className="icon-edit"></span> Edit Profile
                    </button>
                  )}
                </div>

                <div className="danger-zone">
                  <h4 className="neon-text neon-red">Danger Zone</h4>
                  <button 
                    className="signout-btn neon-button neon-red"
                    onClick={handleSignOut}
                  >
                    <span className="icon-logout"></span> Sign Out
                  </button>
                </div>
              </div>
            )}

            {activeTab === 'achievements' && (
              <div className="achievements-content">
                <h3 className="neon-text neon-purple"><span className="icon-trophy"></span> Achievements</h3>
                <div className="achievements-grid">
                  <div className="achievement-item locked">
                    <div className="achievement-icon"><span className="icon-medal"></span></div>
                    <div className="achievement-name neon-text neon-yellow">First Victory</div>
                    <div className="achievement-description neon-text neon-dim">Win your first game</div>
                    <div className="achievement-progress">
                      {userProfile.total_wins > 0 ? <><span className="icon-check"></span> Unlocked</> : <><span className="icon-lock"></span> Locked</>}
                    </div>
                  </div>
                  <div className="achievement-item locked">
                    <div className="achievement-icon"><span className="icon-dollar"></span></div>
                    <div className="achievement-name neon-text neon-green">Big Winner</div>
                    <div className="achievement-description neon-text neon-dim">Earn 100+ SOL in total</div>
                    <div className="achievement-progress">
                      {userProfile.total_earnings >= 100 ? <><span className="icon-check"></span> Unlocked</> : <><span className="icon-lock"></span> Locked</>}
                    </div>
                  </div>
                  <div className="achievement-item locked">
                    <div className="achievement-icon"><span className="icon-gamepad"></span></div>
                    <div className="achievement-name neon-text neon-cyan">Veteran Player</div>
                    <div className="achievement-description neon-text neon-dim">Play 50+ games</div>
                    <div className="achievement-progress">
                      {userProfile.total_games_played >= 50 ? <><span className="icon-check"></span> Unlocked</> : <><span className="icon-lock"></span> Locked</>}
                    </div>
                  </div>
                  <div className="achievement-item locked">
                    <div className="achievement-icon"><span className="icon-fire"></span></div>
                    <div className="achievement-name neon-text neon-orange">Hot Streak</div>
                    <div className="achievement-description neon-text neon-dim">Win 5 games in a row</div>
                    <div className="achievement-progress"><span className="icon-lock"></span> Coming Soon</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserProfile
