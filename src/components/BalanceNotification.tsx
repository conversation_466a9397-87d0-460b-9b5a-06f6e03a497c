import React, { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import BalanceMonitorService, { BalanceChangeEvent } from '../services/BalanceMonitorService'
import { useSolanaPrice } from '../hooks/useSolanaPrice'

interface BalanceNotificationProps {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left'
  duration?: number // milliseconds
  showOnlyPositive?: boolean
  minAmountToShow?: number // minimum SOL amount to trigger notification
}

interface Notification {
  id: string
  event: BalanceChangeEvent
  timestamp: Date
}

const BalanceNotification: React.FC<BalanceNotificationProps> = ({
  position = 'top-right',
  duration = 5000,
  showOnlyPositive = false,
  minAmountToShow = 0.000001
}) => {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<Notification[]>([])
  
  const {
    solPrice,
    convertSolToUsd,
    formatUsdAmount
  } = useSolanaPrice({ autoRefresh: true })

  useEffect(() => {
    if (!user) return

    const balanceMonitor = BalanceMonitorService.getInstance()
    
    const unsubscribe = balanceMonitor.onBalanceChange((event: BalanceChangeEvent) => {
      if (event.userId !== user.id) return
      
      // Filter based on settings
      if (showOnlyPositive && event.difference <= 0) return
      if (Math.abs(event.difference) < minAmountToShow) return
      
      // Create notification
      const notification: Notification = {
        id: `${Date.now()}-${Math.random()}`,
        event,
        timestamp: new Date()
      }
      
      setNotifications(prev => [...prev, notification])
      
      // Auto-remove after duration
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id))
      }, duration)
    })

    return unsubscribe
  }, [user, duration, showOnlyPositive, minAmountToShow])

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const getPositionStyles = () => {
    switch (position) {
      case 'top-left':
        return { top: '20px', left: '20px' }
      case 'bottom-right':
        return { bottom: '20px', right: '20px' }
      case 'bottom-left':
        return { bottom: '20px', left: '20px' }
      default:
        return { top: '20px', right: '20px' }
    }
  }

  const formatAmount = (amount: number) => {
    const sign = amount > 0 ? '+' : ''
    return `${sign}${amount.toFixed(6)} SOL`
  }

  const getNotificationIcon = (difference: number) => {
    if (difference > 0) return <span className="icon-dollar"></span>
  if (difference < 0) return <span className="icon-money"></span>
  return <span className="icon-refresh"></span>
  }

  const getNotificationColor = (difference: number) => {
    if (difference > 0) return 'success'
    if (difference < 0) return 'warning'
    return 'info'
  }

  if (notifications.length === 0) return null

  const containerStyle = {
    position: 'fixed' as const,
    zIndex: 1000,
    maxWidth: '320px',
    pointerEvents: 'none' as const,
    ...getPositionStyles()
  }

  const notificationStyle = {
    marginBottom: '12px',
    padding: '12px',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    pointerEvents: 'auto' as const,
    fontFamily: 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif'
  }

  const getNotificationTypeStyle = (colorClass: string) => {
    switch (colorClass) {
      case 'success':
        return {
          background: 'linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(56, 142, 60, 0.9))',
          color: 'white'
        }
      case 'warning':
        return {
          background: 'linear-gradient(135deg, rgba(255, 152, 0, 0.9), rgba(245, 124, 0, 0.9))',
          color: 'white'
        }
      case 'info':
        return {
          background: 'linear-gradient(135deg, rgba(33, 150, 243, 0.9), rgba(25, 118, 210, 0.9))',
          color: 'white'
        }
      default:
        return {}
    }
  }

  const contentStyle = {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '8px'
  }

  const headerStyle = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px'
  }

  const iconStyle = {
    fontSize: '1.2em'
  }

  const titleStyle = {
    fontWeight: 600,
    flex: 1
  }

  const closeStyle = {
    background: 'none',
    border: 'none',
    color: 'inherit',
    fontSize: '1.5em',
    cursor: 'pointer',
    padding: 0,
    width: '24px',
    height: '24px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    transition: 'background-color 0.2s ease'
  }

  const bodyStyle = {
    paddingLeft: '28px'
  }

  const amountStyle = {
    fontSize: '1.1em',
    fontWeight: 'bold',
    fontFamily: 'Courier New, monospace'
  }

  const balanceStyle = {
    fontSize: '0.9em',
    opacity: 0.9,
    marginTop: '4px'
  }

  return (
    <div style={containerStyle}>
      {notifications.map((notification) => {
        const { event } = notification
        const colorClass = getNotificationColor(event.difference)
        
        return (
          <div
            key={notification.id}
            style={{...notificationStyle, ...getNotificationTypeStyle(colorClass)}}
          >
            <div style={contentStyle}>
              <div style={headerStyle}>
                <span style={iconStyle}>
                  {getNotificationIcon(event.difference)}
                </span>
                <span style={titleStyle}>
                  Balance {event.difference > 0 ? 'Increased' : 'Decreased'}
                </span>
                <button
                  onClick={() => removeNotification(notification.id)}
                  style={closeStyle}
                  title="Dismiss"
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }}
                >
                  ×
                </button>
              </div>
              <div style={bodyStyle}>
                <div style={amountStyle}>
                  {formatAmount(event.difference)}
                  {solPrice && (
                    <div style={{ fontSize: '0.8em', color: '#ccc', marginTop: '2px' }}>
                      {event.difference > 0 ? '+' : ''}{formatUsdAmount(convertSolToUsd(Math.abs(event.difference)) || 0)}
                    </div>
                  )}
                </div>
                <div style={balanceStyle}>
                  New balance: {event.newBalance.toFixed(6)} SOL
                  {solPrice && (
                    <div style={{ fontSize: '0.8em', color: '#ccc', marginTop: '2px' }}>
                      {formatUsdAmount(convertSolToUsd(event.newBalance) || 0)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )
      })}
      

    </div>
  )
}

export default BalanceNotification