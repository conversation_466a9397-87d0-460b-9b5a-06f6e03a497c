/* SnakePit - Dark Retro Gaming Theme */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Share+Tech+Mono&display=swap');

:root {
  /* Neon Color Palette */
  --neon-green: #00ff41;
  --neon-cyan: #00ffff;
  --neon-pink: #ff0080;
  --neon-magenta: #ff0000;
  --neon-purple: #8000ff;
  --neon-orange: #ff8000;
  --neon-yellow: #ffff00;
  --neon-red: #ff0040;

  /* Dark Theme Colors - Enhanced for readability */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --bg-quaternary: #3a3a3a;
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-tertiary: #cccccc;
  --text-muted: #999999;

  /* Glow Effects - Reduced for better readability */
  --glow-small: 0 0 3px;
  --glow-medium: 0 0 6px;
  --glow-large: 0 0 12px;
  --glow-xl: 0 0 18px;

  /* Subtle glow effects for better readability */
  --glow-subtle: 0 0 2px;
  --glow-text: 0 0 4px;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Share Tech Mono', monospace;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 255, 65, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
}

/* Prevent scrolling only when game is active */
body.game-active {
  overflow: hidden;
}

/* Pixelated/Retro Effects */
.pixelated {
  image-rendering: -moz-crisp-edges;
  image-rendering: -webkit-crisp-edges;
  image-rendering: pixelated;
  image-rendering: crisp-edges;
}

/* Neon Text Effects - Improved readability */
.neon-text {
  text-shadow: var(--glow-text) currentColor;
}

/* Enhanced readability text class */
.readable-neon-text {
  text-shadow: var(--glow-subtle) currentColor;
  font-weight: 500;
}

/* High contrast text for better readability */
.high-contrast-text {
  color: var(--text-primary);
  background: rgba(0, 0, 0, 0.7);
  padding: 2px 6px;
  border-radius: 3px;
  text-shadow: none;
}

/* Subtle glow for UI elements */
.subtle-glow {
  box-shadow: var(--glow-subtle) currentColor;
}

/* Reduced glow for backgrounds */
.reduced-glow-bg {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Lucide React Icon Vertical Alignment */
svg[data-lucide] {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  margin-right: 6px;
  flex-shrink: 0;
}

/* Ensure parent elements support flex alignment */
.modal-title,
.section-title,
h2,
h3,
p,
label,
button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

/* Override for specific cases where flex might break layout */
.setting-item,
.setting-label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.neon-green { color: var(--neon-green); }
.neon-cyan { color: var(--neon-cyan); }
.neon-pink { color: var(--neon-pink); }
.neon-magenta { color: var(--neon-magenta); }
.neon-purple { color: var(--neon-purple); }
.neon-orange { color: var(--neon-orange); }
.neon-yellow { color: var(--neon-yellow); }

/* Main App Container */
.snakepit-app {
  min-height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* ASCII Art Logo */
.ascii-logo {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  font-weight: bold;
  text-align: left;
  margin: 0;
  line-height: 1.0;
  color: var(--neon-green);
  text-shadow: var(--glow-text) var(--neon-green);
  animation: neon-pulse 3s ease-in-out infinite alternate; /* Slower for better performance */
  white-space: pre;
  overflow: visible;
  padding: 1rem 1rem 1rem 5rem;
  background: #000000;
  border-radius: 10px;
  border: 2px solid var(--neon-green);
  box-shadow:
    var(--glow-medium) var(--neon-green),
    inset 0 0 8px rgba(0, 255, 65, 0.1);
}

/* SnakePit Logo/Title - Keep for backwards compatibility */
.snakepit-title {
  font-family: 'Orbitron', monospace;
  font-size: 4rem;
  font-weight: 900;
  text-align: center;
  margin-bottom: 2rem;
  background: linear-gradient(45deg, var(--neon-green), var(--neon-cyan), var(--neon-pink));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: neon-pulse 3s ease-in-out infinite alternate,
             gradient-shift 5s ease-in-out infinite; /* Slower animations for better performance */
  text-shadow: var(--glow-medium) var(--neon-green);
}

@keyframes neon-pulse {
  from {
    text-shadow: var(--glow-medium) var(--neon-green);
  }
  to {
    text-shadow: var(--glow-text) var(--neon-green);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Crown icon animations */
@keyframes crown-pulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 4px rgba(255, 215, 0, 0.6));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.9));
  }
}

.crown-icon {
  display: inline-block;
}

.crown-animated {
  animation: crown-pulse 2s ease-in-out infinite;
}

/* Elimination Banner Styles */
.elimination-banner-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  pointer-events: none;
}

.elimination-banner {
  position: absolute;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  border: 2px solid var(--neon-red);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 10px;
  min-width: 300px;
  box-shadow:
    var(--glow-medium) rgba(255, 0, 64, 0.4),
    inset 0 0 8px rgba(255, 0, 64, 0.1);
  animation: elimination-slide-in 0.5s ease-out;
  backdrop-filter: blur(5px);
}

.elimination-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.elimination-icon {
  flex-shrink: 0;
}

.elimination-icon.headshot {
  color: var(--neon-red);
  filter: drop-shadow(0 0 4px var(--neon-red));
}

.elimination-icon.bodyshot {
  color: var(--neon-yellow);
  filter: drop-shadow(0 0 4px var(--neon-yellow));
}

.elimination-icon.collision {
  color: var(--neon-purple);
  filter: drop-shadow(0 0 4px var(--neon-purple));
}

.elimination-icon.segments {
  color: var(--neon-orange);
  filter: drop-shadow(0 0 4px var(--neon-orange));
}

.elimination-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
}

.elimination-killer {
  font-weight: bold;
  font-size: 0.8rem;
}

.elimination-victim {
  font-weight: bold;
  font-size: 1rem;
}

.elimination-method {
  font-size: 0.8rem;
  opacity: 0.9;
}

.elimination-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0 0 6px 6px;
  overflow: hidden;
}

.elimination-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--neon-red), var(--neon-yellow));
  width: 100%;
  animation: elimination-progress 4s linear;
}

@keyframes elimination-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes elimination-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Game Mode Selection */
.game-mode-select {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
}

.mode-selection-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--neon-cyan);
  text-shadow: var(--glow-text) var(--neon-cyan);
  margin-bottom: 1rem;
}

.mode-buttons {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.mode-button {
  background: var(--bg-secondary);
  border: 2px solid var(--neon-green);
  border-radius: 10px;
  padding: 2rem;
  min-width: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mode-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.2), transparent);
  transition: left 0.5s;
}

.mode-button:hover::before {
  left: 100%;
}

.mode-button:hover {
  border-color: var(--neon-cyan);
  box-shadow:
    var(--glow-medium) var(--neon-cyan),
    inset var(--glow-small) var(--neon-cyan);
  transform: translateY(-5px);
}

.mode-button h3 {
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  color: var(--neon-green);
  margin: 0 0 1rem 0;
  text-shadow: var(--glow-subtle) var(--neon-green);
}

.mode-button:hover h3 {
  color: var(--neon-cyan);
  text-shadow: var(--glow-subtle) var(--neon-cyan);
}

.mode-button p {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* Game Container */
.game-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  position: relative;
}

/* Status Bar - Under Minimap */
.status-bar {
  position: absolute;
  top: 240px; /* Position under minimap (200px height + 20px top + 20px gap) */
  right: 20px;
  width: 250px; /* Same width as minimap */
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  background: rgba(10, 10, 15, 0.9);
  border: 1px solid var(--neon-cyan);
  border-radius: 8px;
  padding: 0.5rem;
  backdrop-filter: blur(5px);
  box-shadow: var(--glow-small) var(--neon-cyan);
  z-index: 20;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.7rem;
  color: var(--text-secondary);
  flex: 1;
  min-width: 45%;
}

.status-item.connected {
  color: var(--neon-green);
}

.status-item.disconnected {
  color: var(--neon-orange);
}

.status-icon {
  font-size: 0.9rem;
}

.status-text {
  font-weight: 600;
}

/* Cash Tracker - Under Status Bar */
.cash-tracker {
  position: absolute;
  top: 290px; /* Position under status bar */
  right: 20px;
  width: 250px; /* Same width as minimap and status bar */
  background: rgba(10, 10, 15, 0.9);
  border: 2px solid var(--neon-green);
  border-radius: 8px;
  padding: 0.75rem;
  backdrop-filter: blur(5px);
  box-shadow: var(--glow-medium) var(--neon-green);
  z-index: 15;
}

.cash-tracker-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid rgba(0, 255, 0, 0.3);
}

.cash-tracker-title {
  font-family: 'Orbitron', monospace;
  font-size: 0.8rem;
  font-weight: 700;
  color: var(--neon-green);
  text-shadow: var(--glow-subtle) var(--neon-green);
}

.cash-stats {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  margin-bottom: 0.75rem;
}

.cash-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.2rem 0;
}

.cash-stat-item.king-status {
  background: rgba(255, 215, 0, 0.1);
  border-radius: 4px;
  padding: 0.3rem;
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.cash-stat-label {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.7rem;
  color: var(--text-secondary);
  font-weight: 600;
}

.cash-stat-value {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.7rem;
  font-weight: 700;
}

/* Cashout Button in Cash Tracker */
.cash-tracker .cashout-container {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.cash-tracker .cashout-button {
  width: 100%;
  padding: 0.5rem;
  font-family: 'Orbitron', monospace;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  border: 2px solid;
  border-radius: 6px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.cash-tracker .cashout-button.neon-green {
  color: var(--neon-green);
  border-color: var(--neon-green);
  box-shadow: var(--glow-small) var(--neon-green);
}

.cash-tracker .cashout-button.neon-green:hover {
  background: rgba(0, 255, 0, 0.1);
  box-shadow: var(--glow-medium) var(--neon-green);
  transform: scale(1.02);
}

.cash-tracker .cashout-button.neon-disabled {
  color: var(--text-secondary);
  border-color: var(--text-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

.cash-tracker .cashout-hint {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.6rem;
  text-align: center;
  color: var(--text-secondary);
}

/* Game UI - Vertical layout under minimap */
.game-ui {
  position: absolute;
  top: 240px; /* Position under minimap (200px height + 20px top + 20px gap) */
  right: 20px;
  width: 250px; /* Same width as minimap */
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: rgba(10, 10, 15, 0.9);
  border: 2px solid var(--neon-green);
  border-radius: 10px;
  box-shadow: var(--glow-small) var(--neon-green);
  z-index: 10;
}

.ui-stats {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-item {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--neon-green);
  border-radius: 5px;
  box-shadow: inset var(--glow-small) var(--neon-green);
  text-align: center;
  width: 100%;
}

.stat-score { color: var(--neon-yellow); }
.stat-cash { color: var(--neon-green); }
.stat-length { color: var(--neon-cyan); }
.stat-boost { color: var(--neon-orange); }
.stat-weapon { color: var(--neon-pink); }
.stat-cooldown { color: var(--neon-purple); }

/* Game Canvas */
.game-canvas {
  flex: 1;
  position: relative;
  width: 100%;
  height: 100%;
}

.main-canvas {
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  border: 2px solid var(--neon-green);
  box-shadow: inset var(--glow-small) var(--neon-green);
}

/* Minimap */
.minimap-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 250px;
  height: 200px;
  background: var(--bg-secondary);
  border: 2px solid var(--neon-cyan);
  border-radius: 8px;
  box-shadow: var(--glow-medium) var(--neon-cyan);
  backdrop-filter: blur(5px);
  z-index: 15;
}

.minimap-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-green), var(--neon-cyan));
  border-radius: 10px;
  z-index: -1;
  opacity: 0.3;
  animation: minimap-glow 2s ease-in-out infinite alternate;
}

@keyframes minimap-glow {
  0% { opacity: 0.3; }
  100% { opacity: 0.6; }
}

.minimap-canvas {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  background: rgba(10, 10, 10, 0.9);
}

/* Weapon Inventory - Bottom Left */
.weapon-inventory {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: rgba(10, 10, 15, 0.9);
  border: 2px solid var(--neon-purple);
  border-radius: 8px;
  backdrop-filter: blur(5px);
  box-shadow: var(--glow-medium) var(--neon-purple);
  z-index: 15;
  transition: all 0.3s ease;
  overflow: hidden;
}

/* Collapsed State - No header, just weapon squares */
.weapon-inventory.collapsed {
  padding: 0.5rem;
  width: 200px;
  height: 90px;
}

/* Expanded State */
.weapon-inventory.expanded {
  padding: 0.75rem;
  min-width: 280px;
  max-width: 320px;
}

.inventory-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-bottom: 0.3rem;
  border-bottom: 1px solid rgba(128, 0, 255, 0.3);
}

.inventory-title {
  font-family: 'Orbitron', monospace;
  font-size: 0.8rem;
  font-weight: 700;
  color: var(--neon-purple);
  text-shadow: var(--glow-small) var(--neon-purple);
  flex: 1;
}

.inventory-toggle {
  background: transparent;
  border: 1px solid var(--neon-purple);
  color: var(--neon-purple);
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-family: 'Orbitron', monospace;
  font-size: 0.8rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.inventory-toggle:hover {
  background: rgba(128, 0, 255, 0.2);
  box-shadow: var(--glow-small) var(--neon-purple);
  transform: scale(1.1);
}

/* Compact Weapon Slots - Uniform Square Layout */
.inventory-slots-compact {
  display: flex;
  gap: 0.3rem;
  justify-content: space-between;
  width: 100%;
}

.inventory-slot-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.3rem;
  background: rgba(0, 0, 0, 0.4);
  border: 2px solid rgba(128, 0, 255, 0.3);
  border-radius: 6px;
  transition: all 0.2s ease;
  width: 55px;
  height: 70px;
  position: relative;
}

.inventory-slot-compact.active {
  background: rgba(128, 0, 255, 0.2);
  border-color: var(--neon-pink);
  box-shadow: var(--glow-medium) var(--neon-pink);
  transform: scale(1.05);
}

.inventory-slot-compact .slot-key {
  position: absolute;
  top: 0.15rem;
  left: 0.15rem;
  width: 14px;
  height: 14px;
  font-size: 0.55rem;
  background: var(--neon-purple);
  color: var(--bg-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Orbitron', monospace;
  font-weight: 700;
}

.inventory-slot-compact.active .slot-key {
  background: var(--neon-pink);
  box-shadow: var(--glow-small) var(--neon-pink);
}

.weapon-thumbnail {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.2rem;
  font-size: 1.2rem;
  color: var(--text-primary);
}

.inventory-slot-compact.active .weapon-thumbnail {
  background: rgba(255, 0, 128, 0.2);
  color: var(--neon-pink);
}

.weapon-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.slot-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
}

.slot-weapon-name {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.45rem;
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1;
  margin-bottom: 0.05rem;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.slot-ammo {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.4rem;
  color: var(--text-secondary);
  line-height: 1;
  font-weight: 700;
  text-align: center;
}

.inventory-slot-compact.active .slot-weapon-name {
  color: var(--neon-pink);
}

.inventory-slot-compact.active .slot-ammo {
  color: var(--text-primary);
}

/* Expanded Content */
.inventory-expanded-content {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(128, 0, 255, 0.3);
  animation: expand-in 0.3s ease;
}

@keyframes expand-in {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

/* Full Weapon Slots - Only in Expanded State */
.inventory-slots-horizontal {
  display: flex;
  flex-direction: column;
  gap: 0.4rem;
  margin-bottom: 0.75rem;
}

.inventory-slot-horizontal {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(128, 0, 255, 0.3);
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
  min-height: 40px;
}

.inventory-slot-horizontal.active {
  background: rgba(128, 0, 255, 0.2);
  border-color: var(--neon-pink);
  box-shadow: var(--glow-medium) var(--neon-pink);
}

.slot-key {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: var(--neon-purple);
  color: var(--bg-primary);
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 0.8rem;
  border-radius: 50%;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.inventory-slot-horizontal.active .slot-key {
  background: var(--neon-pink);
  box-shadow: var(--glow-small) var(--neon-pink);
}

.slot-content-horizontal {
  display: flex;
  flex-direction: column;
  flex: 1;
  text-align: left;
}

.slot-name {
  font-family: 'Orbitron', monospace;
  font-size: 0.7rem;
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 0.2rem;
}

.slot-weapon {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.65rem;
  color: var(--text-secondary);
  line-height: 1.2;
}

.inventory-slot-horizontal.active .slot-weapon {
  color: var(--text-primary);
  font-weight: 600;
}

/* Cooldown Progress Bar */
.cooldown-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.cooldown-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--neon-orange), var(--neon-yellow));
  transition: width 0.1s ease-out;
  box-shadow: 0 0 8px var(--neon-orange);
}

/* Ammo Inventory */
.ammo-inventory {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 6px;
}

.ammo-header {
  font-family: 'Orbitron', monospace;
  font-size: 0.7rem;
  color: var(--neon-cyan);
  font-weight: 600;
  margin-bottom: 0.4rem;
  text-align: center;
  text-transform: uppercase;
}

.ammo-types {
  display: flex;
  gap: 0.3rem;
  flex-wrap: wrap;
  justify-content: space-between;
}

.ammo-type {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  padding: 0.2rem 0.3rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(128, 0, 255, 0.1);
  border-radius: 2px;
  transition: all 0.2s ease;
}

.ammo-type:hover {
  border-color: var(--neon-cyan);
  box-shadow: var(--glow-small) var(--neon-cyan);
}

.ammo-icon {
  font-size: 0.8rem;
  flex-shrink: 0;
}

.ammo-name {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.5rem;
  color: var(--text-primary);
  font-weight: 600;
  flex: 1;
  text-align: left;
  line-height: 1;
}

.ammo-count {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.6rem;
  color: var(--text-secondary);
  font-weight: 600;
  min-width: 12px;
  flex-shrink: 0;
}

/* Powerup Inventory */
.powerup-inventory {
  margin-bottom: 0.75rem;
  padding: 0.5rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 0, 128, 0.3);
  border-radius: 6px;
}

.powerup-header {
  font-family: 'Orbitron', monospace;
  font-size: 0.7rem;
  color: var(--neon-pink);
  font-weight: 600;
  margin-bottom: 0.4rem;
  text-align: center;
  text-transform: uppercase;
}

.powerup-types {
  display: flex;
  gap: 0.2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.powerup-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.1rem;
  padding: 0.2rem 0.3rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 0, 128, 0.1);
  border-radius: 2px;
  transition: all 0.2s ease;
  min-width: 40px;
}

.powerup-type:hover {
  border-color: var(--neon-pink);
  box-shadow: var(--glow-small) var(--neon-pink);
}

.powerup-icon {
  font-size: 0.8rem;
}

.powerup-name {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.5rem;
  color: var(--text-secondary);
  font-weight: 600;
  text-align: center;
  line-height: 1;
}

/* Active Powerups */
.active-powerups {
  margin-bottom: 0.3rem;
  padding: 0.3rem;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 255, 65, 0.2);
  border-radius: 3px;
}

.active-powerups-header {
  display: none; /* Hide header to save space */
}

.active-powerup-list {
  display: flex;
  gap: 0.2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.active-powerup {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.1rem;
  padding: 0.2rem;
  background: rgba(0, 255, 65, 0.1);
  border: 1px solid rgba(0, 255, 65, 0.2);
  border-radius: 2px;
  transition: all 0.2s ease;
  min-width: 30px;
}

.active-powerup-icon {
  font-size: 0.7rem;
}

.active-powerup-timer {
  width: 20px;
  height: 3px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 1px;
  overflow: hidden;
}

.timer-bar {
  height: 100%;
  background: var(--neon-green);
  transition: width 0.1s ease-out;
  box-shadow: 0 0 4px var(--neon-green);
}

.inventory-controls {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(128, 0, 255, 0.3);
  text-align: center;
}

.control-hint {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.65rem;
  color: var(--text-secondary);
  font-weight: 600;
}

/* Instructions */
.instructions {
  position: absolute;
  bottom: 0;
  left: 0;
  background: var(--bg-secondary);
  border: 2px solid var(--neon-purple);
  border-radius: 0 8px 0 0;
  border-bottom: none;
  border-left: none;
  padding: 0.75rem 1rem 1rem 1rem;
  max-width: 280px;
  box-shadow: var(--glow-small) var(--neon-purple);
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  z-index: 15;
}

.instructions.collapsed {
  padding: 0.5rem 0.75rem 0.75rem 0.75rem;
  max-width: 200px;
}

.instructions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  user-select: none;
  margin-bottom: 0.5rem;
  transition: margin-bottom 0.3s ease-in-out;
}

.instructions.collapsed .instructions-header {
  margin-bottom: 0;
}

.instructions-header:hover {
  opacity: 0.8;
}

.instructions h3 {
  font-family: 'Orbitron', monospace;
  color: var(--neon-purple);
  margin: 0;
  text-shadow: var(--glow-small) var(--neon-purple);
}

.collapse-button {
  background: none;
  border: none;
  color: var(--neon-purple);
  font-size: 1rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 3px;
  transition: all 0.2s ease;
  text-shadow: var(--glow-small) var(--neon-purple);
}

.collapse-button:hover {
  background: rgba(128, 0, 255, 0.2);
  transform: scale(1.1);
}

.instructions-content {
  max-height: 500px;
  opacity: 1;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.instructions.collapsed .instructions-content {
  max-height: 0;
  opacity: 0;
  margin-top: 0;
}

.instructions p {
  margin: 0.25rem 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  transition: opacity 0.3s ease-in-out;
}

.controller-section {
  margin-top: 0.75rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(128, 0, 255, 0.3);
}

.controller-section p {
  font-size: 0.8rem;
}

/* Game Over Screen */
.game-over {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.game-over-content {
  background: var(--bg-secondary);
  border: 3px solid var(--neon-pink);
  border-radius: 15px;
  padding: 3rem;
  text-align: center;
  box-shadow: var(--glow-large) var(--neon-pink);
  animation: game-over-pulse 1s ease-in-out infinite alternate;
}

@keyframes game-over-pulse {
  from {
    box-shadow: var(--glow-medium) var(--neon-pink);
  }
  to {
    box-shadow: var(--glow-large) var(--neon-pink);
  }
}

.game-over h2 {
  font-family: 'Orbitron', monospace;
  font-size: 3rem;
  color: var(--neon-pink);
  margin: 0 0 1rem 0;
  text-shadow: var(--glow-text) var(--neon-pink);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

/* Cute skull with pink glowing outline */
.cute-skull {
  font-size: 3.5rem;
  display: inline-block;
  filter:
    drop-shadow(0 0 8px var(--neon-pink))
    drop-shadow(0 0 16px var(--neon-pink))
    drop-shadow(0 0 24px var(--neon-pink));
  animation: skull-pulse 2s ease-in-out infinite alternate;
  transition: all 0.3s ease;
}

.cute-skull:hover {
  transform: scale(1.1) rotate(5deg);
  filter:
    drop-shadow(0 0 12px var(--neon-pink))
    drop-shadow(0 0 24px var(--neon-pink))
    drop-shadow(0 0 36px var(--neon-pink));
}

@keyframes skull-pulse {
  from {
    filter:
      drop-shadow(0 0 8px var(--neon-pink))
      drop-shadow(0 0 16px var(--neon-pink))
      drop-shadow(0 0 24px var(--neon-pink));
  }
  to {
    filter:
      drop-shadow(0 0 12px var(--neon-pink))
      drop-shadow(0 0 24px var(--neon-pink))
      drop-shadow(0 0 36px var(--neon-pink));
  }
}

.final-stats {
  margin: 2rem 0;
}

.final-stat {
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  margin: 0.5rem 0;
  color: var(--neon-cyan);
  text-shadow: var(--glow-subtle) var(--neon-cyan);
  background: rgba(0, 0, 0, 0.6);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.game-over-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.retro-button {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 1.1rem;
  padding: 1rem 2rem;
  background: var(--bg-tertiary);
  border: 2px solid var(--neon-green);
  border-radius: 5px;
  color: var(--neon-green);
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.retro-button:hover {
  background: var(--neon-green);
  color: var(--bg-primary);
  box-shadow: var(--glow-medium) var(--neon-green);
  transform: translateY(-2px);
}

.retro-button.secondary {
  border-color: var(--neon-orange);
  color: var(--neon-orange);
}

.retro-button.secondary:hover {
  background: var(--neon-orange);
  box-shadow: var(--glow-medium) var(--neon-orange);
}

/* Enhanced Home Page Styles */

/* Old edge navigation styles removed - now using tab system */

/* Old nav-link and user-status styles removed - now using tab system and top user status */

/* Audio Controls */
.audio-controls {
  position: fixed;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  pointer-events: auto;
  background: rgba(10, 10, 15, 0.9);
  border: 1px solid var(--neon-purple);
  border-radius: 8px;
  padding: 0.5rem;
  backdrop-filter: blur(5px);
  box-shadow: var(--glow-small) var(--neon-purple);
  z-index: 1001;
}

.audio-btn {
  background: transparent;
  border: 1px solid var(--neon-purple);
  color: var(--neon-purple);
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.audio-btn:hover {
  background: rgba(128, 0, 255, 0.2);
  box-shadow: var(--glow-small) var(--neon-purple);
  transform: scale(1.05);
}

.volume-control {
  display: flex;
  align-items: center;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(128, 0, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--neon-purple);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--glow-small) var(--neon-purple);
  transition: all 0.2s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: var(--glow-medium) var(--neon-purple);
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--neon-purple);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: var(--glow-small) var(--neon-purple);
  transition: all 0.2s ease;
}

.volume-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: var(--glow-medium) var(--neon-purple);
}

/* Legal Overlay */
.legal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.legal-overlay-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(10px);
}

.legal-content {
  position: relative;
  background: rgba(10, 10, 15, 0.95);
  border: 2px solid var(--neon-green);
  border-radius: 15px;
  padding: 2rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow:
    var(--glow-large) var(--neon-green),
    inset var(--glow-small) rgba(0, 255, 65, 0.1);
  animation: legal-appear 0.5s ease-out;
}

@keyframes legal-appear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.legal-header {
  text-align: center;
  margin-bottom: 2rem;
}

.legal-title {
  font-family: 'Orbitron', monospace;
  font-size: 2rem;
  font-weight: 900;
  margin: 0 0 0.5rem 0;
}

.legal-subtitle {
  font-family: 'Share Tech Mono', monospace;
  font-size: 1rem;
  margin: 0;
  opacity: 0.8;
}

.legal-form {
  margin-bottom: 2rem;
}

.consent-item {
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.consent-item.check-all-item {
  background: rgba(255, 255, 0, 0.1);
  border: 1px solid var(--neon-yellow);
  margin-bottom: 1.5rem;
}

.consent-item.required {
  background: rgba(255, 0, 128, 0.05);
  border: 1px solid rgba(255, 0, 128, 0.2);
}

.consent-item.optional {
  background: rgba(0, 255, 65, 0.05);
  border: 1px solid rgba(0, 255, 65, 0.2);
}

.consent-item:hover {
  background-opacity: 0.15;
  transform: translateX(5px);
}

.consent-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
  line-height: 1.4;
}

.consent-checkbox {
  display: none;
}

.checkbox-custom {
  width: 20px;
  height: 20px;
  border: 2px solid var(--neon-cyan);
  border-radius: 4px;
  background: transparent;
  position: relative;
  flex-shrink: 0;
  transition: all 0.3s ease;
  margin-top: 2px;
}

.consent-checkbox:checked + .checkbox-custom {
  background: var(--neon-cyan);
  box-shadow: var(--glow-small) var(--neon-cyan);
}

.consent-checkbox:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--bg-primary);
  font-weight: bold;
  font-size: 14px;
}

.check-all-item .checkbox-custom {
  border-color: var(--neon-yellow);
}

.check-all-item .consent-checkbox:checked + .checkbox-custom {
  background: var(--neon-yellow);
  box-shadow: var(--glow-small) var(--neon-yellow);
}

.consent-text {
  color: var(--text-primary);
  flex: 1;
}

.required-indicator {
  font-weight: bold;
}

.optional-indicator {
  font-size: 0.8rem;
  opacity: 0.8;
}

.legal-link {
  text-decoration: underline;
  transition: all 0.3s ease;
}

.legal-link:hover {
  text-shadow: var(--glow-medium) currentColor;
}

.consent-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
  margin: 1rem 0;
  opacity: 0.5;
}

.legal-footer {
  text-align: center;
}

.required-note {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.8rem;
  margin-bottom: 1.5rem;
}

.enter-button {
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  font-weight: 700;
  padding: 1rem 2rem;
  background: transparent;
  border: 2px solid currentColor;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin: 0 auto;
  min-width: 200px;
}

.enter-button:not(.disabled):hover {
  background: currentColor;
  color: var(--bg-primary);
  box-shadow: var(--glow-medium) currentColor;
  transform: translateY(-2px);
}

.enter-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: var(--text-secondary);
  border-color: var(--text-secondary);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
  margin-top: 1rem;
  padding: 0.5rem;
  background: rgba(255, 128, 0, 0.1);
  border: 1px solid var(--neon-orange);
  border-radius: 4px;
}

/* Responsive Design for Legal Overlay */
@media (max-width: 768px) {
  .legal-content {
    padding: 1.5rem;
    margin: 1rem;
  }

  .legal-title {
    font-size: 1.5rem;
  }

  .legal-subtitle {
    font-size: 0.9rem;
  }

  .consent-label {
    font-size: 0.8rem;
  }

  .enter-button {
    font-size: 1rem;
    padding: 0.8rem 1.5rem;
  }
}

/* Play Now Screen - Improved Layout */

/* Hero Section */
.hero-section {
  text-align: center;
  margin-bottom: 4rem;
}

.hero-content {
  margin-top: 2rem;
}

.hero-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-description {
  font-family: 'Share Tech Mono', monospace;
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.4;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-section {
  margin-bottom: 2rem;
}

.play-now-button {
  font-family: 'Orbitron', monospace;
  font-size: 1.4rem;
  font-weight: 700;
  padding: 1.5rem 3rem;
  background: var(--bg-tertiary);
  border: 3px solid var(--neon-green);
  border-radius: 12px;
  color: var(--neon-green);
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 2px;
  position: relative;
  overflow: hidden;
  margin-bottom: 1rem;
  animation: button-text-pulse 2s ease-in-out infinite;
}

.play-now-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 65, 0.3), transparent);
  transition: left 0.5s;
}

.play-now-button:hover::before {
  left: 100%;
}

.play-now-button:hover {
  background: var(--neon-green);
  color: var(--bg-primary);
  box-shadow: var(--glow-large) var(--neon-green);
  transform: translateY(-3px) scale(1.05);
}

.cta-subtitle {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.7;
}

/* Game Modes Preview */
.modes-preview {
  margin-bottom: 4rem;
  text-align: center;
}

.section-title {
  font-family: 'Orbitron', monospace;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
}

.modes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.mode-preview {
  background: rgba(10, 10, 15, 0.7);
  border: 2px solid var(--neon-green);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.mode-preview:hover {
  transform: translateY(-5px);
  box-shadow: var(--glow-medium) var(--neon-green);
}

.mode-preview.combat-preview {
  border-color: var(--neon-pink);
}

.mode-preview.combat-preview:hover {
  box-shadow: var(--glow-medium) var(--neon-pink);
}

.mode-preview-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.mode-preview h3 {
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.mode-preview p {
  font-family: 'Share Tech Mono', monospace;
  font-size: 1rem;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.4;
}

.mode-preview-features {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left;
}

.mode-preview-features li {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
  color: var(--neon-green);
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.combat-preview .mode-preview-features li {
  color: var(--neon-pink);
}

/* Features Section */
.features-section {
  margin-bottom: 3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  background: rgba(10, 10, 15, 0.6);
  border: 1px solid var(--neon-cyan);
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(3px);
}

.feature-card:hover {
  border-color: var(--neon-yellow);
  box-shadow: var(--glow-small) var(--neon-yellow);
  transform: translateY(-3px);
}

.feature-card .feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-card h4 {
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--neon-cyan);
  margin: 0 0 1rem 0;
}

.feature-card:hover h4 {
  color: var(--neon-yellow);
}

.feature-card p {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* User Setup Screen - Cleaner Layout */
.user-setup-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

/* Setup Header */
.setup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 3rem;
  padding: 0 1rem;
}

.back-button {
  font-family: 'Orbitron', monospace;
  font-size: 1rem;
  padding: 0.75rem 1.5rem;
  background: var(--bg-tertiary);
  border: 2px solid var(--neon-orange);
  border-radius: 8px;
  color: var(--neon-orange);
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: var(--neon-orange);
  color: var(--bg-primary);
  box-shadow: var(--glow-medium) var(--neon-orange);
  transform: translateY(-2px);
}

.setup-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 900;
  margin: 0;
  text-align: center;
  flex: 1;
}

/* Balance Widget - Integrated into header */
.balance-widget {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  background: rgba(10, 10, 15, 0.6);
  border: 2px solid var(--neon-yellow);
  border-radius: 10px;
  padding: 1rem;
  backdrop-filter: blur(3px);
  box-shadow: var(--glow-small) var(--neon-yellow);
}

.balance-amount {
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0;
}

.balance-actions {
  display: flex;
  gap: 0.5rem;
}

.balance-btn {
  font-family: 'Orbitron', monospace;
  font-size: 1rem;
  font-weight: 700;
  padding: 0.5rem 0.75rem;
  background: var(--bg-tertiary);
  border: 2px solid currentColor;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 40px;
}

.balance-btn:hover:not(:disabled) {
  background: currentColor;
  color: var(--bg-primary);
  box-shadow: var(--glow-small) currentColor;
  transform: scale(1.1);
}

.balance-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Main Setup Card */
.setup-card {
  background: rgba(10, 10, 15, 0.7);
  border: 2px solid var(--neon-cyan);
  border-radius: 15px;
  padding: 2rem;
  width: 100%;
  max-width: 800px;
  box-shadow: var(--glow-medium) var(--neon-cyan);
  backdrop-filter: blur(5px);
}

/* Setup Steps */
.setup-step {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setup-step:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.step-number {
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  font-weight: 900;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid currentColor;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  box-shadow: var(--glow-small) currentColor;
}

.step-title {
  font-family: 'Orbitron', monospace;
  font-size: 1.3rem;
  font-weight: 700;
  margin: 0;
}

/* Snake Name Input */
.snake-name-input {
  font-family: 'Share Tech Mono', monospace;
  font-size: 1.3rem;
  padding: 1rem 1.5rem;
  background: rgba(42, 42, 42, 0.7);
  border: 2px solid var(--neon-purple);
  border-radius: 10px;
  color: var(--text-primary);
  width: 100%;
  max-width: 400px;
  text-align: center;
  transition: all 0.3s ease;
  margin: 0 auto;
  display: block;
  backdrop-filter: blur(2px);
}

.snake-name-input:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: var(--glow-medium) var(--neon-cyan);
  transform: scale(1.02);
}

.snake-name-input::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Wager Grid - Casino Chip Style */
.wager-grid {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.wager-chip {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(20, 20, 20, 0.95), rgba(10, 10, 10, 0.9));
  border: 3px solid var(--neon-green);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  backdrop-filter: blur(4px);
  color: var(--neon-green);
  box-shadow: 0 4px 15px rgba(0, 255, 127, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.wager-chip::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50px;
  height: 50px;
  background-image: url('../public/assets/SnakePit-Currency-Icon.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.wager-chip:hover:not(.disabled) {
  border-color: var(--neon-yellow);
  color: var(--neon-yellow);
  box-shadow: 0 8px 25px rgba(255, 255, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2), var(--glow-medium) var(--neon-yellow);
  transform: translateY(-5px) scale(1.05);
  background: radial-gradient(circle, rgba(40, 40, 20, 0.95), rgba(20, 20, 10, 0.9));
}

.wager-chip:hover:not(.disabled) .circular-text {
  transform: translate(-50%, -50%) scale(1.15);
}

.wager-chip:hover:not(.disabled) .circular-text svg {
  animation-duration: 15s;
}

.wager-chip:hover:not(.disabled) .usd-text {
  fill: #FFED4E;
  text-shadow: 0 0 12px #FFED4E;
}

.wager-chip:hover:not(.disabled) .sol-text {
  fill: #00FFB3;
  text-shadow: 0 0 10px #00FFB3;
}

.wager-chip:hover:not(.disabled)::before {
  border-color: var(--neon-yellow);
}

.wager-chip.selected {
  background: radial-gradient(circle, rgba(0, 255, 127, 0.3), rgba(0, 200, 100, 0.2));
  color: var(--bg-primary);
  box-shadow: 0 0 30px rgba(0, 255, 127, 0.6), 0 0 60px rgba(0, 255, 127, 0.3), inset 0 2px 4px rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  border-color: rgba(0, 255, 127, 1);
  border-width: 4px;
}

.wager-chip.selected .circular-text {
  transform: translate(-50%, -50%) scale(1.2);
}

.wager-chip.selected .circular-text svg {
  animation-duration: 12s;
}

.wager-chip.selected .usd-text {
  fill: #FFFF00;
  text-shadow: 0 0 15px #FFFF00;
}

.wager-chip.selected .sol-text {
  fill: #00FFCC;
  text-shadow: 0 0 12px #00FFCC;
}

.wager-chip.selected::before {
  border-color: var(--bg-primary);
}

.wager-chip.disabled {
  opacity: 0.3;
  cursor: not-allowed;
  border-color: var(--text-secondary);
  color: var(--text-secondary);
}

.wager-chip .circular-text {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100px;
  height: 100px;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.wager-chip .circular-text svg {
  width: 100%;
  height: 100%;
  animation: rotate 20s linear infinite;
}

.wager-chip .circular-text .usd-text {
  font-family: 'Orbitron', monospace;
  font-size: 10px;
  font-weight: 900;
  fill: #FFD700;
  text-shadow: 0 0 8px #FFD700;
  letter-spacing: 2px;
}

.wager-chip .circular-text .sol-text {
  font-family: 'Share Tech Mono', monospace;
  font-size: 8px;
  font-weight: 700;
  fill: #00FFA3;
  text-shadow: 0 0 6px #00FFA3;
  letter-spacing: 1.5px;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.chip-amount {
  display: none;
}

.chip-label {
  display: none;
}

.wager-summary {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
  text-align: center;
  margin-top: 1rem;
}

/* Mode Selection Card Deck */
.mode-slider-container {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 3rem;
  padding-bottom: 80px;
  padding-left: 2rem;
  padding-right: 2rem;
  justify-content: center;
}

.card-deck-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 500px;
  perspective: 1000px;
}

.card-deck {
  position: relative;
  width: 350px;
  height: 480px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.shuffle-arrow {
  background: rgba(0, 255, 255, 0.1);
  border: 2px solid #00ffff;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00ffff;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.shuffle-arrow:hover:not(:disabled) {
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  transform: scale(1.1);
}

.shuffle-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.shuffle-arrow:disabled:hover {
  transform: none;
  box-shadow: none;
}

.shuffle-arrow:active:not(:disabled) {
  transform: scale(0.95);
  background: rgba(0, 255, 255, 0.3);
}

.mode-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  width: 350px;
  max-width: 350px;
  min-width: 350px;
  height: 480px;
  position: absolute;
  overflow: visible;
  flex-shrink: 0;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  margin: 0 10px;
}

.card-active {
  z-index: 10;
}

.card-behind {
  display: none;
  z-index: 5;
  opacity: 0;
  visibility: hidden;
}

.card-shuffling {
  animation: cardShuffle 0.3s ease-in-out;
}

@keyframes cardShuffle {
  0% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  25% {
    transform: translateY(-30px) rotate(5deg) scale(0.95);
  }
  50% {
    transform: translateY(-50px) rotate(-5deg) scale(0.9);
  }
  75% {
    transform: translateY(-30px) rotate(3deg) scale(0.95);
  }
  100% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
}

.mode-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #000000;
  border-radius: 12px;
  padding: 0;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.5);
}

.new-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: linear-gradient(45deg, #ff6b35, #ff8e53);
  color: white;
  font-family: 'Orbitron', monospace;
  font-size: 0.7rem;
  font-weight: 700;
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(255, 107, 53, 0.4);
  z-index: 15;
  animation: new-badge-glow 2s ease-in-out infinite alternate;
}

@keyframes new-badge-glow {
  from {
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.4);
  }
  to {
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.8);
  }
}

.mode-image {
  width: 320px;
  max-width: 320px;
  min-width: 320px;
  height: 380px;
  object-fit: cover;
  border: 3px solid var(--neon-green);
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 255, 127, 0.3);
  margin: 0 auto;
  display: block;
}

.mode-chip {
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(20, 20, 20, 0.95);
  border: 2px solid var(--neon-green);
  border-radius: 20px;
  padding: 12px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 255, 127, 0.4);
  transition: all 0.3s ease;
  z-index: 10;
  min-height: 55px;
  white-space: nowrap;
  width: 220px;
  min-width: 220px;
  max-width: 220px;
  margin: 0;
}

.chip-title {
  font-family: 'Orbitron', monospace;
  font-size: 0.9rem;
  font-weight: 700;
  color: var(--neon-green);
  text-shadow: 0 0 8px currentColor;
  line-height: 1;
}

/* Selected mode display */
.selected-mode-display {
  text-align: center;
  padding: 12px 20px;
  margin-top: 15px;
  background: rgba(0, 255, 255, 0.1);
  border: 2px solid var(--neon-cyan);
  border-radius: 8px;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  font-size: 1rem;
  text-shadow: 0 0 10px currentColor;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

/* Mode card selected state */
.mode-card.selected {
  border-color: var(--neon-yellow) !important;
  transform: scale(1.02) !important;
}

.mode-card.selected .mode-image {
  border-color: var(--neon-yellow) !important;
}

.mode-card.selected .mode-chip {
  border-color: var(--neon-yellow) !important;
}

.mode-card.selected .chip-title {
  color: var(--neon-yellow) !important;
}

/* Upgrade options */
.upgrade-options {
  margin-top: 15px;
}

.upgrade-category {
  margin-bottom: 20px;
}

.category-title {
  font-size: 1.1em;
  font-weight: bold;
  margin-bottom: 10px;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.upgrade-row {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.upgrade-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 10px;
}

.upgrade-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: rgba(20, 20, 20, 0.8);
  border: 2px solid transparent;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  min-height: 70px;
}

.upgrade-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.upgrade-icon {
  font-size: 2rem;
  min-width: 40px;
  text-align: center;
}

.upgrade-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.upgrade-name {
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  font-size: 0.9rem;
  color: inherit;
}

.upgrade-desc {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
}

/* Battle section */
.battle-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid rgba(255, 255, 255, 0.1);
}

.enter-battle-button {
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  font-weight: 700;
  padding: 18px 40px;
  background: linear-gradient(45deg, #ff0040, #ff4070);
  border: 3px solid var(--neon-red);
  border-radius: 12px;
  color: white;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 0 20px rgba(255, 0, 64, 0.4);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.enter-battle-button:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 30px rgba(255, 0, 64, 0.6);
  text-shadow: 0 0 15px rgba(255, 255, 255, 1);
}

.enter-battle-button:active:not(:disabled) {
  transform: translateY(-1px) scale(1.02);
}

.enter-battle-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 0 10px rgba(255, 0, 64, 0.2);
}

.chip-subtitle {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.7rem;
  color: var(--text-secondary);
  opacity: 0.8;
  line-height: 1;
}

.mode-card:hover:not(:disabled) {
  transform: translateY(-5px);
}

.mode-card:hover:not(:disabled) .mode-image {
  border-color: var(--neon-cyan);
  box-shadow: 0 6px 25px rgba(0, 255, 255, 0.5);
}

.mode-card:hover:not(:disabled) .mode-chip {
  border-color: var(--neon-cyan);
  box-shadow: 0 6px 20px rgba(0, 255, 255, 0.6);
  transform: translateX(-50%) translateY(-5px);
}

.mode-card:hover:not(:disabled) .chip-title {
  color: var(--neon-cyan);
}

.mode-card:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mode-card:disabled .mode-image {
  border-color: var(--text-secondary);
  box-shadow: none;
}

.mode-card:disabled .mode-chip {
  border-color: var(--text-secondary);
  box-shadow: none;
}

.mode-card.classic-mode .mode-image,
.mode-card.combat-mode .mode-image {
  border-color: var(--neon-green);
  box-shadow: 0 4px 15px rgba(0, 255, 127, 0.3);
}

.mode-card.classic-mode .mode-chip,
.mode-card.combat-mode .mode-chip {
  border-color: var(--neon-green);
  box-shadow: 0 4px 15px rgba(0, 255, 127, 0.4);
}

.mode-card.classic-mode .chip-title,
.mode-card.combat-mode .chip-title {
  color: var(--neon-green);
}

.mode-card.classic-mode:hover:not(:disabled) .mode-image,
.mode-card.combat-mode:hover:not(:disabled) .mode-image {
  border-color: var(--neon-cyan);
  box-shadow: 0 6px 25px rgba(0, 255, 255, 0.5);
}

.mode-card.classic-mode:hover:not(:disabled) .mode-chip,
.mode-card.combat-mode:hover:not(:disabled) .mode-chip {
  border-color: var(--neon-cyan);
  box-shadow: 0 6px 20px rgba(0, 255, 255, 0.6);
}

.mode-card.classic-mode:hover:not(:disabled) .chip-title,
.mode-card.combat-mode:hover:not(:disabled) .chip-title {
  color: var(--neon-cyan);
}

.mode-card.combat-mode .mode-image {
  border-color: var(--neon-pink);
  box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
}

.mode-card.combat-mode .mode-chip {
  border-color: var(--neon-pink);
  box-shadow: 0 4px 15px rgba(255, 20, 147, 0.4);
}

.mode-card.combat-mode .chip-title {
  color: var(--neon-pink);
}

.mode-card.combat-mode:hover:not(:disabled) .mode-image {
  border-color: var(--neon-orange);
  box-shadow: 0 6px 25px rgba(255, 165, 0, 0.5);
}

.mode-card.combat-mode:hover:not(:disabled) .mode-chip {
  border-color: var(--neon-orange);
  box-shadow: 0 6px 20px rgba(255, 165, 0, 0.6);
}

.mode-card.combat-mode:hover:not(:disabled) .chip-title {
  color: var(--neon-orange);
}

/* Old mode card styling removed - now using image-based cards */

/* Setup Warning */
.setup-warning {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
  text-align: center;
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(255, 128, 0, 0.1);
  border: 1px solid var(--neon-orange);
  border-radius: 8px;
  animation: warning-pulse 2s ease-in-out infinite alternate;
}

@keyframes warning-pulse {
  from {
    opacity: 0.8;
  }
  to {
    opacity: 1;
    box-shadow: var(--glow-small) var(--neon-orange);
  }
}

/* Neon Button Base Styles */
.neon-button {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  background: var(--bg-tertiary);
  border: 2px solid currentColor;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: var(--glow-subtle) currentColor;
}

.neon-button:hover:not(:disabled) {
  background: currentColor;
  color: var(--bg-primary);
  box-shadow: var(--glow-small) currentColor;
  transform: translateY(-2px);
}

.neon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Neon Input Styles */
.neon-input {
  background: var(--bg-tertiary);
  border: 2px solid currentColor;
  border-radius: 5px;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.neon-input:focus {
  outline: none;
  box-shadow: var(--glow-small) currentColor;
}

/* Neon Dim Text */
.neon-dim {
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .snakepit-title {
    font-size: 2.5rem;
  }

  .mode-selection-title {
    font-size: 2rem;
  }

  .mode-buttons {
    flex-direction: column;
    align-items: center;
  }

  .mode-button {
    min-width: 280px;
  }

  .nav-left,
  .nav-right {
    display: none;
  }

  .nav-bottom {
    flex-direction: column;
    gap: 0.5rem;
    align-items: center;
  }

  /* Play Now Screen Mobile */
  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .play-now-button {
    font-size: 1.2rem;
    padding: 1.2rem 2rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .modes-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .mode-preview {
    padding: 1.5rem;
  }

  .mode-preview-icon {
    font-size: 2.5rem;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .feature-card {
    padding: 1.2rem;
  }

  .feature-card .feature-icon {
    font-size: 2rem;
  }

  /* Mobile Landing Container */
  .landing-container {
    margin: 1rem;
    padding: 2rem 1.5rem;
    border-radius: 15px;
    max-width: calc(100% - 2rem);
  }

  .landing-container::before {
    border-radius: 12px;
    margin: 3px;
  }



  /* Mobile User Setup */
  .user-setup-container {
    padding: 1rem;
  }

  .setup-header {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .setup-title {
    font-size: 2rem;
  }

  .balance-widget {
    order: -1;
  }

  .setup-card {
    padding: 1.5rem;
  }

  .step-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 1.2rem;
  }

  .step-title {
    font-size: 1.1rem;
  }

  .snake-name-input {
    font-size: 1.1rem;
    max-width: 100%;
  }

  .wager-grid {
    gap: 0.75rem;
  }

  .wager-chip {
    width: 70px;
    height: 70px;
  }

  .chip-amount {
    font-size: 0.9rem;
  }

  .mode-slider-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .mode-cards-track {
    flex-direction: column;
    gap: 1rem;
  }
  
  .slider-arrow {
    display: none;
  }

  .mode-card {
    min-width: auto;
    max-width: 100%;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .mode-info {
    text-align: center;
  }

  .mode-features {
    align-items: center;
  }

  .game-ui {
    top: 160px; /* Position under smaller mobile minimap (140px height + 10px top + 10px gap) */
    right: 10px;
    width: 180px; /* Same width as mobile minimap */
  }

  .ui-stats {
    gap: 0.5rem;
  }

  .stat-item {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }

  .minimap-container {
    width: 180px;
    height: 140px;
    top: 10px;
    right: 10px;
  }

  .instructions {
    max-width: 250px;
  }
}

/* Retro Scanlines Effect */
.scanlines::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: linear-gradient(
    transparent 50%,
    rgba(0, 255, 65, 0.03) 50%
  );
  background-size: 100% 4px;
  animation: scanlines 0.1s linear infinite;
}

@keyframes scanlines {
  0% { transform: translateY(0); }
  100% { transform: translateY(4px); }
}

/* Enhanced Landing Page Styles */

/* Game Preview Video Background */
.game-preview-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.3;
  filter: blur(1px) brightness(0.7) contrast(1.2);
}

/* Video Fallback */
.video-fallback {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(0, 255, 65, 0.1) 0%,
    rgba(0, 255, 255, 0.1) 25%,
    rgba(255, 0, 128, 0.1) 50%,
    rgba(128, 0, 255, 0.1) 75%,
    rgba(255, 128, 0, 0.1) 100%
  );
  background-size: 400% 400%;
  animation: gradient-flow 8s ease-in-out infinite;
}

@keyframes gradient-flow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.placeholder-content {
  max-width: 600px;
  padding: 2rem;
}

.placeholder-content h3 {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 900;
  margin-bottom: 1rem;
}

.placeholder-content p {
  font-family: 'Share Tech Mono', monospace;
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.preview-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
}

.preview-features span {
  font-family: 'Share Tech Mono', monospace;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  border: 1px solid currentColor;
  backdrop-filter: blur(5px);
  animation: feature-glow 3s ease-in-out infinite alternate;
}

@keyframes feature-glow {
  from {
    box-shadow: var(--glow-small) currentColor;
  }
  to {
    box-shadow: var(--glow-medium) currentColor;
  }
}

/* Optimized Animated Background Colors */
.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 0, 0.08) 0%,
    rgba(255, 0, 128, 0.08) 33%,
    rgba(128, 0, 255, 0.08) 66%,
    rgba(255, 128, 0, 0.08) 100%
  );
  background-size: 400% 400%;
  animation: gradient-flow 20s ease-in-out infinite; /* Slower animation for better performance */
  will-change: background-position; /* Optimize for animation */
}

@keyframes gradient-flow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes button-text-pulse {
  0% {
    color: var(--neon-green);
    text-shadow: var(--glow-small) var(--neon-green);
  }
  50% {
    color: var(--neon-yellow);
    text-shadow: var(--glow-medium) var(--neon-yellow);
  }
  100% {
    color: var(--neon-green);
    text-shadow: var(--glow-small) var(--neon-green);
  }
}

.falling-money-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.landing-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  gap: 3rem;
}

/* Landing Container with Border and Black Background */
.landing-container {
  background: rgba(0, 0, 0, 0.98);
  border: 3px solid var(--neon-cyan);
  border-radius: 20px;
  padding: 3rem;
  max-width: 900px;
  width: 100%;
  box-shadow: var(--glow-large) var(--neon-cyan);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.landing-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid var(--neon-cyan);
  border-radius: 17px;
  margin: 5px;
  opacity: 0.5;
  pointer-events: none;
}

.snakepit-logo {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

/* Logo Snakes Canvas */
.logo-snakes-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}



.snakepit-title {
  font-family: 'Orbitron', monospace;
  font-size: 5rem;
  font-weight: 900;
  margin: 0;
  background: linear-gradient(45deg, var(--neon-green), var(--neon-cyan), var(--neon-pink));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: neon-pulse 3s ease-in-out infinite alternate,
             gradient-shift 5s ease-in-out infinite; /* Slower animations for better performance */
  text-shadow:
    0 0 10px var(--neon-green),
    0 0 20px var(--neon-green),
    0 0 30px var(--neon-green);
}

.snakepit-subtitle {
  font-family: 'Share Tech Mono', monospace;
  font-size: 1.5rem;
  margin: 1rem 0 0 0;
  opacity: 0.9;
}

.game-stats {
  display: flex;
  gap: 3rem;
  margin: 2rem 0;
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background: rgba(10, 10, 15, 0.8);
  border: 2px solid var(--neon-green);
  border-radius: 10px;
  box-shadow: var(--glow-small) var(--neon-green);
  min-width: 120px;
}

.stat-number {
  font-family: 'Orbitron', monospace;
  font-size: 3rem;
  font-weight: 900;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.mode-selection {
  text-align: center;
  max-width: 1000px;
}

.mode-selection-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  margin-bottom: 2rem;
}

.mode-buttons {
  display: flex;
  gap: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.mode-button {
  background: rgba(10, 10, 15, 0.9);
  border: 2px solid var(--neon-green);
  border-radius: 15px;
  padding: 2rem;
  min-width: 350px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.mode-button:hover {
  border-color: var(--neon-cyan);
  box-shadow:
    var(--glow-medium) var(--neon-cyan),
    inset var(--glow-small) var(--neon-cyan);
  transform: translateY(-5px);
}

.mode-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.mode-button h3 {
  font-family: 'Orbitron', monospace;
  font-size: 1.8rem;
  color: var(--neon-green);
  margin: 0 0 1rem 0;
  text-shadow: var(--glow-small) var(--neon-green);
}

.mode-button:hover h3 {
  color: var(--neon-cyan);
  text-shadow: var(--glow-small) var(--neon-cyan);
}

.mode-button p {
  color: var(--text-secondary);
  margin: 0 0 1.5rem 0;
  line-height: 1.4;
  font-size: 1.1rem;
}

.mode-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  text-align: left;
}

.mode-features span {
  color: var(--neon-green);
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.9rem;
}

.classic-mode:hover .mode-features span {
  color: var(--neon-cyan);
}

.combat-mode {
  border-color: var(--neon-pink);
}

.combat-mode:hover {
  border-color: var(--neon-orange);
}

.combat-mode h3 {
  color: var(--neon-pink);
  text-shadow: var(--glow-small) var(--neon-pink);
}

.combat-mode:hover h3 {
  color: var(--neon-orange);
  text-shadow: var(--glow-small) var(--neon-orange);
}

.combat-mode .mode-features span {
  color: var(--neon-pink);
}

.combat-mode:hover .mode-features span {
  color: var(--neon-orange);
}

.game-features {
  max-width: 1000px;
  margin-top: 3rem;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(10, 10, 15, 0.6);
  border: 1px solid var(--neon-green);
  border-radius: 10px;
  box-shadow: var(--glow-small) var(--neon-green);
  transition: all 0.3s ease;
}

.feature-item:hover {
  border-color: var(--neon-cyan);
  box-shadow: var(--glow-medium) var(--neon-cyan);
  transform: translateY(-3px);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-item h4 {
  font-family: 'Orbitron', monospace;
  color: var(--neon-green);
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.feature-item:hover h4 {
  color: var(--neon-cyan);
}

.feature-item p {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
  font-size: 0.9rem;
}

/* Cash/Coin Graphics - We'll add these as CSS sprites or icons */
.cash-icon::before {
  content: '💰';
  font-size: 1.2em;
  margin-right: 0.5rem;
  filter: hue-rotate(120deg) brightness(1.5);
}

.coin-icon::before {
  content: '🪙';
  font-size: 1.2em;
  margin-right: 0.5rem;
  filter: hue-rotate(60deg) brightness(1.5);
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .ascii-logo {
    font-size: 0.5rem;
    line-height: 0.9;
    padding: 0.5rem 0.5rem 0.5rem 2.5rem;
  }

  .snakepit-title {
    font-size: 3rem;
  }

  .snakepit-subtitle {
    font-size: 1.2rem;
  }

  .game-stats {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .stat-card {
    min-width: 100px;
  }

  .stat-number {
    font-size: 2rem;
  }

  .mode-buttons {
    flex-direction: column;
    align-items: center;
  }

  .mode-button {
    min-width: 300px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .landing-content {
    gap: 2rem;
    padding: 1rem;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .ascii-logo {
    font-size: 0.3rem;
    line-height: 0.8;
    padding: 0.3rem 0.3rem 0.3rem 1.8rem;
  }

  .game-ui {
    right: 150px; /* Further adjust for very small screens */
    padding: 0.5rem 1rem;
  }

  .ui-stats {
    gap: 0.5rem;
  }

  .stat-item {
    font-size: 0.9rem;
    padding: 0.3rem 0.6rem;
  }

  .minimap-container {
    width: 120px;
    height: 100px;
    top: 5px;
    right: 5px;
  }
}

/* Cashout button specific styles */
.cashout-container {
  margin-top: 15px;
  padding: 10px;
  border: 1px solid var(--neon-green);
  border-radius: 8px;
  background: rgba(0, 255, 0, 0.05);
  text-align: center;
}

.cashout-button {
  background: transparent;
  border: 2px solid var(--neon-green);
  color: var(--neon-green);
  padding: 8px 16px;
  font-family: 'Orbitron', monospace;
  font-weight: bold;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: 0 0 10px var(--neon-green);
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
  animation: cashout-pulse 2s infinite;
  border-radius: 5px;
}

.cashout-button:hover {
  background: var(--neon-green);
  color: var(--bg-dark);
  box-shadow: 0 0 25px var(--neon-green);
  animation: none;
  transform: scale(1.05);
}

.cashout-button:active {
  transform: scale(0.98);
}

.cashout-button:disabled,
.cashout-button.neon-disabled {
  border-color: #666;
  color: #666;
  cursor: not-allowed;
  animation: none;
  box-shadow: none;
  text-shadow: none;
  opacity: 0.5;
}

.cashout-button:disabled:hover,
.cashout-button.neon-disabled:hover {
  background: transparent;
  color: #666;
  box-shadow: none;
  transform: none;
}

.cashout-hint {
  font-size: 10px;
  margin-top: 5px;
  opacity: 0.7;
  color: var(--text-secondary);
}

@keyframes cashout-pulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(0, 255, 0, 0.6);
  }
}

/* Auth Modal Styles */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.auth-modal {
  background: var(--bg-secondary);
  border: 3px solid var(--neon-red);
  border-radius: 15px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  box-shadow: var(--glow-large) var(--neon-red);
  animation: modal-appear 0.3s ease-out;
}

.auth-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.auth-modal-header h2 {
  margin: 0;
  font-size: 1.8rem;
}

.auth-intro {
  text-align: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 0, 0, 0.1);
  border-radius: 8px;
  border: 1px solid var(--neon-red);
}

.auth-intro p {
  margin: 0;
  font-size: 1rem;
}

.close-button {
  position: relative;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-red);
  color: var(--neon-red);
  padding: 1rem 1.5rem;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.close-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 0, 0, 0.2), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.close-button:hover::before {
  opacity: 1;
}

.close-button:hover {
  background: rgba(255, 0, 0, 0.2);
  color: #ffffff;
  box-shadow:
    0 0 30px rgba(255, 0, 0, 0.6),
    0 0 60px rgba(255, 0, 0, 0.3);
  transform: scale(1.1) rotate(90deg);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 1rem;
  font-weight: bold;
  color: var(--neon-red);
  text-shadow: var(--glow-small) var(--neon-red);
}

.neon-input {
  background: black;
  border: 2px solid var(--neon-red);
  border-radius: 8px;
  padding: 1rem;
  font-size: 1rem;
  color: white;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 10px rgba(255, 0, 0, 0.1);
}

.neon-input:focus {
  outline: none;
  box-shadow: var(--glow-medium) var(--neon-red), inset 0 0 15px rgba(255, 0, 0, 0.2);
  border-color: var(--neon-red);
}

.neon-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.neon-input.neon-purple {
  border-color: var(--neon-red);
}

.neon-input.neon-green {
  border-color: var(--neon-red);
}

.neon-input.neon-yellow {
  border-color: var(--neon-red);
}

.error-message {
  padding: 1rem;
  background: rgba(255, 0, 0, 0.1);
  border: 2px solid var(--neon-red);
  border-radius: 8px;
  text-align: center;
}

.auth-submit-button {
  padding: 1rem 2rem;
  font-size: 1.2rem;
  font-weight: bold;
  border-radius: 10px;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: var(--bg-primary);
  border: 2px solid var(--neon-red);
  color: var(--neon-red);
  box-shadow: var(--glow-small) var(--neon-red);
}

.auth-submit-button:hover:not(:disabled) {
  background: var(--neon-red);
  color: var(--bg-primary);
  box-shadow: var(--glow-large) var(--neon-red);
  transform: translateY(-2px);
}

.auth-submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.auth-toggle {
  text-align: center;
  margin-top: 1.5rem;
}

.toggle-button {
  background: none;
  border: none;
  cursor: pointer;
  text-decoration: underline;
  font-size: 1rem;
  margin-left: 0.5rem;
}

.auth-info {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
}

.auth-info p {
  margin: 0.5rem 0;
}

/* Leaderboard Modal Styles */
.leaderboard-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.leaderboard-modal {
  background: var(--bg-secondary);
  border: 3px solid var(--neon-yellow);
  border-radius: 15px;
  padding: 2rem;
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--glow-large) var(--neon-yellow);
  animation: modal-appear 0.3s ease-out;
}

.leaderboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.leaderboard-header h2 {
  margin: 0;
  font-size: 2rem;
}

.leaderboard-controls {
  margin-bottom: 2rem;
}

.mode-selector {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.mode-button {
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.mode-button.active {
  box-shadow: var(--glow-medium);
  transform: scale(1.05);
}

.leaderboard-content {
  min-height: 300px;
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  gap: 1rem;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.leaderboard-headers {
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 2px solid var(--neon-dim);
  font-weight: bold;
}

.leaderboard-row {
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
}

.leaderboard-row:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.leaderboard-row.top-three {
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid var(--neon-yellow);
}

.leaderboard-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 2px solid var(--neon-dim);
}

.refresh-button {
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: bold;
}

.leaderboard-info {
  font-size: 0.9rem;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* User Profile Modal Styles - Modern Fortnite Style */
.profile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fade-in 0.3s ease-out;
}

.profile-modal {
  background: linear-gradient(135deg, rgba(10, 10, 20, 0.98) 0%, rgba(20, 20, 40, 0.95) 100%);
  border: 3px solid var(--neon-cyan);
  border-radius: 25px;
  padding: 0;
  max-width: 1200px;
  width: 95%;
  max-height: 95vh;
  overflow: hidden;
  box-shadow:
    0 0 50px rgba(0, 255, 255, 0.3),
    0 0 100px rgba(0, 255, 255, 0.1),
    inset 0 0 50px rgba(0, 255, 255, 0.05);
  animation: modal-slide-up 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

.profile-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(0, 255, 255, 0.1) 0%,
    transparent 25%,
    transparent 75%,
    rgba(255, 0, 255, 0.1) 100%);
  pointer-events: none;
  z-index: 1;
}

.profile-header {
  position: relative;
  z-index: 2;
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.2) 0%, rgba(255, 0, 255, 0.2) 100%);
  padding: 2rem 2.5rem;
  border-bottom: 2px solid rgba(0, 255, 255, 0.3);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-title h2 {
  margin: 0;
  font-size: 2.5rem;
  font-family: 'Orbitron', monospace;
  font-weight: 900;
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
  display: flex;
  align-items: center;
  gap: 15px;
}

.profile-title h2 .icon-snake {
  font-size: 2rem;
  color: var(--neon-green);
  text-shadow: 0 0 20px var(--neon-green);
  animation: snake-pulse 2s ease-in-out infinite;
}

@keyframes snake-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.profile-subtitle {
  font-size: 1.1rem;
  margin-top: 0.8rem;
  color: var(--neon-yellow);
  text-shadow: 0 0 10px var(--neon-yellow);
  font-weight: 600;
}

.profile-content {
  position: relative;
  z-index: 2;
  padding: 2rem 2.5rem;
  overflow-y: auto;
  max-height: calc(95vh - 120px);
}

/* Profile Tabs - Modern Gaming Style */
.profile-tabs {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 2.5rem;
  padding: 0 1rem;
}

.profile-tab {
  position: relative;
  padding: 1rem 2rem;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 15px;
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 1rem;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.profile-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.profile-tab:hover::before {
  left: 100%;
}

.profile-tab:hover {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
  transform: translateY(-3px);
}

.profile-tab.active {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.2) 0%, rgba(255, 0, 255, 0.2) 100%);
  border-color: var(--neon-purple);
  color: var(--neon-cyan);
  box-shadow:
    0 0 30px rgba(255, 0, 255, 0.5),
    inset 0 0 20px rgba(0, 255, 255, 0.1);
  transform: translateY(-5px) scale(1.05);
}

.profile-tab.active::before {
  display: none;
}

.profile-tab-content {
  min-height: 500px;
  animation: tab-content-fade-in 0.4s ease-out;
}

@keyframes tab-content-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Overview Content - Enhanced Gaming Style */
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.profile-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.stat-card {
  position: relative;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 40, 0.6) 100%);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(0, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 0, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.stat-card:hover {
  border-color: var(--neon-cyan);
  box-shadow:
    0 0 30px rgba(0, 255, 255, 0.4),
    0 0 60px rgba(0, 255, 255, 0.2);
  transform: translateY(-8px) scale(1.02);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-icon {
  font-size: 3.5rem;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  border: 2px solid currentColor;
  box-shadow: 0 0 20px currentColor;
  animation: stat-icon-glow 3s ease-in-out infinite;
}

@keyframes stat-icon-glow {
  0%, 100% {
    box-shadow: 0 0 20px currentColor;
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30px currentColor, 0 0 40px currentColor;
    transform: scale(1.05);
  }
}

.stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 900;
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 15px currentColor;
  line-height: 1;
}

.profile-info-card {
  position: relative;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(30, 30, 60, 0.6) 100%);
  border: 2px solid rgba(255, 0, 255, 0.4);
  border-radius: 20px;
  padding: 2.5rem;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.profile-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(255, 0, 255, 0.1) 0%,
    transparent 50%,
    rgba(0, 255, 255, 0.1) 100%);
  pointer-events: none;
}

.profile-info-card h3 {
  position: relative;
  z-index: 1;
  margin: 0 0 2rem 0;
  font-size: 1.8rem;
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.info-grid {
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem 1.5rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 15px;
  border: 2px solid rgba(0, 255, 255, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.info-item:hover {
  border-color: var(--neon-cyan);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  transform: translateY(-2px);
}

.info-label {
  font-family: 'Orbitron', monospace;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.info-value {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  font-size: 1.1rem;
  text-shadow: 0 0 10px currentColor;
}

/* Inventory Content */
.inventory-content {
  text-align: center;
}

.inventory-content h3 {
  margin: 0 0 2rem 0;
  font-size: 1.5rem;
}

.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.inventory-item {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--neon-dim);
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
}

.inventory-item.coming-soon {
  opacity: 0.6;
  border-style: dashed;
}

.inventory-item:hover:not(.coming-soon) {
  border-color: var(--neon-cyan);
  box-shadow: var(--glow-small) var(--neon-cyan);
  transform: translateY(-3px);
}

.item-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.item-name {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.item-description {
  font-size: 0.9rem;
}

.coming-soon-note {
  text-align: center;
  font-style: italic;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

/* Transactions Content */
.transactions-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.transactions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.transactions-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.refresh-btn {
  padding: 0.6rem 1.2rem;
  border-radius: 6px;
  font-size: 0.9rem;
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 3rem;
  font-size: 1.1rem;
}

.transactions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--neon-dim);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.transaction-item:hover {
  border-color: var(--neon-cyan);
  box-shadow: var(--glow-small) var(--neon-cyan);
}

.transaction-icon {
  font-size: 1.5rem;
  width: 40px;
  text-align: center;
}

.transaction-details {
  flex: 1;
}

.transaction-type {
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.3rem;
}

.transaction-description {
  font-size: 0.9rem;
  margin-bottom: 0.3rem;
}

.transaction-date {
  font-size: 0.8rem;
}

.transaction-amount {
  font-family: 'Orbitron', monospace;
  font-weight: bold;
  font-size: 1.1rem;
  text-align: right;
}

/* Settings Content */
.settings-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-content h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
}

.settings-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--neon-dim);
  border-radius: 10px;
  padding: 1.5rem;
}

.settings-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.setting-item {
  margin-bottom: 1.5rem;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  font-size: 1rem;
}

.setting-input {
  width: 100%;
  max-width: 300px;
  padding: 0.8rem;
  font-size: 1rem;
  border-radius: 6px;
}

.setting-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.setting-checkbox {
  margin-right: 0.5rem;
  transform: scale(1.2);
}

.setting-slider {
  width: 200px;
  margin-right: 1rem;
}

.volume-display {
  font-family: 'Orbitron', monospace;
  font-weight: bold;
}

.settings-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.save-btn,
.cancel-btn,
.edit-btn {
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: bold;
}

.danger-zone {
  background: rgba(255, 0, 0, 0.1);
  border: 2px solid var(--neon-red);
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
}

.danger-zone h4 {
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
}

.signout-btn {
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-weight: bold;
}

/* Achievements Content */
.achievements-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.achievements-content h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  text-align: center;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.achievement-item {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--neon-dim);
  border-radius: 10px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.achievement-item.locked {
  opacity: 0.6;
}

.achievement-item:hover {
  border-color: var(--neon-yellow);
  box-shadow: var(--glow-small) var(--neon-yellow);
  transform: translateY(-2px);
}

.achievement-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.achievement-name {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.achievement-description {
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.achievement-progress {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.8rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
}

/* Responsive Design for Profile */
@media (max-width: 768px) {
  .profile-modal {
    width: 98%;
    padding: 1rem;
    max-height: 95vh;
  }

  .profile-tabs {
    flex-direction: column;
    gap: 0.5rem;
  }

  .profile-tab {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
  }

  .profile-stats-grid {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .inventory-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .settings-actions {
    flex-direction: column;
    align-items: center;
  }

  .transactions-header {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }
}

/* Cashout Success Popup */
.cashout-success {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.cashout-success-content {
  background: var(--bg-secondary);
  border: 3px solid var(--neon-green);
  border-radius: 15px;
  padding: 3rem;
  text-align: center;
  box-shadow: var(--glow-large) var(--neon-green);
  animation: cashout-success-pulse 1s ease-in-out infinite alternate;
  max-width: 500px;
}

@keyframes cashout-success-pulse {
  from {
    box-shadow: var(--glow-large) var(--neon-green);
  }
  to {
    box-shadow: var(--glow-xl) var(--neon-green);
  }
}

.cashout-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  color: var(--neon-green);
  margin: 0 0 2rem 0;
  text-shadow: var(--glow-medium) var(--neon-green);
  animation: cashout-title-glow 2s ease-in-out infinite alternate;
}

@keyframes cashout-title-glow {
  from {
    text-shadow: var(--glow-medium) var(--neon-green);
  }
  to {
    text-shadow: var(--glow-large) var(--neon-green);
  }
}

.cashout-amount {
  margin: 2rem 0;
  padding: 1.5rem;
  border: 2px solid var(--neon-yellow);
  border-radius: 10px;
  background: rgba(255, 255, 0, 0.1);
  box-shadow: var(--glow-small) var(--neon-yellow);
}

.amount-label {
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  color: var(--neon-yellow);
  margin-bottom: 0.5rem;
  text-shadow: var(--glow-small) var(--neon-yellow);
}

.amount-value {
  font-family: 'Orbitron', monospace;
  font-size: 3rem;
  font-weight: 900;
  color: var(--neon-green);
  text-shadow: var(--glow-medium) var(--neon-green);
  animation: amount-pulse 1.5s ease-in-out infinite alternate;
}

@keyframes amount-pulse {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

.success-message {
  margin: 2rem 0;
  color: var(--text-primary);
}

.success-message p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

.spectate-info {
  margin: 2rem 0;
  padding: 1rem;
  border: 1px solid var(--neon-cyan);
  border-radius: 8px;
  background: rgba(0, 255, 255, 0.05);
  color: var(--text-secondary);
}

.spectate-info p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.spectate-info strong {
  color: var(--neon-cyan);
  font-weight: bold;
}

.cashout-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* Controller Status */
.controller-status {
  position: absolute;
  bottom: 10px;
  left: 10px;
  background: var(--bg-secondary);
  border: 2px solid var(--neon-purple);
  border-radius: 8px;
  padding: 0.75rem;
  max-width: 280px;
  box-shadow: var(--glow-small) var(--neon-purple);
  z-index: 20;
}

.controller-header {
  margin-bottom: 0.5rem;
}

.controller-title {
  font-family: 'Orbitron', monospace;
  color: var(--neon-purple);
  font-size: 0.9rem;
  font-weight: bold;
  text-shadow: var(--glow-small) var(--neon-purple);
}

.no-controllers {
  text-align: center;
  padding: 0.5rem 0;
}

.controller-hint {
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
}

.controller-instructions {
  font-size: 0.7rem;
  color: var(--text-dim);
  line-height: 1.3;
}

.connected-controllers {
  margin-bottom: 0.75rem;
}

.controller-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.3rem 0;
  border-bottom: 1px solid rgba(128, 0, 255, 0.2);
}

.controller-item:last-child {
  border-bottom: none;
}

.controller-icon {
  font-size: 1.2rem;
  text-shadow: 0 0 8px currentColor;
}

.controller-info {
  flex: 1;
}

.controller-type {
  font-family: 'Share Tech Mono', monospace;
  font-size: 0.8rem;
  color: var(--text-primary);
  font-weight: 600;
}

.controller-id {
  font-size: 0.7rem;
  color: var(--text-secondary);
}

.controller-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--neon-green);
  box-shadow: 0 0 6px var(--neon-green);
  animation: controller-pulse 2s infinite;
}

@keyframes controller-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.controller-controls {
  border-top: 1px solid rgba(128, 0, 255, 0.3);
  padding-top: 0.5rem;
}

.control-section {
  margin-bottom: 0.5rem;
}

.control-section:last-child {
  margin-bottom: 0;
}

.control-title {
  font-family: 'Orbitron', monospace;
  font-size: 0.7rem;
  color: var(--neon-cyan);
  font-weight: bold;
  margin-bottom: 0.2rem;
  text-transform: uppercase;
}

.control-item {
  font-size: 0.65rem;
  color: var(--text-secondary);
  margin-bottom: 0.1rem;
  padding-left: 0.5rem;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .controller-status {
    bottom: 5px;
    left: 5px;
    max-width: 200px;
    padding: 0.5rem;
  }

  .controller-title {
    font-size: 0.8rem;
  }

  .controller-controls {
    display: none; /* Hide detailed controls on mobile to save space */
  }
}

/* Fortnite Lobby Styles */
.fortnite-lobby {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.fortnite-lobby-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  position: relative;
  z-index: 1;
}

/* Top Navigation Bar */
.lobby-top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid rgba(0, 255, 136, 0.3);
}

.nav-left {
  display: flex;
  gap: 5px;
}

.nav-tab {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 8px;
  color: #ffffff;
  font-weight: bold;
  font-size: 0.9em;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-tab:hover {
  background: rgba(0, 255, 136, 0.2);
  border-color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
  transform: translateY(-2px);
}

.nav-tab.active {
  background: linear-gradient(135deg, var(--neon-cyan), var(--neon-green));
  border-color: var(--neon-yellow);
  color: #000000;
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.v-bucks-btn {
  padding: 10px 20px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border: 2px solid #ffa500;
  border-radius: 25px;
  color: #000000;
  font-weight: bold;
  font-size: 0.9em;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.v-bucks-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.user-profile-btn {
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid var(--neon-purple);
  border-radius: 50px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-profile-btn:hover {
  background: rgba(255, 0, 255, 0.2);
  box-shadow: 0 0 15px rgba(255, 0, 255, 0.3);
}

.user-level {
  font-size: 0.8em;
  font-weight: bold;
  color: var(--neon-yellow);
}

/* Main Lobby Content */
.lobby-main-content {
  flex: 1;
  display: flex;
  padding: 20px;
  gap: 20px;
  min-height: 0;
  width: 100%;
  max-width: 100vw;
}

/* Left Game Mode Panel */
.lobby-left-panel {
  flex: 0 0 300px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-cyan);
  border-radius: 15px;
  padding: 25px;
  backdrop-filter: blur(10px);
}

.game-mode-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mode-title {
  font-size: 1.8em;
  font-weight: bold;
  text-align: center;
  color: var(--neon-yellow);
  text-shadow: 0 0 10px var(--neon-yellow);
  margin-bottom: 10px;
}

.mode-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-option:hover {
  background: rgba(0, 255, 136, 0.2);
  border-color: var(--neon-cyan);
  transform: translateX(5px);
}

.mode-option.selected {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.3), rgba(0, 255, 255, 0.3));
  border-color: var(--neon-green);
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.4);
}

.mode-icon {
  font-size: 2em;
  filter: drop-shadow(0 0 5px rgba(0, 255, 136, 0.5));
}

.mode-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.mode-name {
  font-size: 1.2em;
  font-weight: bold;
  color: var(--neon-cyan);
  text-shadow: 0 0 5px var(--neon-cyan);
}

.mode-desc {
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.8);
}

/* Large Play Button */
.lobby-play-button {
  padding: 20px 40px;
  background: linear-gradient(135deg, var(--neon-green), var(--neon-cyan));
  border: 3px solid var(--neon-yellow);
  border-radius: 15px;
  color: #000000;
  font-size: 1.5em;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  box-shadow: 0 0 30px rgba(0, 255, 136, 0.5);
}

.lobby-play-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 0 40px rgba(0, 255, 136, 0.8);
}

.lobby-play-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.player-count {
  text-align: center;
  padding: 15px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.count-label {
  display: block;
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 5px;
}

.count-number {
  font-size: 1.4em;
  font-weight: bold;
  color: var(--neon-green);
  text-shadow: 0 0 10px var(--neon-green);
}

/* Tab System Styles */
.main-content-area {
  display: flex;
  gap: 20px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.left-sidebar {
  flex: 0 0 200px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-cyan);
  border-radius: 10px;
  padding: 20px;
}

/* Central Character Display Area */
.lobby-center-area {
  position: relative;
  flex: 1;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.character-display {
  position: absolute;
  /* Position based on actual background image dimensions: 1536x1024 */
  /* Coordinates (991, 731) relative to background image */
  left: 80.52%; /* 991/1536 = 64.52% */
  top: 34.39%; /* 731/1024 = 71.39% */
  transform: translate(-50%, -50%); /* Center the container on the coordinates */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  z-index: 10;
  pointer-events: none;
  width: 300px;
  height: 400px;
}

/* Responsive adjustments for different desktop screen sizes */
/* All based on background image dimensions: 1536x1024 */
@media screen and (min-width: 1200px) and (max-width: 1599px) {
  .character-display {
    /* Smaller desktop screens */
    transform: translate(-50%, -50%) scale(0.8);
  }
}

@media screen and (min-width: 1600px) and (max-width: 2559px) {
  .character-display {
    /* Standard desktop screens */
    transform: translate(-50%, -50%) scale(1.0);
  }
}

@media screen and (min-width: 2560px) {
  .character-display {
    /* Large/4K screens */
    transform: translate(-50%, -50%) scale(1.3);
  }
}

.character-platform {
  position: relative;
  width: 100%;
  height: 100%; /* Reduced to leave space for character info */
  min-height: 300px; /* Reduced minimum height */
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto; /* Enable interactions for the 3D model */
}

.character-model {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: auto; /* Enable interactions for the 3D model */
}

.avatar-3d {
  border: 2px solid rgba(255, 102, 0, 0.5);
  box-shadow:
    0 0 20px rgba(255, 102, 0, 0.3),
    0 0 40px rgba(255, 102, 0, 0.2),
    inset 0 0 20px rgba(255, 102, 0, 0.1);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(20, 20, 20, 0.9));
  transition: all 0.3s ease;
}

.avatar-3d:hover {
  border-color: rgba(255, 102, 0, 0.8);
  box-shadow:
    0 0 30px rgba(255, 102, 0, 0.5),
    0 0 60px rgba(255, 102, 0, 0.3),
    inset 0 0 30px rgba(255, 102, 0, 0.2);
  transform: scale(1.02);
}

.avatar-3d-seamless {
  background: transparent;
  border: none;
  box-shadow: none;
  cursor: grab;
  transition: none;
  width: 100% !important;
  height: 100% !important;
  min-width: 300px;
  min-height: 400px;
}

.avatar-3d-seamless:active {
  cursor: grabbing;
}

.snake-character {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.snake-head {
  font-size: 8em;
  filter: drop-shadow(0 0 20px rgba(0, 255, 136, 0.8));
  animation: float 3s ease-in-out infinite;
}

.snake-body {
  width: 100px;
  height: 20px;
  background: linear-gradient(90deg, var(--neon-green), var(--neon-cyan));
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.platform-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(0, 255, 136, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

.character-info {
  text-align: center;
}

.character-name {
  font-size: 1.8em;
  font-weight: bold;
  color: var(--neon-yellow);
  text-shadow: 0 0 10px var(--neon-yellow);
  margin-bottom: 15px;
}

.character-stats {
  display: flex;
  gap: 30px;
  justify-content: center;
}

.stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1em;
  color: var(--neon-cyan);
  text-shadow: 0 0 5px var(--neon-cyan);
}

/* Right Side Panels */
.lobby-right-panel {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setup-panel {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-purple);
  border-radius: 15px;
  padding: 25px;
  backdrop-filter: blur(10px);
}

.panel-title {
  font-size: 1.4em;
  font-weight: bold;
  text-align: center;
  color: var(--neon-purple);
  text-shadow: 0 0 10px var(--neon-purple);
  margin-bottom: 20px;
}

.setup-field {
  margin-bottom: 20px;
}

.setup-field label {
  display: block;
  font-size: 1em;
  font-weight: bold;
  color: var(--neon-cyan);
  margin-bottom: 8px;
  text-shadow: 0 0 5px var(--neon-cyan);
}

.setup-input {
  width: 100%;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: #ffffff;
  font-size: 1em;
  transition: all 0.3s ease;
}

.setup-input:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.wager-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.wager-btn {
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 8px;
  color: #ffffff;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.wager-btn:hover:not(:disabled) {
  background: rgba(255, 215, 0, 0.2);
  border-color: var(--neon-yellow);
}

.wager-btn.selected {
  background: linear-gradient(135deg, var(--neon-yellow), var(--neon-orange));
  border-color: var(--neon-yellow);
  color: #000000;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
}

.wager-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.balance-display {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
}

.balance-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 1.2em;
  font-weight: bold;
  color: var(--neon-green);
  margin-bottom: 8px;
}

.balance-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.balance-usd {
  font-size: 1em;
  color: rgba(255, 255, 255, 0.8);
}

.quick-actions-panel {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-orange);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.quick-action-btn {
  width: 100%;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 8px;
  color: #ffffff;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.quick-action-btn:hover {
  background: rgba(255, 165, 0, 0.2);
  border-color: var(--neon-orange);
  transform: translateX(5px);
}

.quick-action-btn:last-child {
  margin-bottom: 0;
}

/* Bottom Action Bar */
.lobby-bottom-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-top: 2px solid rgba(0, 255, 136, 0.3);
}

.bottom-left,
.bottom-right {
  display: flex;
  gap: 15px;
}

.bottom-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.bottom-action-btn {
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  border-radius: 8px;
  color: #ffffff;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bottom-action-btn:hover {
  background: rgba(0, 255, 136, 0.2);
  border-color: var(--neon-cyan);
  transform: translateY(-2px);
}

.ready-status {
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  text-align: center;
}

.ready-text {
  color: var(--neon-green);
  text-shadow: 0 0 10px var(--neon-green);
}

.not-ready-text {
  color: var(--neon-orange);
  text-shadow: 0 0 10px var(--neon-orange);
}

/* Mobile Responsiveness for Fortnite Lobby */
@media (max-width: 1200px) {
  .lobby-main-content {
    flex-direction: column;
    gap: 15px;
  }

  .lobby-left-panel,
  .lobby-right-panel {
    flex: none;
    width: 100%;
  }

  .lobby-center-area {
    order: -1;
  }

  .character-platform {
    width: 200px;
    height: 200px;
  }

  .snake-head {
    font-size: 5em;
  }
}

@media (max-width: 768px) {
  .lobby-top-nav {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .nav-left {
    flex-wrap: wrap;
    justify-content: center;
  }

  .nav-tab {
    padding: 8px 12px;
    font-size: 0.8em;
  }

  .lobby-main-content {
    padding: 10px;
  }

  .setup-panel,
  .quick-actions-panel {
    padding: 15px;
  }

  .wager-selector {
    grid-template-columns: repeat(2, 1fr);
  }

  .character-stats {
    flex-direction: column;
    gap: 15px;
  }

  .lobby-bottom-bar {
    flex-direction: column;
    gap: 10px;
    padding: 15px;
  }

  .bottom-left,
  .bottom-right {
    justify-content: center;
  }
}

.nav-tabs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.nav-tab {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid var(--neon-cyan);
  color: var(--neon-cyan);
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
}

.nav-tab:hover {
  background: rgba(0, 255, 255, 0.2);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.nav-tab.active {
  background: rgba(0, 255, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
  border-color: var(--neon-green);
  color: var(--neon-green);
}

.main-content {
  flex: 1;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-purple);
  border-radius: 10px;
  padding: 30px;
  min-height: 500px;
}

.right-sidebar {
  flex: 0 0 180px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-orange);
  border-radius: 10px;
  padding: 20px;
  height: fit-content;
}

.sidebar-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sidebar-link {
  background: rgba(255, 165, 0, 0.1);
  border: 1px solid var(--neon-orange);
  color: var(--neon-orange);
  padding: 10px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 12px;
}

.sidebar-link:hover {
  background: rgba(255, 165, 0, 0.2);
  box-shadow: 0 0 8px rgba(255, 165, 0, 0.3);
}

/* Content Styles */
.content-title {
  font-size: 28px;
  margin-bottom: 10px;
  text-align: center;
}

.content-description {
  text-align: center;
  margin-bottom: 30px;
  font-size: 16px;
}

/* Shop Styles */
.shop-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.shop-item {
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid var(--neon-cyan);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.shop-item:hover {
  border-color: var(--neon-green);
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);
}

.shop-item .item-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.shop-item h3 {
  color: var(--neon-cyan);
  margin-bottom: 10px;
}

.shop-item p {
  color: var(--neon-dim);
  margin-bottom: 15px;
  font-size: 14px;
}

.buy-btn {
  padding: 8px 16px;
  font-size: 14px;
}

/* Inventory Styles */
.inventory-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  justify-content: center;
}

.inv-tab {
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid var(--neon-cyan);
  color: var(--neon-cyan);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.inv-tab.active {
  background: rgba(0, 255, 255, 0.3);
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.inventory-item {
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid var(--neon-purple);
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
}

.inventory-item.equipped {
  border-color: var(--neon-green);
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.item-preview {
  font-size: 32px;
  margin-bottom: 10px;
}

.equipped-badge {
  background: var(--neon-green);
  color: black;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
}

/* Friends Styles */
.friends-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
}

.friend-btn {
  padding: 10px 20px;
}

.friends-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.friend-item {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid var(--neon-cyan);
  border-radius: 8px;
  padding: 15px;
  gap: 15px;
}

.friend-avatar {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 255, 255, 0.2);
  border-radius: 50%;
}

.friend-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.friend-name {
  color: var(--neon-cyan);
  font-weight: bold;
}

.friend-status.online {
  color: var(--neon-green);
  font-size: 12px;
}

.friend-status.offline {
  color: var(--neon-dim);
  font-size: 12px;
}

.invite-btn {
  padding: 6px 12px;
  font-size: 12px;
}

/* More Section Styles */
.more-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.more-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.more-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.more-icon {
  font-size: 32px;
}

/* Modal Styles for Information Components */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
}

.modal-content {
  position: relative;
  background: rgba(10, 10, 15, 0.95);
  border: 2px solid var(--neon-cyan);
  border-radius: 15px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--glow-medium) var(--neon-cyan);
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modal-slide-up {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
}

.modal-title {
  font-family: 'Orbitron', monospace;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.modal-close {
  background: transparent;
  border: 2px solid var(--neon-orange);
  color: var(--neon-orange);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--neon-orange);
  color: var(--bg-primary);
  box-shadow: var(--glow-small) var(--neon-orange);
}

.modal-body {
  padding: 30px;
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.modal-footer {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 20px 30px;
  border-top: 1px solid rgba(0, 255, 255, 0.3);
}

/* Top User Status for Play Now Screen */
.top-user-status {
  position: fixed;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 10;
  pointer-events: auto;
}

.user-status {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-green);
  border-radius: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Orbitron', monospace;
  font-weight: bold;
}

.user-status:hover {
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.5);
  transform: translateY(-2px);
}

.login-button {
  padding: 8px 16px;
  font-size: 14px;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-purple);
  border-radius: 8px;
  padding: 8px 12px;
}

.audio-btn {
  padding: 4px 8px;
  font-size: 16px;
  min-width: 40px;
}

.volume-control {
  display: flex;
  align-items: center;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(128, 0, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--neon-purple);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 8px rgba(128, 0, 255, 0.5);
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--neon-purple);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 8px rgba(128, 0, 255, 0.5);
}

/* Settings Modal Styles */
.settings-modal {
  border-color: var(--neon-purple);
  box-shadow: var(--glow-large) var(--neon-purple);
}

.settings-section {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(128, 0, 255, 0.3);
  border-radius: 10px;
}

.section-title {
  font-family: 'Orbitron', monospace;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  gap: 15px;
}

.setting-label {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.setting-input, .setting-select {
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid var(--neon-cyan);
  border-radius: 6px;
  padding: 8px 12px;
  color: var(--text-primary);
  font-family: 'Share Tech Mono', monospace;
  width: 200px;
}

.setting-input:focus, .setting-select:focus {
  outline: none;
  box-shadow: var(--glow-small) var(--neon-cyan);
}

.setting-checkbox {
  width: 18px;
  height: 18px;
  accent-color: var(--neon-cyan);
}

.setting-slider {
  width: 150px;
  height: 4px;
  background: rgba(0, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.setting-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--neon-cyan);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--glow-small) var(--neon-cyan);
}

.setting-value {
  font-family: 'Share Tech Mono', monospace;
  color: var(--neon-cyan);
  font-weight: bold;
  min-width: 40px;
  text-align: right;
}

.settings-message {
  padding: 10px 15px;
  border-radius: 6px;
  margin-top: 20px;
  font-family: 'Share Tech Mono', monospace;
  text-align: center;
}

.settings-message.success {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid var(--neon-green);
  color: var(--neon-green);
}

.settings-message.error {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid var(--neon-orange);
  color: var(--neon-orange);
}

/* Help Modal Styles */
.help-modal {
  border-color: var(--neon-green);
  box-shadow: var(--glow-large) var(--neon-green);
  max-width: 900px;
}

.help-navigation {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.help-nav-btn {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid var(--neon-green);
  color: var(--neon-green);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Share Tech Mono', monospace;
  font-size: 14px;
}

.help-nav-btn:hover, .help-nav-btn.active {
  background: rgba(0, 255, 0, 0.3);
  box-shadow: var(--glow-small) var(--neon-green);
}

.help-section {
  animation: fade-in 0.3s ease-in;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.help-item {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 255, 0, 0.3);
  border-radius: 8px;
}

.help-item h4 {
  font-family: 'Orbitron', monospace;
  color: var(--neon-cyan);
  margin: 0 0 10px 0;
  font-size: 16px;
}

.help-item p {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-primary);
  margin: 0 0 10px 0;
  line-height: 1.5;
}

.help-item ul {
  margin: 10px 0 0 20px;
  color: var(--text-secondary);
}

.help-item li {
  margin-bottom: 5px;
  font-family: 'Share Tech Mono', monospace;
}

.controls-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.control-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 6px;
}

.control-key {
  font-family: 'Orbitron', monospace;
  color: var(--neon-cyan);
  font-weight: bold;
  background: rgba(0, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--neon-cyan);
}

.control-desc {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-primary);
}

/* Glossary Modal Styles */
.glossary-modal {
  border-color: var(--neon-yellow);
  box-shadow: var(--glow-large) var(--neon-yellow);
  max-width: 1000px;
}

.glossary-controls {
  margin-bottom: 30px;
}

.search-container {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid var(--neon-yellow);
  border-radius: 8px;
  padding: 12px 16px;
  color: var(--text-primary);
  font-family: 'Share Tech Mono', monospace;
  font-size: 16px;
}

.search-input:focus {
  outline: none;
  box-shadow: var(--glow-small) var(--neon-yellow);
}

.category-filters {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-btn {
  background: rgba(255, 255, 0, 0.1);
  border: 1px solid var(--neon-yellow);
  color: var(--neon-yellow);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Share Tech Mono', monospace;
  font-size: 14px;
}

.filter-btn:hover, .filter-btn.active {
  background: rgba(255, 255, 0, 0.3);
  box-shadow: var(--glow-small) var(--neon-yellow);
}

.glossary-terms {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.glossary-term {
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 0, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.glossary-term:hover {
  border-color: var(--neon-yellow);
  box-shadow: var(--glow-small) var(--neon-yellow);
}

.term-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.term-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.term-name {
  font-family: 'Orbitron', monospace;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  flex: 1;
}

.term-category {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Share Tech Mono', monospace;
  text-transform: uppercase;
  font-weight: bold;
}

.term-category.gameplay {
  background: rgba(0, 255, 0, 0.2);
  color: var(--neon-green);
  border: 1px solid var(--neon-green);
}

.term-category.financial {
  background: rgba(255, 255, 0, 0.2);
  color: var(--neon-yellow);
  border: 1px solid var(--neon-yellow);
}

.term-category.technical {
  background: rgba(0, 255, 255, 0.2);
  color: var(--neon-cyan);
  border: 1px solid var(--neon-cyan);
}

.term-category.social {
  background: rgba(255, 0, 255, 0.2);
  color: var(--neon-pink);
  border: 1px solid var(--neon-pink);
}

.term-definition {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-primary);
  line-height: 1.5;
  margin: 0;
}

.no-results {
  text-align: center;
  padding: 40px;
}

.glossary-stats {
  display: flex;
  gap: 20px;
  justify-content: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 0, 0.3);
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-family: 'Orbitron', monospace;
  font-size: 24px;
  font-weight: bold;
  color: var(--neon-yellow);
}

.stat-label {
  font-family: 'Share Tech Mono', monospace;
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
}

/* About Modal Styles */
.about-modal {
  border-color: var(--neon-orange);
  box-shadow: var(--glow-large) var(--neon-orange);
  max-width: 1000px;
}

.about-section {
  margin-bottom: 30px;
  padding: 25px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 10px;
}

.about-text {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-primary);
  line-height: 1.6;
  margin: 0;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 8px;
}

.feature-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
  flex-shrink: 0;
}

.feature-content h4 {
  font-family: 'Orbitron', monospace;
  color: var(--neon-cyan);
  margin: 0 0 8px 0;
  font-size: 16px;
}

.feature-content p {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-secondary);
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.tech-item {
  padding: 20px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 8px;
}

.tech-item h4 {
  font-family: 'Orbitron', monospace;
  color: var(--neon-cyan);
  margin: 0 0 15px 0;
  font-size: 16px;
  text-align: center;
}

.tech-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tech-item li {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-primary);
  padding: 4px 0;
  font-size: 14px;
}

.legal-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.legal-item {
  padding: 15px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 8px;
}

.legal-item h4 {
  font-family: 'Orbitron', monospace;
  color: var(--neon-orange);
  margin: 0 0 10px 0;
  font-size: 14px;
}

.legal-item p {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-primary);
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
}

.version-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 6px;
}

.version-label {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-secondary);
  font-size: 14px;
}

.version-value {
  font-family: 'Orbitron', monospace;
  color: var(--text-primary);
  font-weight: bold;
  font-size: 14px;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.link-item {
  padding: 12px 16px;
  text-align: center;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-family: 'Share Tech Mono', monospace;
  font-size: 14px;
}

.link-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--glow-small) currentColor;
}

.disclaimer {
  border-color: var(--neon-orange);
  background: rgba(255, 165, 0, 0.05);
}

.disclaimer-text {
  font-family: 'Share Tech Mono', monospace;
  color: var(--neon-orange);
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  text-align: center;
}

/* Start Game Tab Layout */
.start-game-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 2px solid var(--neon-magenta);
  border-radius: 15px;
  box-shadow: var(--glow-medium) var(--neon-magenta);
}

.pit-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.5rem;
  font-weight: 900;
  margin: 0 0 20px 0;
  text-shadow: var(--glow-large) var(--neon-magenta);
  animation: title-pulse 2s ease-in-out infinite alternate;
}

@keyframes title-pulse {
  from {
    text-shadow: var(--glow-medium) var(--neon-magenta);
  }
  to {
    text-shadow: var(--glow-large) var(--neon-magenta), var(--glow-xl) var(--neon-magenta);
  }
}

.user-info-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 0, 128, 0.3);
  border-radius: 10px;
  backdrop-filter: blur(5px);
}

.username-btn {
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.username-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--glow-medium) var(--neon-green);
}

.balance-btn {
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  font-weight: 700;
  padding: 10px 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.balance-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--glow-medium) var(--neon-yellow);
}

/* Back Button in Left Sidebar */
.back-button.nav-tab {
  background: rgba(255, 102, 0, 0.1);
  border-color: var(--neon-orange);
  color: var(--neon-orange);
  margin-bottom: 15px;
  font-weight: bold;
}

.back-button.nav-tab:hover {
  background: rgba(255, 102, 0, 0.3);
  box-shadow: var(--glow-small) var(--neon-orange);
  transform: translateX(-5px);
}

/* Crypto Manager Modal Styles */
.crypto-manager-modal {
  border-color: var(--neon-yellow);
  box-shadow: var(--glow-large) var(--neon-yellow);
  max-width: 900px;
}

.crypto-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.crypto-tab {
  background: rgba(255, 255, 0, 0.1);
  border: 1px solid var(--neon-yellow);
  color: var(--neon-yellow);
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Share Tech Mono', monospace;
  font-size: 14px;
  font-weight: 600;
}

.crypto-tab:hover, .crypto-tab.active {
  background: rgba(255, 255, 0, 0.3);
  box-shadow: var(--glow-small) var(--neon-yellow);
  transform: translateY(-2px);
}

.crypto-content {
  animation: fade-in 0.3s ease-in;
}

.balance-display {
  text-align: center;
  padding: 30px;
  background: rgba(0, 0, 0, 0.4);
  border: 2px solid var(--neon-yellow);
  border-radius: 15px;
  margin-bottom: 30px;
}

.balance-title {
  font-family: 'Orbitron', monospace;
  font-size: 18px;
  margin: 0 0 15px 0;
}

.balance-amount {
  font-family: 'Orbitron', monospace;
  font-size: 36px;
  font-weight: 900;
  margin: 0 0 10px 0;
  text-shadow: var(--glow-text) var(--neon-yellow);
  background: rgba(0, 0, 0, 0.7);
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 0, 0.3);
}

.balance-usd {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-secondary);
  font-size: 16px;
}

.quick-actions, .account-info {
  margin-bottom: 30px;
}

.section-subtitle {
  font-family: 'Orbitron', monospace;
  font-size: 18px;
  margin: 0 0 20px 0;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.action-btn {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 0, 0.3);
  border-radius: 8px;
}

.info-label {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-secondary);
  font-size: 14px;
}

.info-value {
  font-family: 'Orbitron', monospace;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
}

.section-title {
  font-family: 'Orbitron', monospace;
  font-size: 24px;
  margin: 0 0 25px 0;
  text-align: center;
}

.deposit-form, .withdraw-form {
  max-width: 500px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-weight: 600;
}

.crypto-input {
  width: 100%;
  background: rgba(0, 0, 0, 0.6);
  border: 2px solid var(--neon-yellow);
  border-radius: 8px;
  padding: 12px 16px;
  color: var(--text-primary);
  font-family: 'Share Tech Mono', monospace;
  font-size: 16px;
}

.crypto-input:focus {
  outline: none;
  box-shadow: var(--glow-small) var(--neon-yellow);
}

.deposit-info, .withdraw-info {
  margin: 25px 0;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 0, 0.3);
  border-radius: 8px;
}

.info-title {
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  margin: 0 0 15px 0;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-list li {
  font-family: 'Share Tech Mono', monospace;
  color: var(--text-secondary);
  padding: 4px 0;
  font-size: 14px;
}

.crypto-action-btn {
  width: 100%;
  padding: 15px;
  font-size: 18px;
  font-weight: 700;
  margin-top: 20px;
}

.transaction-list {
  max-height: 400px;
  overflow-y: auto;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 0, 0.3);
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.transaction-item:hover {
  border-color: var(--neon-yellow);
  box-shadow: var(--glow-small) var(--neon-yellow);
}

.transaction-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.transaction-type {
  font-family: 'Share Tech Mono', monospace;
  font-size: 14px;
  color: var(--text-secondary);
}

.transaction-amount {
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  font-weight: 700;
}

.transaction-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: right;
}

.transaction-date {
  font-family: 'Share Tech Mono', monospace;
  font-size: 12px;
  color: var(--text-secondary);
}

.transaction-status {
  font-family: 'Share Tech Mono', monospace;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 600;
}

.transaction-status.completed {
  background: rgba(0, 255, 0, 0.2);
  color: var(--neon-green);
  border: 1px solid var(--neon-green);
}

.history-footer {
  text-align: center;
  margin-top: 20px;
}

.load-more-btn {
  padding: 10px 20px;
  font-size: 14px;
}

.crypto-message {
  padding: 15px 20px;
  border-radius: 8px;
  margin-top: 20px;
  font-family: 'Share Tech Mono', monospace;
  text-align: center;
  font-weight: 600;
}

.crypto-message.success {
  background: rgba(0, 255, 0, 0.1);
  border: 1px solid var(--neon-green);
  color: var(--neon-green);
}

.crypto-message.error {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid var(--neon-orange);
  color: var(--neon-orange);
}

.mode-card.combat-mode .mode-image {
  width: 320px;
  max-width: 320px;
  min-width: 320px;
  border-color: var(--neon-pink);
  box-shadow: 0 4px 15px rgba(255, 20, 147, 0.3);
}

.mode-card, .mode-image-container, .mode-image {
  box-sizing: border-box;
}

.mode-image {
  object-fit: contain;
  background: #000;
}

/* ===== ENHANCED READABILITY IMPROVEMENTS ===== */

/* Improved text contrast for better readability */
.game-ui-text {
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-shadow: none;
}

/* Reduced glow for UI panels */
.ui-panel {
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: var(--glow-subtle) rgba(255, 255, 255, 0.1);
}

/* Better contrast for important text */
.important-text {
  color: var(--text-primary);
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  background: rgba(0, 0, 0, 0.6);
  padding: 2px 6px;
  border-radius: 3px;
}

/* Subtle animation for less eye strain */
@keyframes subtle-pulse {
  0%, 100% { opacity: 0.9; }
  50% { opacity: 1; }
}

.subtle-pulse {
  animation: subtle-pulse 3s ease-in-out infinite;
}

/* Override for overly bright elements */
.tone-down-glow {
  text-shadow: var(--glow-subtle) currentColor !important;
  box-shadow: var(--glow-small) currentColor !important;
}

/* Improved readability for modal content */
.modal-content p,
.modal-content li,
.modal-content span {
  line-height: 1.6;
  color: var(--text-secondary);
}

/* Better contrast for form elements */
.form-element {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 4px;
}

.form-element:focus {
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
  outline: none;
}

/* Balance Section Refresh Button Styles */
.refresh-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
}

.refresh-btn:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-btn.sol-refresh:hover {
  color: #9945FF;
}

.refresh-btn.usd-refresh:hover {
  color: #22c55e;
}

.refresh-btn svg.spinning {
  animation: spin 1s linear infinite;
}

/* Modal Refresh Button Styles */
.refresh-btn-modal {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.refresh-btn-modal:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.refresh-btn-modal:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.refresh-btn-modal.sol-refresh-modal:hover {
  color: #9945FF;
}

.refresh-btn-modal.usd-refresh-modal:hover {
  color: #22c55e;
}

.refresh-btn-modal svg.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}