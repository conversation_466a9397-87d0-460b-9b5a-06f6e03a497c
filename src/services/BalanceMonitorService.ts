import { getWalletBalance } from '../utils/SolanaWallet'
import { supabase } from '../lib/supabase'

interface BalanceChangeEvent {
  userId: string
  publicKey: string
  oldBalance: number
  newBalance: number
  timestamp: Date
  difference: number
}

type BalanceChangeCallback = (event: BalanceChangeEvent) => void

class BalanceMonitorService {
  private static instance: BalanceMonitorService
  private callbacks: Set<BalanceChangeCallback> = new Set()
  private monitoredWallets: Map<string, { publicKey: string; lastBalance: number }> = new Map()
  private intervalId: NodeJS.Timeout | null = null
  private isRunning = false
  private checkInterval = 15000 // 15 seconds

  private constructor() {}

  static getInstance(): BalanceMonitorService {
    if (!BalanceMonitorService.instance) {
      BalanceMonitorService.instance = new BalanceMonitorService()
    }
    return BalanceMonitorService.instance
  }

  /**
   * Add a wallet to monitor for balance changes
   */
  async addWallet(userId: string): Promise<boolean> {
    try {
      // Get wallet public key from database
      const { data: walletData, error } = await supabase
        .from('solana_wallets')
        .select('public_key')
        .eq('user_id', userId)
        .single()

      if (error || !walletData) {
        console.error('Failed to get wallet for user:', userId, error)
        return false
      }

      const publicKey = walletData.public_key
      
      // Get initial balance
      const initialBalance = await getWalletBalance(publicKey)
      
      // Add to monitoring
      this.monitoredWallets.set(userId, {
        publicKey,
        lastBalance: initialBalance
      })

      console.log(`Added wallet ${publicKey} to monitoring for user ${userId}`)
      
      // Start monitoring if not already running
      if (!this.isRunning) {
        this.startMonitoring()
      }

      return true
    } catch (error) {
      console.error('Error adding wallet to monitor:', error)
      return false
    }
  }

  /**
   * Remove a wallet from monitoring
   */
  removeWallet(userId: string): void {
    this.monitoredWallets.delete(userId)
    console.log(`Removed wallet monitoring for user ${userId}`)
    
    // Stop monitoring if no wallets left
    if (this.monitoredWallets.size === 0) {
      this.stopMonitoring()
    }
  }

  /**
   * Subscribe to balance change events
   */
  onBalanceChange(callback: BalanceChangeCallback): () => void {
    this.callbacks.add(callback)
    
    // Return unsubscribe function
    return () => {
      this.callbacks.delete(callback)
    }
  }

  /**
   * Start monitoring all added wallets
   */
  private startMonitoring(): void {
    if (this.isRunning) return

    this.isRunning = true
    console.log('Starting balance monitoring service')
    
    this.intervalId = setInterval(() => {
      this.checkAllBalances()
    }, this.checkInterval)
  }

  /**
   * Stop monitoring
   */
  private stopMonitoring(): void {
    if (!this.isRunning) return

    this.isRunning = false
    console.log('Stopping balance monitoring service')
    
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }

  /**
   * Check balances for all monitored wallets
   */
  private async checkAllBalances(): Promise<void> {
    const promises = Array.from(this.monitoredWallets.entries()).map(
      ([userId, walletInfo]) => this.checkWalletBalance(userId, walletInfo)
    )

    await Promise.allSettled(promises)
  }

  /**
   * Check balance for a specific wallet
   */
  private async checkWalletBalance(
    userId: string,
    walletInfo: { publicKey: string; lastBalance: number }
  ): Promise<void> {
    try {
      const currentBalance = await getWalletBalance(walletInfo.publicKey)
      const difference = currentBalance - walletInfo.lastBalance
      
      // Only notify if there's a significant change (more than 0.000001 SOL)
      if (Math.abs(difference) > 0.000001) {
        const event: BalanceChangeEvent = {
          userId,
          publicKey: walletInfo.publicKey,
          oldBalance: walletInfo.lastBalance,
          newBalance: currentBalance,
          timestamp: new Date(),
          difference
        }

        // Update stored balance
        walletInfo.lastBalance = currentBalance
        
        // Update database
        await this.updateDatabaseBalance(userId, currentBalance)
        
        // Notify all callbacks
        this.callbacks.forEach(callback => {
          try {
            callback(event)
          } catch (error) {
            console.error('Error in balance change callback:', error)
          }
        })

        console.log(`Balance change detected for user ${userId}: ${difference > 0 ? '+' : ''}${difference.toFixed(6)} SOL`)
      }
    } catch (error) {
      console.error(`Error checking balance for user ${userId}:`, error)
    }
  }

  /**
   * Update balance in database
   */
  private async updateDatabaseBalance(userId: string, balance: number): Promise<void> {
    try {
      await supabase
        .from('user_profiles')
        .update({ solana_balance: balance })
        .eq('id', userId)
    } catch (error) {
      console.error('Error updating database balance:', error)
    }
  }

  /**
   * Get current monitoring status
   */
  getStatus(): {
    isRunning: boolean
    monitoredWallets: number
    callbacks: number
  } {
    return {
      isRunning: this.isRunning,
      monitoredWallets: this.monitoredWallets.size,
      callbacks: this.callbacks.size
    }
  }

  /**
   * Force check all balances immediately
   */
  async forceCheck(): Promise<void> {
    await this.checkAllBalances()
  }

  /**
   * Set monitoring interval
   */
  setCheckInterval(intervalMs: number): void {
    this.checkInterval = intervalMs
    
    // Restart monitoring with new interval if currently running
    if (this.isRunning) {
      this.stopMonitoring()
      this.startMonitoring()
    }
  }

  /**
   * Clean up and stop all monitoring
   */
  destroy(): void {
    this.stopMonitoring()
    this.monitoredWallets.clear()
    this.callbacks.clear()
  }
}

export default BalanceMonitorService
export type { BalanceChangeEvent, BalanceChangeCallback }