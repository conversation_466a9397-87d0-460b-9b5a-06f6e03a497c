import { createClient } from '@supabase/supabase-js';
import SolanaPriceService from './SolanaPriceService';
import solanaClientService from './SolanaClientService';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL!;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Service for automatically updating user balance information in Supabase
 * Handles both SOL balance from blockchain and USD conversion using Pyth prices
 */
class BalanceUpdateService {
  private static instance: BalanceUpdateService;
  private updateInterval: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL = 60000; // 1 minute
  private isRunning = false;
  private priceService = SolanaPriceService.getInstance();

  private constructor() {}

  public static getInstance(): BalanceUpdateService {
    if (!BalanceUpdateService.instance) {
      BalanceUpdateService.instance = new BalanceUpdateService();
    }
    return BalanceUpdateService.instance;
  }

  /**
   * Start automatic balance updates for a user
   */
  public startAutoUpdate(userId: string): void {
    if (this.isRunning) {
      this.stopAutoUpdate();
    }

    console.log(`🔄 Starting automatic balance updates for user ${userId}`);
    this.isRunning = true;

    // Initial update
    this.updateUserBalance(userId);

    // Set up interval
    this.updateInterval = setInterval(() => {
      this.updateUserBalance(userId);
    }, this.UPDATE_INTERVAL);
  }

  /**
   * Stop automatic balance updates
   */
  public stopAutoUpdate(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.isRunning = false;
    console.log('⏹️ Stopped automatic balance updates');
  }

  /**
   * Update user balance in Supabase with latest blockchain data and USD conversion
   */
  public async updateUserBalance(userId: string): Promise<void> {
    try {
      // Get current SOL balance from blockchain
      const balanceResult = await solanaClientService.getUserBalance(userId);
      
      if (!balanceResult.success || balanceResult.balance === undefined) {
        console.warn(`⚠️ Failed to fetch balance for user ${userId}:`, balanceResult.error);
        return;
      }

      const solBalance = balanceResult.balance;

      // Get current SOL price from Pyth
      const solPrice = await this.priceService.getSolanaPrice();
      const usdBalance = solPrice ? solBalance * solPrice : null;

      // Update user profile in Supabase
      const updateData: any = {
        solana_balance: solBalance,
        balance_last_updated: new Date().toISOString()
      };

      if (usdBalance !== null) {
        updateData.usd_balance = usdBalance;
        updateData.sol_price_at_update = solPrice;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) {
        console.error('❌ Error updating user balance in Supabase:', error);
        return;
      }

      console.log(`✅ Updated balance for user ${userId}: ${solBalance.toFixed(6)} SOL${usdBalance ? ` ($${usdBalance.toFixed(2)})` : ''}`);

    } catch (error) {
      console.error('❌ Error in updateUserBalance:', error);
    }
  }

  /**
   * Force update balance for a specific user (manual refresh)
   */
  public async forceUpdateBalance(userId: string): Promise<{
    success: boolean;
    solBalance?: number;
    usdBalance?: number;
    error?: string;
  }> {
    try {
      // Get current SOL balance from blockchain
      const balanceResult = await solanaClientService.getUserBalance(userId);
      
      if (!balanceResult.success || balanceResult.balance === undefined) {
        return {
          success: false,
          error: balanceResult.error || 'Failed to fetch balance'
        };
      }

      const solBalance = balanceResult.balance;

      // Get current SOL price from Pyth
      const solPrice = await this.priceService.getSolanaPrice();
      const usdBalance = solPrice ? solBalance * solPrice : null;

      // Update user profile in Supabase
      const updateData: any = {
        solana_balance: solBalance,
        balance_last_updated: new Date().toISOString()
      };

      if (usdBalance !== null) {
        updateData.usd_balance = usdBalance;
        updateData.sol_price_at_update = solPrice;
      }

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId);

      if (error) {
        console.error('❌ Error updating user balance in Supabase:', error);
        return {
          success: false,
          error: 'Failed to update balance in database'
        };
      }

      console.log(`✅ Force updated balance for user ${userId}: ${solBalance.toFixed(6)} SOL${usdBalance ? ` ($${usdBalance.toFixed(2)})` : ''}`);

      return {
        success: true,
        solBalance,
        usdBalance: usdBalance || undefined
      };

    } catch (error) {
      console.error('❌ Error in forceUpdateBalance:', error);
      return {
        success: false,
        error: 'Unexpected error occurred'
      };
    }
  }

  /**
   * Update only the USD balance using current SOL balance and latest price
   */
  public async updateUsdBalance(userId: string, currentSolBalance: number): Promise<{
    success: boolean;
    usdBalance?: number;
    solPrice?: number;
    error?: string;
  }> {
    try {
      // Get current SOL price from Pyth
      const solPrice = await this.priceService.getSolanaPrice();
      
      if (!solPrice) {
        return {
          success: false,
          error: 'Failed to fetch SOL price'
        };
      }

      const usdBalance = currentSolBalance * solPrice;

      // Update user profile in Supabase
      const { error } = await supabase
        .from('profiles')
        .update({
          usd_balance: usdBalance,
          sol_price_at_update: solPrice,
          balance_last_updated: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) {
        console.error('❌ Error updating USD balance in Supabase:', error);
        return {
          success: false,
          error: 'Failed to update USD balance in database'
        };
      }

      console.log(`✅ Updated USD balance for user ${userId}: $${usdBalance.toFixed(2)} (${solPrice.toFixed(2)} SOL/USD)`);

      return {
        success: true,
        usdBalance,
        solPrice
      };

    } catch (error) {
      console.error('❌ Error in updateUsdBalance:', error);
      return {
        success: false,
        error: 'Unexpected error occurred'
      };
    }
  }

  /**
   * Check if auto-update is currently running
   */
  public isAutoUpdateRunning(): boolean {
    return this.isRunning;
  }

  /**
   * Get the current update interval in milliseconds
   */
  public getUpdateInterval(): number {
    return this.UPDATE_INTERVAL;
  }
}

export default BalanceUpdateService;
