// No longer need Solana web3 or Pyth client imports for Hermes

interface SolanaPriceResponse {
  slug: string;
  prices: number[];
  token_id: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number | null;
  btc_price: number;
  btc_marketcap: number;
  btc_volume_24h: number;
  btc_price_change_24h: number;
  usd_price: number;
  usd_marketcap: number;
  usd_volume_24h: number;
  usd_price_change_24h: number;
  usd_price_change_24h_abs: number;
  token_dominance_rate: number | null;
  rank: number;
  app_tradable: boolean;
  exchange_tradable: boolean;
  defi_tradable: boolean;
  update_time: number;
  price_update_time: number;
  app_symbol: string;
}

class SolanaPriceService {
  private static instance: SolanaPriceService;
  private cachedPrice: number | null = null;
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 30000; // 30 seconds cache for Pyth (more frequent updates)

  // Pyth Hermes configuration
  private readonly SOL_USD_PRICE_FEED_ID = '0xef0d8b6fda2ceba41da15d4095d1da392a0d2f8ed0c6c7bc0f4cfac8c280b56d'; // SOL/USD price feed
  private readonly HERMES_BASE_URL = 'https://hermes.pyth.network';

  // Fallback to Crypto.com API if Pyth fails
  private readonly FALLBACK_API_URL = 'https://price-api.crypto.com/price/v1/token-price/solana';

  private constructor() {
    console.log(`🐍 Pyth Hermes client initialized with SOL/USD feed: ${this.SOL_USD_PRICE_FEED_ID}`);
  }

  public static getInstance(): SolanaPriceService {
    if (!SolanaPriceService.instance) {
      SolanaPriceService.instance = new SolanaPriceService();
    }
    return SolanaPriceService.instance;
  }

  public async getSolanaPrice(): Promise<number | null> {
    const now = Date.now();

    // Return cached price if still valid
    if (this.cachedPrice !== null && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.cachedPrice;
    }

    try {
      // Try Pyth Hermes first
      const pythPrice = await this.getPriceFromHermes();
      if (pythPrice !== null) {
        this.cachedPrice = pythPrice;
        this.lastFetchTime = now;
        console.log(`📊 SOL price from Pyth Hermes: $${pythPrice.toFixed(2)}`);
        return pythPrice;
      }
    } catch (error) {
      console.warn('⚠️ Pyth Hermes price fetch failed, falling back to Crypto.com:', error);
    }

    try {
      // Fallback to Crypto.com API
      const fallbackPrice = await this.getPriceFromCryptoCom();
      if (fallbackPrice !== null) {
        this.cachedPrice = fallbackPrice;
        this.lastFetchTime = now;
        console.log(`📊 SOL price from Crypto.com (fallback): $${fallbackPrice.toFixed(2)}`);
        return fallbackPrice;
      }
    } catch (error) {
      console.error('❌ Both Pyth Hermes and Crypto.com price fetch failed:', error);
    }

    // Return cached price if available, null otherwise
    return this.cachedPrice;
  }

  private async getPriceFromHermes(): Promise<number | null> {
    try {
      // Fetch latest price data from Hermes
      const url = `${this.HERMES_BASE_URL}/v2/updates/price/latest?ids[]=${this.SOL_USD_PRICE_FEED_ID}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'SnakePit-Game/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`Hermes HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.parsed || data.parsed.length === 0) {
        throw new Error('No price data returned from Hermes');
      }

      const priceData = data.parsed[0];
      if (!priceData.price) {
        throw new Error('Invalid price data structure from Hermes');
      }

      // Pyth prices come with confidence intervals and exponents
      const price = parseInt(priceData.price.price);
      const expo = priceData.price.expo;
      const confidence = parseInt(priceData.price.conf);

      // Calculate the actual price: price * 10^expo
      const actualPrice = price * Math.pow(10, expo);

      // Validate price is reasonable (between $1 and $10,000)
      if (actualPrice < 1 || actualPrice > 10000) {
        throw new Error(`Unreasonable SOL price from Hermes: $${actualPrice}`);
      }

      // Log confidence for monitoring
      const confidenceValue = confidence * Math.pow(10, expo);
      console.log(`🎯 Hermes SOL/USD: $${actualPrice.toFixed(2)} ±$${confidenceValue.toFixed(2)} (${new Date(priceData.price.publish_time * 1000).toISOString()})`);

      return actualPrice;
    } catch (error) {
      console.error('Error fetching price from Hermes:', error);
      throw error;
    }
  }

  private async getPriceFromCryptoCom(): Promise<number | null> {
    try {
      const response = await fetch(this.FALLBACK_API_URL, {
        method: 'GET',
        headers: {
          'accept': '*/*',
          'accept-language': 'en-US,en;q=0.9',
          'origin': 'https://crypto.com',
          'referer': 'https://crypto.com/',
          'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
          'sec-ch-ua-mobile': '?0',
          'sec-ch-ua-platform': '"macOS"',
          'sec-fetch-dest': 'empty',
          'sec-fetch-mode': 'cors',
          'sec-fetch-site': 'same-site',
          'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
          'x-client': 'PriceWebPages'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: SolanaPriceResponse = await response.json();
      const price = data.usd_price;

      if (isNaN(price) || price <= 0) {
        throw new Error('Invalid price data received from Crypto.com');
      }

      return price;
    } catch (error) {
      console.error('Error fetching price from Crypto.com:', error);
      throw error;
    }
  }

  public convertSolToUsd(solAmount: number, solPrice: number): number {
    return solAmount * solPrice;
  }

  public convertUsdToSol(usdAmount: number, solPrice: number): number {
    return usdAmount / solPrice;
  }

  public formatUsdAmount(usdAmount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(usdAmount);
  }

  public formatSolAmount(solAmount: number): string {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(solAmount) + ' SOL';
  }

  public clearCache(): void {
    this.cachedPrice = null;
    this.lastFetchTime = 0;
  }
}

export default SolanaPriceService;