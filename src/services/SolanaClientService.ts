interface SolanaResponse {
  success: boolean;
  error?: string;
  message?: string;
}

interface WalletCreationResponse extends SolanaResponse {
  publicKey?: string;
}

interface BalanceResponse extends SolanaResponse {
  balance?: number;
  publicKey?: string;
}

interface TransactionResponse extends SolanaResponse {
  signature?: string;
  transactionId?: string;
  newBalance?: number;
  amount?: number;
}

interface ValidationResponse extends SolanaResponse {
  canJoin?: boolean;
  balance?: number;
}

class SolanaClientService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.REACT_APP_SERVER_URL || 'http://localhost:3005';
  }

  /**
   * Create a wallet for a user
   */
  async createUserWallet(userId: string): Promise<WalletCreationResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/solana/create-user-wallet`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      return await response.json();
    } catch (error) {
      console.error('Error creating user wallet:', error);
      return {
        success: false,
        error: 'Failed to create wallet'
      };
    }
  }

  /**
   * Get user wallet balance
   */
  async getUserBalance(userId: string): Promise<BalanceResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/solana/balance/${userId}`);
      return await response.json();
    } catch (error) {
      console.error('Error getting user balance:', error);
      return {
        success: false,
        error: 'Failed to get balance'
      };
    }
  }

  /**
   * Validate if user can join a game
   */
  async validateGameEntry(userId: string, wagerAmount: number): Promise<ValidationResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/solana/validate-game-entry`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, wagerAmount }),
      });

      return await response.json();
    } catch (error) {
      console.error('Error validating game entry:', error);
      return {
        success: false,
        canJoin: false,
        error: 'Failed to validate game entry'
      };
    }
  }

  /**
   * Process a shop purchase
   */
  async processShopPurchase(userId: string, itemPrice: number, itemName: string): Promise<TransactionResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/solana/shop-purchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, itemPrice, itemName }),
      });

      return await response.json();
    } catch (error) {
      console.error('Error processing shop purchase:', error);
      return {
        success: false,
        error: 'Failed to process purchase'
      };
    }
  }

  /**
   * Transfer SOL to external wallet
   */
  async transferToExternalWallet(userId: string, externalWalletAddress: string, amount: number): Promise<TransactionResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/api/solana/transfer-external`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, externalWalletAddress, amount }),
      });

      return await response.json();
    } catch (error) {
      console.error('Error transferring to external wallet:', error);
      return {
        success: false,
        error: 'Failed to transfer to external wallet'
      };
    }
  }

  /**
   * Get Solana service health status
   */
  async getHealthStatus(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/solana/health`);
      return await response.json();
    } catch (error) {
      console.error('Error getting health status:', error);
      return {
        success: false,
        error: 'Failed to get health status'
      };
    }
  }

  /**
   * Convert USD to SOL using current price
   */
  async convertUsdToSol(usdAmount: number): Promise<number> {
    try {
      // This is a simple conversion - you might want to get real-time prices
      // For now, using a fallback conversion rate
      const response = await fetch('https://price-api.crypto.com/price/v1/token-price/solana');
      const data = await response.json();
      const solPrice = data.usd_price || 200; // Fallback price
      return usdAmount / solPrice;
    } catch (error) {
      console.error('Error converting USD to SOL:', error);
      // Fallback conversion rate
      return usdAmount / 200;
    }
  }

  /**
   * Convert SOL to USD using current price
   */
  async convertSolToUsd(solAmount: number): Promise<number> {
    try {
      const response = await fetch('https://price-api.crypto.com/price/v1/token-price/solana');
      const data = await response.json();
      const solPrice = data.usd_price || 200; // Fallback price
      return solAmount * solPrice;
    } catch (error) {
      console.error('Error converting SOL to USD:', error);
      // Fallback conversion rate
      return solAmount * 200;
    }
  }

  /**
   * Format SOL amount for display
   */
  formatSolAmount(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount);
  }

  /**
   * Format USD amount for display
   */
  formatUsdAmount(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  }

  /**
   * Validate Solana public key format
   */
  isValidSolanaAddress(address: string): boolean {
    try {
      // Basic validation - Solana addresses are base58 encoded and 32-44 characters
      const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
      return base58Regex.test(address);
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const solanaClientService = new SolanaClientService();
export default solanaClientService;
