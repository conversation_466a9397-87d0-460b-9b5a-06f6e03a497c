/* SnakePit - Modern Apple x Black Mamba Theme */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=SF+Mono:wght@400;500;600&display=swap');

:root {
  /* Black Mamba Color Palette - Sophisticated blacks and grays */
  --mamba-black: #000000;
  --mamba-charcoal: #1c1c1e;
  --mamba-dark-gray: #2c2c2e;
  --mamba-medium-gray: #3a3a3c;
  --mamba-light-gray: #48484a;
  --mamba-silver: #8e8e93;
  --mamba-platinum: #c7c7cc;
  --mamba-white: #ffffff;

  /* Apple-inspired accent colors */
  --apple-blue: #007aff;
  --apple-green: #34c759;
  --apple-orange: #ff9500;
  --apple-red: #ff3b30;
  --apple-purple: #af52de;
  --apple-pink: #ff2d92;
  --apple-yellow: #ffcc00;

  /* Primary theme colors */
  --bg-primary: var(--mamba-black);
  --bg-secondary: var(--mamba-charcoal);
  --bg-tertiary: var(--mamba-dark-gray);
  --bg-quaternary: var(--mamba-medium-gray);
  --text-primary: var(--mamba-white);
  --text-secondary: var(--mamba-platinum);
  --text-tertiary: var(--mamba-silver);
  --accent-primary: var(--apple-blue);
  --accent-secondary: var(--apple-green);

  /* Modern shadow effects */
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-medium: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  --shadow-large: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  --shadow-xl: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 400;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, var(--mamba-black) 0%, var(--mamba-charcoal) 100%);
}

/* Prevent scrolling only when game is active */
body.game-active {
  overflow: hidden;
}

/* Modern smooth rendering */
.smooth-render {
  image-rendering: auto;
  image-rendering: smooth;
  image-rendering: high-quality;
}

/* Modern text styles */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.accent-primary { color: var(--accent-primary); }
.accent-secondary { color: var(--accent-secondary); }

/* Typography scale */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

/* Main App Container */
.snakepit-app {
  min-height: 100vh;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* Start Screen Background */
.snakepit-app.start-screen-bg {
  background-image: url('../public/assets/backgrounds/StartScreen.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Apple-Style Start Screen */
.apple-start-screen {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.apple-start-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    rgba(0, 0, 0, 0.3),
    radial-gradient(circle at 20% 30%, rgba(0, 122, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(52, 199, 89, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(255, 149, 0, 0.04) 0%, transparent 50%),
    radial-gradient(circle at 90% 20%, rgba(175, 82, 222, 0.03) 0%, transparent 50%);
  animation: liquid-flow 20s ease-in-out infinite;
  z-index: 1;
}

.apple-start-screen::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.01) 50%, transparent 70%);
  background-size: 200px 200px, 150px 150px;
  animation: glass-shimmer 15s linear infinite;
  z-index: 2;
}

@keyframes liquid-flow {
  0%, 100% {
    background:
      radial-gradient(circle at 20% 30%, rgba(0, 122, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(52, 199, 89, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(255, 149, 0, 0.06) 0%, transparent 50%),
      radial-gradient(circle at 90% 20%, rgba(175, 82, 222, 0.05) 0%, transparent 50%);
  }
  25% {
    background:
      radial-gradient(circle at 80% 20%, rgba(0, 122, 255, 0.12) 0%, transparent 50%),
      radial-gradient(circle at 30% 80%, rgba(52, 199, 89, 0.09) 0%, transparent 50%),
      radial-gradient(circle at 70% 30%, rgba(255, 149, 0, 0.07) 0%, transparent 50%),
      radial-gradient(circle at 10% 60%, rgba(175, 82, 222, 0.06) 0%, transparent 50%);
  }
  50% {
    background:
      radial-gradient(circle at 60% 80%, rgba(0, 122, 255, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 20% 20%, rgba(52, 199, 89, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 90% 60%, rgba(255, 149, 0, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(175, 82, 222, 0.07) 0%, transparent 50%);
  }
  75% {
    background:
      radial-gradient(circle at 30% 60%, rgba(0, 122, 255, 0.09) 0%, transparent 50%),
      radial-gradient(circle at 70% 40%, rgba(52, 199, 89, 0.06) 0%, transparent 50%),
      radial-gradient(circle at 20% 90%, rgba(255, 149, 0, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 80% 10%, rgba(175, 82, 222, 0.04) 0%, transparent 50%);
  }
}

@keyframes glass-shimmer {
  0% {
    background-position: -200px -200px, -150px -150px;
  }
  100% {
    background-position: 200px 200px, 150px 150px;
  }
}

/* Modern Logo */
.ascii-logo {
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: left;
  margin: 0;
  line-height: 1.0;
  color: var(--text-primary);
  white-space: pre;
  overflow: visible;
  padding: 1.5rem 2rem;
  background: var(--bg-secondary);
  border-radius: 16px;
  border: 1px solid var(--bg-quaternary);
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ascii-logo:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-large);
  border-color: var(--accent-primary);
}

/* SnakePit Logo/Title - Modern minimalist */
.snakepit-title {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 4rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--text-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.snakepit-title:hover {
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: translateY(-2px);
}

.snakepit-subtitle {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.5rem;
  font-weight: 400;
  margin: 1rem 0 0 0;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* Subtle modern animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-subtle {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Game Mode Selection */
.game-mode-select {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
  padding: 3rem;
  animation: fade-in-up 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.mode-selection-title {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.mode-buttons {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.mode-button {
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 20px;
  padding: 2.5rem;
  min-width: 320px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-medium);
  text-align: center;
}

.mode-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 20px;
  z-index: -1;
}

.mode-button:hover::before {
  opacity: 0.1;
}

.mode-button:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-large);
  transform: translateY(-4px) scale(1.02);
}

.mode-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--accent-primary);
}

.mode-button h3 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
  letter-spacing: -0.01em;
}

.mode-button:hover h3 {
  color: var(--accent-primary);
}

.mode-button p {
  color: var(--text-secondary);
  margin: 0 0 1.5rem 0;
  line-height: 1.6;
  font-size: 1.1rem;
  font-weight: 400;
}

.mode-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  text-align: left;
}

.mode-features span {
  color: var(--accent-secondary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
}

.classic-mode:hover .mode-features span {
  color: var(--accent-primary);
}

.combat-mode {
  border-color: var(--bg-quaternary);
}

.combat-mode:hover {
  border-color: var(--accent-secondary);
}

.combat-mode h3 {
  color: var(--text-primary);
}

.combat-mode:hover h3 {
  color: var(--accent-secondary);
}

.combat-mode .mode-icon {
  color: var(--accent-secondary);
}

/* Game Container */
.game-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  position: relative;
}

/* Game UI - Modern glass morphism design */
.game-ui {
  position: absolute;
  top: 240px;
  right: 20px;
  width: 280px;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  background: rgba(28, 28, 30, 0.8);
  border: 1px solid var(--bg-quaternary);
  border-radius: 20px;
  box-shadow: var(--shadow-large);
  backdrop-filter: blur(20px);
  z-index: 10;
}

.ui-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-item {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.75rem 1rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 12px;
  text-align: center;
  width: 100%;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.stat-item:hover {
  background: var(--bg-quaternary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-subtle);
}

.stat-score { color: var(--apple-yellow); }
.stat-cash { color: var(--apple-green); }
.stat-length { color: var(--apple-blue); }
.stat-boost { color: var(--apple-orange); }
.stat-weapon { color: var(--apple-pink); }
.stat-cooldown { color: var(--apple-purple); }

/* Modern Button Styles */
.retro-button {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.75rem 1.5rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 12px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-subtle);
}

.retro-button:hover {
  background: var(--accent-primary);
  color: var(--mamba-white);
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
  border-color: var(--accent-primary);
}

.retro-button.secondary {
  border-color: var(--bg-quaternary);
  color: var(--text-secondary);
}

.retro-button.secondary:hover {
  background: var(--accent-secondary);
  color: var(--mamba-white);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-secondary);
}

/* Game Over Styles */
.game-over {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.game-over-content {
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 24px;
  padding: 3rem;
  text-align: center;
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(20px);
  animation: fade-in-up 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.game-over h2 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1rem 0;
  letter-spacing: -0.02em;
}

.final-stats {
  margin: 2rem 0;
}

.final-stat {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0.5rem 0;
  color: var(--text-secondary);
}

.game-over-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* Landing Container - Transparent Glass */
.landing-container {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 32px;
  padding: 3rem;
  max-width: 900px;
  width: 100%;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px) saturate(120%);
  position: relative;
  overflow: hidden;
  animation: fade-in-up 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 10;
}

.landing-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  border-radius: 31px;
  margin: 1px;
  opacity: 0.3;
  pointer-events: none;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.landing-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(0, 122, 255, 0.1) 60deg,
    rgba(52, 199, 89, 0.08) 120deg,
    rgba(255, 149, 0, 0.06) 180deg,
    rgba(175, 82, 222, 0.05) 240deg,
    rgba(255, 45, 146, 0.04) 300deg,
    transparent 360deg
  );
  animation: liquid-rotate 30s linear infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes glass-glow {
  0%, 100% {
    opacity: 0.6;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 25%,
      rgba(255, 255, 255, 0.02) 50%,
      rgba(255, 255, 255, 0.05) 75%,
      rgba(255, 255, 255, 0.1) 100%
    );
  }
  50% {
    opacity: 0.8;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.08) 25%,
      rgba(255, 255, 255, 0.04) 50%,
      rgba(255, 255, 255, 0.08) 75%,
      rgba(255, 255, 255, 0.15) 100%
    );
  }
}

@keyframes liquid-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.snakepit-logo {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

/* Logo Snakes Canvas */
.logo-snakes-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* Game Stats */
.game-stats {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
}

.stat-card {
  text-align: center;
  padding: 2rem 1.5rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 16px;
  box-shadow: var(--shadow-medium);
  min-width: 140px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-large);
  border-color: var(--accent-primary);
}

.stat-number {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0;
}

/* Audio Controls */
.audio-controls {
  position: fixed;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  pointer-events: auto;
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 12px;
  padding: 0.75rem;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-medium);
  z-index: 1001;
}

.audio-btn {
  background: transparent;
  border: 1px solid var(--bg-quaternary);
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
}

.audio-btn:hover {
  background: var(--bg-quaternary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
  transform: scale(1.05);
}

.volume-control {
  display: flex;
  align-items: center;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: var(--bg-quaternary);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--accent-primary);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--accent-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.volume-slider::-moz-range-thumb:hover {
  transform: scale(1.2);
}

/* User Info Bar */
.user-info-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 15px 20px;
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-medium);
}

.username-btn {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 16px;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  color: var(--text-primary);
}

.username-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}

.balance-btn {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 16px;
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  color: var(--text-primary);
}

.balance-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-secondary);
  color: var(--accent-secondary);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.modal-content {
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 24px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(20px);
  animation: fade-in-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--bg-quaternary);
}

.modal-title {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.modal-close {
  background: transparent;
  border: 1px solid var(--bg-quaternary);
  color: var(--text-secondary);
  width: 40px;
  height: 40px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--bg-quaternary);
  color: var(--accent-primary);
  border-color: var(--accent-primary);
}

/* Input Styles */
.modern-input {
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
}

.modern-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.modern-input::placeholder {
  color: var(--text-tertiary);
}

/* Checkbox Styles */
.modern-checkbox {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.modern-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.modern-checkbox .checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--bg-quaternary);
  border-radius: 6px;
  background: transparent;
  margin-right: 0.75rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-checkbox input:checked + .checkmark {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
}

.modern-checkbox input:checked + .checkmark::after {
  content: '✓';
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* Minimap */
.minimap-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 250px;
  height: 200px;
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 16px;
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(20px);
  z-index: 15;
}

.minimap-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
  border-radius: 18px;
  z-index: -1;
  opacity: 0.3;
}

/* Instructions */
.instructions {
  position: absolute;
  bottom: 20px;
  left: 0;
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 0 16px 0 0;
  border-bottom: none;
  border-left: none;
  padding: 0.75rem 1rem 1rem 1rem;
  max-width: 280px;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  backdrop-filter: blur(20px);
  z-index: 10;
}

.instructions h3 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text-primary);
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.instructions-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.instructions-toggle:hover {
  background: var(--bg-quaternary);
  color: var(--accent-primary);
}

/* Cashout Button */
.cashout-container {
  margin-top: 15px;
  padding: 10px;
  border: 1px solid var(--accent-secondary);
  border-radius: 12px;
  background: rgba(52, 199, 89, 0.05);
  text-align: center;
}

.cashout-button {
  background: transparent;
  border: 2px solid var(--accent-secondary);
  color: var(--accent-secondary);
  padding: 8px 16px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: pulse-subtle 2s infinite;
}

.cashout-button:hover {
  background: var(--accent-secondary);
  color: var(--mamba-white);
  transform: scale(1.05);
  animation: none;
}

.cashout-button:disabled {
  border-color: var(--text-tertiary);
  color: var(--text-tertiary);
  cursor: not-allowed;
  animation: none;
}

.cashout-button:disabled:hover {
  background: transparent;
  color: var(--text-tertiary);
  transform: none;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }

.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.rounded { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-90 { opacity: 0.9; }

/* Responsive Design */
@media (max-width: 768px) {
  .snakepit-title {
    font-size: 3rem;
  }

  .snakepit-subtitle {
    font-size: 1.2rem;
  }

  .mode-button {
    min-width: 280px;
    padding: 2rem 1.5rem;
  }

  .mode-buttons {
    flex-direction: column;
    align-items: center;
  }

  .game-stats {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .stat-card {
    min-width: 120px;
  }

  .game-ui {
    right: 10px;
    width: 240px;
    padding: 1rem;
  }

  .minimap-container {
    width: 200px;
    height: 160px;
    right: 10px;
  }

  .landing-container {
    margin: 1rem;
    padding: 2rem 1.5rem;
    border-radius: 20px;
    max-width: calc(100% - 2rem);
  }
}

@media (max-width: 480px) {
  .snakepit-title {
    font-size: 2.5rem;
  }

  .mode-button {
    min-width: 260px;
    padding: 1.5rem;
  }

  .game-ui {
    right: 5px;
    width: 200px;
    padding: 0.75rem;
  }

  .minimap-container {
    width: 160px;
    height: 120px;
    right: 5px;
  }

  .audio-controls {
    top: 10px;
    left: 10px;
    padding: 0.5rem;
  }

  .landing-container {
    padding: 1.5rem 1rem;
  }
}

/* Apple-Style Auth Modal */
.auth-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fade-in 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.auth-modal {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 28px;
  padding: 2.5rem;
  max-width: 420px;
  width: 90%;
  box-shadow:
    0 16px 64px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(40px) saturate(180%);
  animation: modal-slide-up 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.auth-modal-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.auth-modal-header h2 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  letter-spacing: -0.02em;
}

.close-button {
  position: absolute;
  top: -1rem;
  right: -1rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  color: var(--text-secondary);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.close-button:hover {
  background: var(--bg-quaternary);
  color: var(--text-primary);
  transform: scale(1.05);
}

.auth-intro {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-intro p {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1rem;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Apple-Style Form Elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  display: block;
  margin-bottom: 0.5rem;
  letter-spacing: -0.01em;
}

.apple-input {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  width: 100%;
  padding: 0.875rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  backdrop-filter: blur(20px);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

.apple-input:focus {
  border-color: rgba(0, 122, 255, 0.4);
  box-shadow:
    0 0 0 3px rgba(0, 122, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.08);
}

.apple-input::placeholder {
  color: var(--text-tertiary);
}

.apple-button {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, var(--accent-primary) 0%, #0056b3 100%);
  border: none;
  border-radius: 16px;
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.01em;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow:
    0 4px 16px rgba(0, 122, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
}

.apple-button:hover {
  background: linear-gradient(135deg, #0056b3 0%, var(--accent-primary) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 122, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.apple-button:active {
  transform: translateY(-1px);
}

.apple-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-tertiary);
  cursor: not-allowed;
  transform: none;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

.apple-button-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--bg-quaternary);
}

.apple-button-secondary:hover {
  background: var(--bg-quaternary);
  border-color: var(--accent-primary);
}

.error-message {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.875rem;
  color: var(--apple-red);
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.auth-toggle {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--bg-quaternary);
}

.auth-toggle p {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0 0 0.5rem 0;
}

.toggle-button {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  color: var(--accent-primary);
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
}

.toggle-button:hover {
  color: #0056b3;
  text-decoration: underline;
}

/* Animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modal-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Apple-Style Start Screen */
.landing-content {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  text-align: center;
}

.top-user-status {
  position: fixed;
  top: 2rem;
  right: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  z-index: 100;
}

.user-status {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 16px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px) saturate(180%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.user-status:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(0, 122, 255, 0.3);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.login-button {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, var(--accent-primary) 0%, #0056b3 100%);
  border: none;
  border-radius: 16px;
  color: white;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow:
    0 4px 16px rgba(0, 122, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.login-button:hover {
  background: linear-gradient(135deg, #0056b3 0%, var(--accent-primary) 100%);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(0, 122, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.audio-btn {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 16px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px) saturate(180%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.audio-btn:hover {
  background: rgba(255, 255, 255, 0.12);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.volume-control {
  display: flex;
  align-items: center;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: var(--bg-quaternary);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--accent-primary);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.volume-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.hero-content {
  max-width: 600px;
  margin: 0 auto;
}

.hero-title {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 3.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  letter-spacing: -0.03em;
  line-height: 1.1;
}

.hero-description {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin: 0 0 3rem 0;
  line-height: 1.4;
}

.cta-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.play-now-button {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.125rem;
  font-weight: 600;
  padding: 1.25rem 3rem;
  background: linear-gradient(135deg,
    var(--accent-primary) 0%,
    #0056b3 50%,
    var(--accent-primary) 100%
  );
  border: none;
  border-radius: 24px;
  color: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.01em;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow:
    0 8px 32px rgba(0, 122, 255, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.play-now-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.play-now-button:hover::before {
  left: 100%;
}

.play-now-button:hover {
  background: linear-gradient(135deg,
    #0056b3 0%,
    var(--accent-primary) 50%,
    #0056b3 100%
  );
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(0, 122, 255, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.play-now-button:active {
  transform: translateY(-1px) scale(1.01);
}

.play-now-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-tertiary);
  cursor: not-allowed;
  transform: none;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.cta-subtitle {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.9rem;
  color: var(--text-tertiary);
  margin: 0;
  text-align: center;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .top-user-status {
    top: 1rem;
    right: 1rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .user-status, .login-button {
    font-size: 0.8rem;
    padding: 0.625rem 0.875rem;
  }

  .audio-controls {
    flex-direction: row;
    gap: 0.5rem;
  }

  .volume-slider {
    width: 60px;
  }

  .landing-content {
    padding: 1rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
  }

  .play-now-button {
    font-size: 1rem;
    padding: 0.875rem 2rem;
  }

  .auth-modal {
    padding: 2rem 1.5rem;
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .auth-modal-header h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .play-now-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .auth-modal {
    padding: 1.5rem 1rem;
  }
}

/* Additional Apple-Style Components */
.apple-card {
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.apple-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-large);
  transform: translateY(-2px);
}

.apple-text-primary {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text-primary);
  font-weight: 500;
}

.apple-text-secondary {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text-secondary);
  font-weight: 400;
}

.apple-text-tertiary {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text-tertiary);
  font-weight: 400;
}

.apple-heading-1 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.03em;
  line-height: 1.1;
  margin: 0;
}

.apple-heading-2 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  line-height: 1.2;
  margin: 0;
}

.apple-heading-3 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  letter-spacing: -0.01em;
  line-height: 1.3;
  margin: 0;
}

.apple-body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 1rem;
  font-weight: 400;
  color: var(--text-secondary);
  line-height: 1.5;
  margin: 0;
}

.apple-caption {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-tertiary);
  line-height: 1.4;
  margin: 0;
}

/* Apple-Style Button Variants */
.apple-button-large {
  font-size: 1.125rem;
  padding: 1rem 2rem;
  border-radius: 16px;
}

.apple-button-small {
  font-size: 0.875rem;
  padding: 0.625rem 1rem;
  border-radius: 10px;
}

.apple-button-icon {
  padding: 0.75rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: auto;
}

.apple-button-success {
  background: var(--apple-green);
  color: white;
}

.apple-button-success:hover {
  background: #28a745;
}

.apple-button-warning {
  background: var(--apple-orange);
  color: white;
}

.apple-button-warning:hover {
  background: #e68900;
}

.apple-button-danger {
  background: var(--apple-red);
  color: white;
}

.apple-button-danger:hover {
  background: #dc2626;
}

/* Apple-Style Form Enhancements */
.apple-form-group {
  margin-bottom: 1.5rem;
}

.apple-form-row {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.apple-form-row .apple-form-group {
  flex: 1;
  margin-bottom: 0;
}

.apple-select {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  width: 100%;
  padding: 0.875rem 1rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  cursor: pointer;
}

.apple-select:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  background: var(--bg-secondary);
}

.apple-textarea {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  width: 100%;
  padding: 0.875rem 1rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 12px;
  color: var(--text-primary);
  font-size: 1rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  resize: vertical;
  min-height: 100px;
}

.apple-textarea:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
  background: var(--bg-secondary);
}

.apple-checkbox {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.apple-checkbox:checked {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
}

.apple-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.apple-radio {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.apple-radio:checked {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
}

.apple-radio:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

/* Apple-Style Legal Overlay */
.legal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.legal-overlay-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  z-index: -1;
}

.legal-content {
  background: var(--bg-secondary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 24px;
  padding: 3rem;
  max-width: 600px;
  width: 90%;
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(20px);
  animation: fade-in-up 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 90vh;
  overflow-y: auto;
}

.legal-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.legal-title {
  margin-bottom: 1rem;
}

.legal-subtitle {
  margin: 0;
}

.legal-form {
  margin-bottom: 2.5rem;
}

.consent-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--bg-tertiary);
  border: 1px solid var(--bg-quaternary);
  border-radius: 12px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.consent-item:hover {
  border-color: var(--accent-primary);
  background: var(--bg-secondary);
}

.consent-item.check-all-item {
  background: var(--bg-quaternary);
  border-color: var(--accent-primary);
}

.consent-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--text-primary);
}

.consent-checkbox {
  appearance: none;
  width: 20px;
  height: 20px;
  background: var(--bg-primary);
  border: 2px solid var(--bg-quaternary);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  flex-shrink: 0;
  margin-top: 2px;
}

.consent-checkbox:checked {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
}

.consent-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.checkbox-custom {
  display: none;
}

.consent-text {
  flex: 1;
}

.legal-link {
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.legal-link:hover {
  text-decoration: underline;
}

.legal-footer {
  text-align: center;
  border-top: 1px solid var(--bg-quaternary);
  padding-top: 2rem;
}

.required-note {
  margin-bottom: 1.5rem;
}

.enter-button {
  min-width: 200px;
  margin-bottom: 1rem;
}

.enter-button.disabled {
  background: var(--bg-quaternary) !important;
  color: var(--text-tertiary) !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Mobile responsive for legal overlay */
@media (max-width: 768px) {
  .legal-content {
    padding: 2rem 1.5rem;
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .legal-title {
    font-size: 2rem;
  }

  .consent-item {
    padding: 0.75rem;
  }

  .consent-label {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .legal-content {
    padding: 1.5rem 1rem;
  }

  .legal-title {
    font-size: 1.75rem;
  }
}
