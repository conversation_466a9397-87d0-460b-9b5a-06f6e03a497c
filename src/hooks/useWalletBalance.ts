import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '../contexts/AuthContext'
import solanaClientService from '../services/SolanaClientService'
import BalanceMonitorService, { BalanceChangeEvent } from '../services/BalanceMonitorService'
import SolanaPriceService from '../services/SolanaPriceService'
import BalanceUpdateService from '../services/BalanceUpdateService'

interface UseWalletBalanceOptions {
  autoRefresh?: boolean
  refreshInterval?: number
  syncWithDatabase?: boolean
  enableRealTimeUpdates?: boolean
}

interface UseWalletBalanceReturn {
  balance: number | null
  usdBalance: number | null
  loading: boolean
  priceLoading: boolean
  error: string | null
  lastUpdated: Date | null
  refreshBalance: () => Promise<void>
  refreshPrice: () => Promise<void>
  refreshAll: () => Promise<void>
  isMonitoring: boolean
  solPrice: number | null
}

export function useWalletBalance({
  autoRefresh = false,
  refreshInterval = 30000, // 30 seconds
  syncWithDatabase = true,
  enableRealTimeUpdates = false
}: UseWalletBalanceOptions = {}): UseWalletBalanceReturn {
  const { user, userProfile, updateProfile } = useAuth()
  const [balance, setBalance] = useState<number | null>(null)
  const [usdBalance, setUsdBalance] = useState<number | null>(null)
  const [loading, setLoading] = useState(false)
  const [priceLoading, setPriceLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [solPrice, setSolPrice] = useState<number | null>(null)

  const priceService = SolanaPriceService.getInstance()
  const balanceUpdateService = BalanceUpdateService.getInstance()



  const fetchBalance = useCallback(async () => {
    if (!user) {
      setBalance(null)
      setUsdBalance(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const balanceResult = await solanaClientService.getUserBalance(user.id)

      if (balanceResult.success && balanceResult.balance !== undefined) {
        setBalance(balanceResult.balance)
        setLastUpdated(new Date())

        // Update USD balance if we have a price
        if (solPrice !== null) {
          setUsdBalance(balanceResult.balance * solPrice)
        }

        // Sync with database if enabled and balance differs
        if (syncWithDatabase && userProfile && userProfile.solana_balance !== balanceResult.balance) {
          await updateProfile({ solana_balance: balanceResult.balance })
        }
      } else {
        throw new Error(balanceResult.error || 'Failed to fetch balance')
      }
    } catch (err) {
      console.error('Error fetching wallet balance:', err)
      setError('Failed to fetch wallet balance')
      setBalance(null)
      setUsdBalance(null)
    } finally {
      setLoading(false)
    }
  }, [user, userProfile, syncWithDatabase, updateProfile, solPrice])

  const fetchPrice = useCallback(async () => {
    setPriceLoading(true)
    try {
      const price = await priceService.getSolanaPrice()
      setSolPrice(price)

      // Update USD balance if we have a SOL balance
      if (balance !== null && price !== null) {
        setUsdBalance(balance * price)
      }
    } catch (err) {
      console.error('Error fetching SOL price:', err)
    } finally {
      setPriceLoading(false)
    }
  }, [priceService, balance])

  const refreshBalance = useCallback(async () => {
    if (!user) return

    // Use the balance update service for more comprehensive updates
    const result = await balanceUpdateService.forceUpdateBalance(user.id)
    if (result.success) {
      setBalance(result.solBalance || null)
      setUsdBalance(result.usdBalance || null)
      setLastUpdated(new Date())
    } else {
      // Fallback to direct fetch
      await fetchBalance()
    }
  }, [user, balanceUpdateService, fetchBalance])

  const refreshPrice = useCallback(async () => {
    if (!user || balance === null) {
      await fetchPrice()
      return
    }

    // Update USD balance using current SOL balance and latest price
    const result = await balanceUpdateService.updateUsdBalance(user.id, balance)
    if (result.success) {
      setSolPrice(result.solPrice || null)
      setUsdBalance(result.usdBalance || null)
      setLastUpdated(new Date())
    } else {
      // Fallback to direct price fetch
      await fetchPrice()
    }
  }, [user, balance, balanceUpdateService, fetchPrice])

  const refreshAll = useCallback(async () => {
    await Promise.all([refreshBalance(), refreshPrice()])
  }, [refreshBalance, refreshPrice])

  // Handle real-time balance updates
  useEffect(() => {
    if (!enableRealTimeUpdates || !user) return

    const balanceMonitor = BalanceMonitorService.getInstance()
    
    // Add wallet to monitoring
    const setupMonitoring = async () => {
      const success = await balanceMonitor.addWallet(user.id)
      setIsMonitoring(success)
    }

    // Subscribe to balance changes
    const unsubscribe = balanceMonitor.onBalanceChange((event: BalanceChangeEvent) => {
      if (event.userId === user.id) {
        setBalance(event.newBalance)
        setLastUpdated(event.timestamp)
        console.log(`Balance updated: ${event.newBalance} SOL (${event.difference > 0 ? '+' : ''}${event.difference.toFixed(6)} SOL)`)
      }
    })

    setupMonitoring()

    return () => {
      unsubscribe()
      balanceMonitor.removeWallet(user.id)
      setIsMonitoring(false)
    }
  }, [enableRealTimeUpdates, user])

  // Initial fetch
  useEffect(() => {
    fetchBalance()
    fetchPrice()
  }, [fetchBalance, fetchPrice])

  // Auto refresh (fallback when real-time updates are disabled)
  useEffect(() => {
    if (!autoRefresh || enableRealTimeUpdates || !user) return

    // Start automatic balance updates using the service
    if (syncWithDatabase) {
      balanceUpdateService.startAutoUpdate(user.id)
    }

    const interval = setInterval(() => {
      fetchBalance()
      fetchPrice()
    }, refreshInterval)

    return () => {
      clearInterval(interval)
      if (syncWithDatabase) {
        balanceUpdateService.stopAutoUpdate()
      }
    }
  }, [autoRefresh, refreshInterval, fetchBalance, fetchPrice, enableRealTimeUpdates, user, syncWithDatabase, balanceUpdateService])

  return {
    balance,
    usdBalance,
    loading,
    priceLoading,
    error,
    lastUpdated,
    refreshBalance,
    refreshPrice,
    refreshAll,
    isMonitoring,
    solPrice
  }
}

export default useWalletBalance