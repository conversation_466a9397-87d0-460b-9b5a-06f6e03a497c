import { useState, useEffect, useCallback } from 'react';
import SolanaPriceService from '../services/SolanaPriceService';

interface UseSolanaPriceOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseSolanaPriceReturn {
  solPrice: number | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  refreshPrice: () => Promise<void>;
  convertSolToUsd: (solAmount: number) => number | null;
  formatUsdAmount: (usdAmount: number) => string;
}

export function useSolanaPrice({
  autoRefresh = false,
  refreshInterval = 60000 // 1 minute
}: UseSolanaPriceOptions = {}): UseSolanaPriceReturn {
  const [solPrice, setSolPrice] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const priceService = SolanaPriceService.getInstance();

  const fetchPrice = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const price = await priceService.getSolanaPrice();
      setSolPrice(price);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching Solana price:', err);
      setError('Failed to fetch Solana price');
    } finally {
      setLoading(false);
    }
  }, [priceService]);

  const refreshPrice = useCallback(async () => {
    await fetchPrice();
  }, [fetchPrice]);

  const convertSolToUsd = useCallback((solAmount: number): number | null => {
    if (solPrice === null) return null;
    return priceService.convertSolToUsd(solAmount, solPrice);
  }, [solPrice, priceService]);

  const formatUsdAmount = useCallback((usdAmount: number): string => {
    return priceService.formatUsdAmount(usdAmount);
  }, [priceService]);

  // Initial fetch
  useEffect(() => {
    fetchPrice();
  }, [fetchPrice]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchPrice, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchPrice]);

  return {
    solPrice,
    loading,
    error,
    lastUpdated,
    refreshPrice,
    convertSolToUsd,
    formatUsdAmount
  };
}

export default useSolanaPrice;