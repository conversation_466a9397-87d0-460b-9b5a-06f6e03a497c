import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase, UserProfile, getUserProfile, createUserProfile } from '../lib/supabase'

interface WalletCreationData {
  publicKey: string
  isNewWallet: boolean
}

interface AuthContextType {
  user: User | null
  session: Session | null
  userProfile: UserProfile | null
  loading: boolean
  walletCreationData: WalletCreationData | null
  setWalletCreationData: (data: WalletCreationData | null) => void
  signUp: (email: string, password: string, username: string) => Promise<{ data: any; error: any }>
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<UserProfile>) => Promise<boolean>
  updateUserProfile: (updates: Partial<UserProfile>) => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [walletCreationData, setWalletCreationData] = useState<WalletCreationData | null>(null)

  useEffect(() => {
    console.log('🔐 AuthContext: Initializing...')

    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.log('🔐 AuthContext: Loading timeout reached, forcing completion')
      setLoading(false)
    }, 5000) // 5 second timeout

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('🔐 AuthContext: Initial session check:', session ? 'Found' : 'None')
      clearTimeout(loadingTimeout) // Clear timeout since we got a response
      setSession(session)
      setUser(session?.user ?? null)
      if (session?.user) {
        console.log('🔐 AuthContext: Loading profile for user:', session.user.id)
        loadUserProfile(session.user.id)
      } else {
        console.log('🔐 AuthContext: No session, setting loading to false')
        setLoading(false)
      }
    }).catch((error) => {
      console.error('🔐 AuthContext: Error getting session:', error)
      clearTimeout(loadingTimeout)
      setLoading(false)
    })

    // Listen for profile refresh events
    const handleProfileRefresh = () => {
      if (user) {
        console.log('🔐 AuthContext: Profile refresh requested')
        loadUserProfile(user.id)
      }
    }

    window.addEventListener('profileRefresh', handleProfileRefresh)

    return () => {
      window.removeEventListener('profileRefresh', handleProfileRefresh)
    }

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔐 AuthContext: Auth state change:', event, session ? 'Session exists' : 'No session')
      setSession(session)
      setUser(session?.user ?? null)

      if (session?.user) {
        console.log('🔐 AuthContext: Loading profile after auth change for user:', session.user.id)
        await loadUserProfile(session.user.id)
      } else {
        console.log('🔐 AuthContext: No session after auth change, clearing profile')
        setUserProfile(null)
        setLoading(false)
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const loadUserProfile = async (userId: string) => {
    try {
      console.log('🔐 AuthContext: Loading profile for user:', userId)
      let profile = await getUserProfile(userId)

      // If profile doesn't exist, create it
      if (!profile && user) {
        console.log('🔐 AuthContext: Profile not found, creating new profile')
        const username = user.user_metadata?.username || `Player${userId.slice(0, 8)}`
        profile = await createUserProfile(userId, username)
        console.log('🔐 AuthContext: New profile created:', profile?.username)
      }

      console.log('🔐 AuthContext: Setting profile:', profile?.username || 'No profile')
      setUserProfile(profile)
    } catch (error) {
      console.error('🔐 AuthContext: Error loading user profile:', error)
      // Create a fallback profile to prevent infinite loading
      const fallbackProfile: UserProfile = {
        id: userId,
        username: `Player${userId.slice(0, 8)}`,
        solana_balance: 100,
        total_games_played: 0,
        total_wins: 0,
        total_earnings: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        audio_muted: false,
        audio_volume: 0.6
      }
      setUserProfile(fallbackProfile)
      console.log('🔐 AuthContext: Fallback profile set')
    } finally {
      console.log('🔐 AuthContext: Setting loading to false')
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string, username: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username: username
          }
        }
      })

      if (error) throw error

      if (data.user) {
        // User profile will be created automatically by database trigger
        // Now create Solana wallet
        try {
          const walletResponse = await fetch(`${process.env.REACT_APP_SUPABASE_URL}/functions/v1/createUserWallet`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${process.env.REACT_APP_SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              user_id: data.user.id
            })
          });

          const walletResult = await walletResponse.json();

          if (walletResult.success) {
            console.log('✅ Wallet created for new user:', walletResult.public_key);

            // Set wallet creation data to show popup
            setWalletCreationData({
              publicKey: walletResult.public_key,
              isNewWallet: true
            });
          } else {
            console.error('❌ Failed to create wallet:', walletResult.error);
          }
        } catch (walletError) {
          console.error('❌ Error creating wallet:', walletError);
          // Don't throw here - user account was created successfully
        }

        // Create Solana wallet for the user
        try {
          const { data: walletData, error: walletError } = await supabase.functions.invoke('createUserWallet', {
            body: { user_id: data.user.id }
          })

          if (walletError) {
            console.error('Error creating wallet:', walletError)
          } else if (walletData?.success) {
            console.log('✅ Solana wallet created:', walletData.public_key)
            // Show wallet creation popup to user
            setWalletCreationData({
              publicKey: walletData.public_key,
              isNewWallet: true
            })
          }
        } catch (walletError) {
          console.error('Error creating Solana wallet:', walletError)
          // Don't fail the signup process if wallet creation fails
        }
      }

      return { data, error: null }
    } catch (error) {
      console.error('Error signing up:', error)
      return { data: null, error }
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      return { data, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    try {
      await supabase.auth.signOut()
      setUserProfile(null)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user || !userProfile) return false

    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('user_id', user.id)

      if (error) {
        console.error('Error updating profile:', error)
        return false
      }

      // Update local state
      setUserProfile({ ...userProfile, ...updates })
      return true
    } catch (error) {
      console.error('Error updating profile:', error)
      return false
    }
  }

  const updateUserProfile = async (updates: Partial<UserProfile>) => {
    if (!user || !userProfile) return

    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('user_id', user.id)

      if (error) {
        console.error('Error updating profile:', error)
        return
      }

      // Update local state
      setUserProfile({ ...userProfile, ...updates })
    } catch (error) {
      console.error('Error updating profile:', error)
    }
  }

  const refreshProfile = async () => {
    if (user) {
      await loadUserProfile(user.id)
    }
  }

  const value = {
    user,
    session,
    userProfile,
    loading,
    walletCreationData,
    setWalletCreationData,
    signUp,
    signIn,
    signOut,
    updateProfile,
    updateUserProfile,
    refreshProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}