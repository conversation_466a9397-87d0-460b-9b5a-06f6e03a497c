#!/bin/bash

# SnakePit Solana Integration Deployment Script
# This script deploys all Supabase functions and sets up the Solana integration

set -e  # Exit on any error

echo "🚀 SnakePit Solana Integration Deployment"
echo "========================================"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

echo "📋 Checking environment variables..."

# Check for required environment variables
if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_SERVICE_ROLE_KEY" ]; then
    echo "❌ Missing required environment variables:"
    echo "   SUPABASE_URL"
    echo "   SUPABASE_SERVICE_ROLE_KEY"
    echo ""
    echo "Please set these in your .env file or environment"
    exit 1
fi

echo "✅ Environment variables configured"

echo ""
echo "🔧 Deploying Supabase Functions..."

# Array of functions to deploy
functions=(
    "createUserWallet"
    "createRoomWallet" 
    "createAdminWallet"
    "validateGameEntry"
    "transferToRoom"
    "cashout"
    "cashoutFromRoom"
    "transferToAdmin"
    "roomToAdmin"
)

# Deploy each function
for func in "${functions[@]}"; do
    echo "📦 Deploying $func..."
    if supabase functions deploy "$func"; then
        echo "✅ $func deployed successfully"
    else
        echo "❌ Failed to deploy $func"
        exit 1
    fi
done

echo ""
echo "🏦 Initializing Admin Wallet..."

# Initialize admin wallet
if node scripts/initializeAdminWallet.js; then
    echo "✅ Admin wallet initialized successfully"
else
    echo "❌ Failed to initialize admin wallet"
    exit 1
fi

echo ""
echo "🎉 Solana Integration Deployment Complete!"
echo ""
echo "Next steps:"
echo "1. Test the integration with a small amount"
echo "2. Fund the admin wallet if needed"
echo "3. Monitor the system for any issues"
echo "4. Update your frontend environment variables if needed"
echo ""
echo "📚 See SOLANA_INTEGRATION.md for detailed documentation"
