#!/usr/bin/env node

/**
 * Initialize Admin Wallet Script
 * 
 * This script creates the admin wallet for the SnakePit game.
 * Run this once during initial setup.
 * 
 * Usage: node scripts/initializeAdminWallet.js
 */

require('dotenv').config();

async function initializeAdminWallet() {
  try {
    console.log('🏦 Initializing Admin Wallet...');
    
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing required environment variables: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
    }
    
    console.log('📡 Calling createAdminWallet function...');
    
    const response = await fetch(`${supabaseUrl}/functions/v1/createAdminWallet`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Admin wallet initialized successfully!');
      console.log(`📍 Admin Wallet Address: ${result.public_key}`);
      console.log('💡 Save this address for your records');
      console.log('');
      console.log('🎉 Setup complete! Your SnakePit admin wallet is ready.');
    } else {
      console.error('❌ Failed to initialize admin wallet:', result.error);
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Error initializing admin wallet:', error.message);
    process.exit(1);
  }
}

// Run the initialization
initializeAdminWallet();
