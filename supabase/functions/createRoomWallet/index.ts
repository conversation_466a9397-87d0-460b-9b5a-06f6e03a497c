import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { Keypair } from 'npm:@solana/web3.js@1.95.2';
import { encode } from 'npm:bs58@5.0.0';
import { AES, enc } from 'npm:crypto-js@4.1.1';

interface CreateRoomWalletRequest {
  room_id: string;
}

interface CreateRoomWalletResponse {
  success: boolean;
  public_key?: string;
  message?: string;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const encryptionKey = Deno.env.get('WALLET_ENCRYPTION_KEY') || 'default-key-change-in-production';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Encrypt a private key for secure storage
 */
function encryptPrivateKey(privateKey: string): string {
  return AES.encrypt(privateKey, encryptionKey).toString();
}

/**
 * Generate a new Solana wallet
 */
function generateSolanaWallet() {
  const keypair = Keypair.generate();
  const publicKey = keypair.publicKey.toBase58();
  const privateKey = encode(keypair.secretKey);
  const encryptedPrivateKey = encryptPrivateKey(privateKey);
  
  return {
    publicKey,
    encryptedPrivateKey
  };
}

Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Verify the user is authenticated (service role for server-side calls)
    const token = authHeader.replace('Bearer ', '');
    
    // For room wallet creation, we expect this to be called from the server
    // with the service role key, so we'll validate differently
    if (token !== supabaseServiceKey) {
      // If not service role, verify it's a valid user token
      const { data: { user }, error: authError } = await supabase.auth.getUser(token);
      if (authError || !user) {
        throw new Error('Unauthorized');
      }
    }

    const { room_id }: CreateRoomWalletRequest = await req.json();
    
    if (!room_id) {
      throw new Error('Room ID is required');
    }

    // Check if room already has a wallet
    const { data: existingWallet, error: checkError } = await supabase
      .from('room_wallets')
      .select('public_key')
      .eq('room_id', room_id)
      .eq('is_active', true)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new Error(`Error checking existing room wallet: ${checkError.message}`);
    }

    if (existingWallet) {
      return new Response(
        JSON.stringify({
          success: true,
          public_key: existingWallet.public_key,
          message: 'Room wallet already exists'
        } as CreateRoomWalletResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    // Generate new wallet for the room
    const { publicKey, encryptedPrivateKey } = generateSolanaWallet();

    // Store room wallet in database
    const { error: insertError } = await supabase
      .from('room_wallets')
      .insert({
        room_id,
        public_key: publicKey,
        encrypted_private_key: encryptedPrivateKey,
        current_balance: 0,
        total_wagered: 0,
        total_paid_out: 0,
        is_active: true
      });

    if (insertError) {
      throw new Error(`Error creating room wallet: ${insertError.message}`);
    }

    console.log(`✅ Created room wallet for room ${room_id}: ${publicKey}`);

    return new Response(
      JSON.stringify({
        success: true,
        public_key: publicKey,
        message: 'Room wallet created successfully'
      } as CreateRoomWalletResponse),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('❌ Error in createRoomWallet:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      } as CreateRoomWalletResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
