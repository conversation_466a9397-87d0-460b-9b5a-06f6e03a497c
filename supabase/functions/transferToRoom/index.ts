import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { Connection, PublicKey, Transaction, SystemProgram, Keypair, LAMPORTS_PER_SOL, sendAndConfirmTransaction } from 'npm:@solana/web3.js@1.95.2';
import { decode } from 'npm:bs58@5.0.0';
import { AES, enc } from 'npm:crypto-js@4.1.1';
import { PythHttpClient, getPythProgramKeyForCluster } from 'npm:@pythnetwork/client@2.21.0';

interface TransferToRoomRequest {
  user_id: string;
  room_id: string;
  amount: number; // Amount in SOL
}

interface TransferToRoomResponse {
  success: boolean;
  transaction_signature?: string;
  transaction_id?: string;
  message?: string;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const encryptionKey = Deno.env.get('WALLET_ENCRYPTION_KEY') || 'default-key-change-in-production';
const solanaRpcUrl = Deno.env.get('SOLANA_RPC_URL') || 'https://api.devnet.solana.com';

const supabase = createClient(supabaseUrl, supabaseServiceKey);
const connection = new Connection(solanaRpcUrl, 'confirmed');

/**
 * Decrypt a private key for transaction signing
 */
function decryptPrivateKey(encryptedPrivateKey: string): string {
  const bytes = AES.decrypt(encryptedPrivateKey, encryptionKey);
  return bytes.toString(enc.Utf8);
}

/**
 * Convert SOL to lamports
 */
function solToLamports(sol: number): number {
  return Math.floor(sol * LAMPORTS_PER_SOL);
}

/**
 * Create and send a SOL transfer transaction
 */
async function transferSol(
  fromKeypair: Keypair,
  toPublicKey: PublicKey,
  lamports: number
): Promise<string> {
  const transaction = new Transaction().add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPublicKey,
      lamports,
    })
  );

  const signature = await sendAndConfirmTransaction(
    connection,
    transaction,
    [fromKeypair],
    {
      commitment: 'confirmed',
      maxRetries: 3,
    }
  );

  return signature;
}

Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Verify the user is authenticated
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      throw new Error('Unauthorized');
    }

    const { user_id, room_id, amount }: TransferToRoomRequest = await req.json();
    
    // Verify the user_id matches the authenticated user
    if (user_id !== user.id) {
      throw new Error('User ID mismatch');
    }

    if (!room_id || !amount || amount <= 0) {
      throw new Error('Invalid room_id or amount');
    }

    // Get user's wallet
    const { data: userWallet, error: userWalletError } = await supabase
      .from('solana_wallets')
      .select('public_key, encrypted_private_key')
      .eq('user_id', user_id)
      .eq('is_active', true)
      .single();

    if (userWalletError || !userWallet) {
      throw new Error('User wallet not found');
    }

    // Get room's wallet
    const { data: roomWallet, error: roomWalletError } = await supabase
      .from('room_wallets')
      .select('public_key')
      .eq('room_id', room_id)
      .eq('is_active', true)
      .single();

    if (roomWalletError || !roomWallet) {
      throw new Error('Room wallet not found');
    }

    // Decrypt user's private key
    const decryptedPrivateKey = decryptPrivateKey(userWallet.encrypted_private_key);
    const userKeypair = Keypair.fromSecretKey(decode(decryptedPrivateKey));
    const roomPublicKey = new PublicKey(roomWallet.public_key);

    // Check user's balance
    const userBalance = await connection.getBalance(userKeypair.publicKey);
    const requiredLamports = solToLamports(amount);
    const estimatedFee = 5000; // Estimated transaction fee in lamports

    if (userBalance < requiredLamports + estimatedFee) {
      throw new Error('Insufficient balance for transfer and fees');
    }

    // Create transaction record (pending)
    const { data: transactionRecord, error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id,
        type: 'room_transfer',
        amount,
        from_wallet: userWallet.public_key,
        to_wallet: roomWallet.public_key,
        room_id,
        status: 'pending',
        description: `Transfer ${amount} SOL to room ${room_id}`
      })
      .select('id')
      .single();

    if (transactionError || !transactionRecord) {
      throw new Error('Failed to create transaction record');
    }

    try {
      // Execute the transfer
      const signature = await transferSol(
        userKeypair,
        roomPublicKey,
        requiredLamports
      );

      // Update transaction record with signature and confirmed status
      const { error: updateError } = await supabase
        .from('transactions')
        .update({
          solana_signature: signature,
          status: 'confirmed',
          confirmed_at: new Date().toISOString()
        })
        .eq('id', transactionRecord.id);

      if (updateError) {
        console.error('Failed to update transaction record:', updateError);
      }

      // Update room wallet balance
      const { error: roomUpdateError } = await supabase
        .from('room_wallets')
        .update({
          current_balance: supabase.sql`current_balance + ${amount}`,
          total_wagered: supabase.sql`total_wagered + ${amount}`
        })
        .eq('room_id', room_id);

      if (roomUpdateError) {
        console.error('Failed to update room wallet balance:', roomUpdateError);
      }

      // Update user's solana balance in profile
      const newUserBalance = (userBalance - requiredLamports - estimatedFee) / LAMPORTS_PER_SOL;
      const { error: profileUpdateError } = await supabase
        .from('user_profiles')
        .update({ solana_balance: newUserBalance })
        .eq('user_id', user_id);

      if (profileUpdateError) {
        console.error('Failed to update user profile balance:', profileUpdateError);
      }

      console.log(`✅ Transfer completed: ${amount} SOL from ${userWallet.public_key} to ${roomWallet.public_key}`);
      console.log(`Transaction signature: ${signature}`);

      return new Response(
        JSON.stringify({
          success: true,
          transaction_signature: signature,
          transaction_id: transactionRecord.id,
          message: 'Transfer completed successfully'
        } as TransferToRoomResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );

    } catch (transferError) {
      // Update transaction record to failed status
      await supabase
        .from('transactions')
        .update({ status: 'failed' })
        .eq('id', transactionRecord.id);

      throw transferError;
    }

  } catch (error) {
    console.error('❌ Error in transferToRoom:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      } as TransferToRoomResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
