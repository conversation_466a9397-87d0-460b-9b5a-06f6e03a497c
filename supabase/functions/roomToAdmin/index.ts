import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

interface RoomToAdminRequest {
  room_id: string;
  reason?: string;
}

interface RoomToAdminResponse {
  success: boolean;
  amount_transferred?: number;
  transaction_signature?: string;
  message?: string;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Verify this is called with service role key (server-side only)
    const token = authHeader.replace('Bearer ', '');
    if (token !== supabaseServiceKey) {
      throw new Error('Unauthorized - Service role required');
    }

    const { room_id, reason }: RoomToAdminRequest = await req.json();

    if (!room_id) {
      throw new Error('Room ID is required');
    }

    console.log(`🏗️ Processing room destruction for ${room_id}, reason: ${reason || 'Not specified'}`);

    // Check if room wallet exists and has balance
    const { data: roomWallet, error: roomWalletError } = await supabase
      .from('room_wallets')
      .select('public_key, current_balance')
      .eq('room_id', room_id)
      .eq('is_active', true)
      .single();

    if (roomWalletError || !roomWallet) {
      console.log(`No active room wallet found for ${room_id}`);
      return new Response(
        JSON.stringify({
          success: true,
          amount_transferred: 0,
          message: 'No room wallet found or already processed'
        } as RoomToAdminResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    // If there's a meaningful balance, transfer to admin
    if (roomWallet.current_balance > 0.001) { // Only transfer if more than 0.001 SOL
      console.log(`Transferring ${roomWallet.current_balance} SOL from room ${room_id} to admin`);

      // Call the transferToAdmin function
      const transferResponse = await fetch(`${supabaseUrl}/functions/v1/transferToAdmin`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          room_id
        })
      });

      const transferResult = await transferResponse.json();

      if (!transferResult.success) {
        throw new Error(`Admin transfer failed: ${transferResult.error}`);
      }

      // Mark room wallet as inactive
      const { error: deactivateError } = await supabase
        .from('room_wallets')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('room_id', room_id);

      if (deactivateError) {
        console.error('Failed to deactivate room wallet:', deactivateError);
      }

      console.log(`✅ Room ${room_id} destruction completed, ${transferResult.amount_transferred} SOL transferred to admin`);

      return new Response(
        JSON.stringify({
          success: true,
          amount_transferred: transferResult.amount_transferred,
          transaction_signature: transferResult.transaction_signature,
          message: `Room destroyed, ${transferResult.amount_transferred} SOL transferred to admin`
        } as RoomToAdminResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    } else {
      // No meaningful balance, just mark as inactive
      const { error: deactivateError } = await supabase
        .from('room_wallets')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('room_id', room_id);

      if (deactivateError) {
        console.error('Failed to deactivate room wallet:', deactivateError);
      }

      console.log(`✅ Room ${room_id} destroyed, no balance to transfer`);

      return new Response(
        JSON.stringify({
          success: true,
          amount_transferred: 0,
          message: 'Room destroyed, no balance to transfer'
        } as RoomToAdminResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

  } catch (error) {
    console.error('❌ Error processing room destruction:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      } as RoomToAdminResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
