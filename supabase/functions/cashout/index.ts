import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

interface CashoutRequest {
  user_id: string;
  room_id: string;
  game_mode: 'classic' | 'warfare';
  final_score: number;
  final_length: number;
  final_cash: number;
  duration_seconds: number;
  wager_amount: number;
}

interface CashoutResponse {
  success: boolean;
  winnings?: number;
  final_balance?: number;
  game_id?: string;
  transaction_signature?: string;
  message?: string;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Pyth Network configuration
const SOLANA_NETWORK = Deno.env.get('SOLANA_NETWORK') || 'devnet';
const SOL_USD_PRICE_FEED_ID = 'ef0d8b6fda2ceba41da15d4095d1da392a0d2f8ed0c6c7bc0f4cfac8c280b56d'; // SOL/USD price feed
const FALLBACK_API_URL = 'https://price-api.crypto.com/price/v1/token-price/solana';

// Initialize Pyth client
let pythClient: PythHttpClient | null = null;

async function initializePythClient() {
  try {
    const cluster = SOLANA_NETWORK === 'mainnet-beta' ? 'mainnet-beta' : 'devnet';
    const solanaRpcUrl = Deno.env.get('SOLANA_RPC_URL') ||
      (SOLANA_NETWORK === 'mainnet-beta' ? 'https://api.mainnet-beta.solana.com' : 'https://api.devnet.solana.com');

    pythClient = new PythHttpClient(
      new Connection(solanaRpcUrl),
      getPythProgramKeyForCluster(cluster as any)
    );
    console.log(`🐍 Pyth client initialized for ${cluster}`);
  } catch (error) {
    console.warn('⚠️ Failed to initialize Pyth client, will use fallback:', error);
    pythClient = null;
  }
}

/**
 * Get real-time SOL price using Pyth Network with Crypto.com fallback
 */
async function getSolanaPrice(): Promise<number> {
  try {
    // Try Pyth Network first
    const pythPrice = await getPriceFromPyth();
    if (pythPrice !== null) {
      console.log(`📊 SOL price from Pyth: $${pythPrice.toFixed(2)}`);
      return pythPrice;
    }
  } catch (error) {
    console.warn('⚠️ Pyth price fetch failed, falling back to Crypto.com:', error);
  }

  try {
    // Fallback to Crypto.com API
    const fallbackPrice = await getPriceFromCryptoCom();
    if (fallbackPrice !== null) {
      console.log(`📊 SOL price from Crypto.com (fallback): $${fallbackPrice.toFixed(2)}`);
      return fallbackPrice;
    }
  } catch (error) {
    console.error('❌ Both Pyth and Crypto.com price fetch failed:', error);
  }

  // Final fallback
  return 200;
}

async function getPriceFromPyth(): Promise<number | null> {
  if (!pythClient) {
    await initializePythClient();
    if (!pythClient) {
      throw new Error('Pyth client not initialized');
    }
  }

  const priceFeeds = await pythClient.getAssetPricesFromAccounts([
    new PublicKey(SOL_USD_PRICE_FEED_ID)
  ]);

  if (priceFeeds.length === 0) {
    throw new Error('No price feeds returned from Pyth');
  }

  const solPriceFeed = priceFeeds[0];
  if (!solPriceFeed || !solPriceFeed.price) {
    throw new Error('Invalid SOL price feed data from Pyth');
  }

  // Pyth prices come with confidence intervals and exponents
  const price = solPriceFeed.price.price;
  const expo = solPriceFeed.price.expo;
  const confidence = solPriceFeed.price.conf;

  // Calculate the actual price: price * 10^expo
  const actualPrice = price * Math.pow(10, expo);

  // Validate price is reasonable (between $1 and $10,000)
  if (actualPrice < 1 || actualPrice > 10000) {
    throw new Error(`Unreasonable SOL price from Pyth: $${actualPrice}`);
  }

  // Log confidence for monitoring
  const confidenceValue = confidence * Math.pow(10, expo);
  console.log(`🎯 Pyth SOL/USD: $${actualPrice.toFixed(2)} ±$${confidenceValue.toFixed(2)}`);

  return actualPrice;
}

async function getPriceFromCryptoCom(): Promise<number | null> {
  const response = await fetch(FALLBACK_API_URL, {
    method: 'GET',
    headers: {
      'accept': '*/*',
      'accept-language': 'en-US,en;q=0.9',
      'origin': 'https://crypto.com',
      'referer': 'https://crypto.com/',
      'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"macOS"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-site',
      'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
      'x-client': 'PriceWebPages'
    }
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  const price = data.usd_price;

  if (isNaN(price) || price <= 0) {
    throw new Error('Invalid price data received from Crypto.com');
  }

  return price;
}

/**
 * Convert USD to SOL using real-time price from Pyth Network
 */
async function usdToSol(usdAmount: number): Promise<number> {
  const price = await getSolanaPrice();
  return usdAmount / price;
}

/**
 * Calculate winnings based on game performance
 */
function calculateWinnings(gameMode: string, finalCash: number, wagerAmount: number): number {
  // In the original game, finalCash represents the player's accumulated value
  // Winnings = finalCash - wager (if positive)
  const winnings = Math.max(0, finalCash - wagerAmount);
  return winnings;
}

Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    const {
      user_id,
      room_id,
      game_mode,
      final_score,
      final_length,
      final_cash,
      duration_seconds,
      wager_amount
    }: CashoutRequest = await req.json();

    // Validate input
    if (!user_id || !room_id || !game_mode || final_cash === undefined || !wager_amount) {
      throw new Error('Missing required parameters');
    }

    if (!['classic', 'warfare'].includes(game_mode)) {
      throw new Error('Invalid game mode');
    }

    if (final_cash < 0 || wager_amount <= 0) {
      throw new Error('Invalid cash or wager amount');
    }

    console.log(`🎮 Processing cashout for user ${user_id} in room ${room_id}: $${final_cash} final cash, $${wager_amount} wager`);

    // Calculate winnings
    const winnings = calculateWinnings(game_mode, final_cash, wager_amount);
    const cashoutAmountUsd = Math.max(0, final_cash); // Player gets their final cash value
    const cashoutAmountSol = await usdToSol(cashoutAmountUsd);

    console.log(`Calculated winnings: $${winnings}, cashout amount: $${cashoutAmountUsd} (${cashoutAmountSol.toFixed(6)} SOL)`);

    // Only process transfer if there's something to cash out
    let transactionSignature = null;
    if (cashoutAmountSol > 0) {
      // Call the cashoutFromRoom function to transfer SOL from room to user
      const cashoutResponse = await fetch(`${supabaseUrl}/functions/v1/cashoutFromRoom`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id,
          room_id,
          amount: cashoutAmountSol
        })
      });

      const cashoutResult = await cashoutResponse.json();

      if (!cashoutResult.success) {
        throw new Error(`Cashout transfer failed: ${cashoutResult.error}`);
      }

      transactionSignature = cashoutResult.transaction_signature;
      console.log(`✅ Cashout transfer completed: ${transactionSignature}`);
    }

    // Record game session
    const gameId = `game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const { error: sessionError } = await supabase
      .from('game_sessions')
      .insert({
        id: gameId,
        user_id,
        game_mode,
        wager_amount,
        final_score: final_score || 0,
        final_length: final_length || 0,
        final_cash: final_cash,
        duration_seconds: duration_seconds || 0,
        cashed_out: true
      });

    if (sessionError) {
      console.error('Failed to record game session:', sessionError);
      // Don't throw here - the financial transaction succeeded
    }

    // Update user stats
    const { error: statsError } = await supabase
      .from('user_profiles')
      .update({
        total_games_played: supabase.sql`total_games_played + 1`,
        total_earnings: supabase.sql`total_earnings + ${winnings}`,
        total_wins: winnings > 0 ? supabase.sql`total_wins + 1` : supabase.sql`total_wins`
      })
      .eq('id', user_id);

    if (statsError) {
      console.error('Failed to update user stats:', statsError);
    }

    return new Response(
      JSON.stringify({
        success: true,
        winnings,
        final_balance: cashoutAmountUsd,
        game_id: gameId,
        transaction_signature: transactionSignature,
        message: `Cashout successful! You earned $${winnings.toFixed(2)}`
      } as CashoutResponse),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('❌ Error processing cashout:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      } as CashoutResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
