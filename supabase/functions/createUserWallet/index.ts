import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { Keypair } from 'npm:@solana/web3.js@1.95.2';
import { encode } from 'npm:bs58@5.0.0';
import { AES, enc } from 'npm:crypto-js@4.1.1';

interface CreateWalletRequest {
  user_id: string;
}

interface CreateWalletResponse {
  success: boolean;
  public_key?: string;
  message?: string;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const encryptionKey = Deno.env.get('WALLET_ENCRYPTION_KEY') || 'default-key-change-in-production';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Encrypt a private key for secure storage
 */
function encryptPrivateKey(privateKey: string): string {
  return AES.encrypt(privateKey, encryptionKey).toString();
}

/**
 * Generate a new Solana wallet
 */
function generateSolanaWallet() {
  const keypair = Keypair.generate();
  const publicKey = keypair.publicKey.toBase58();
  const privateKey = encode(keypair.secretKey);
  const encryptedPrivateKey = encryptPrivateKey(privateKey);
  
  return {
    publicKey,
    encryptedPrivateKey
  };
}

Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Verify the user is authenticated
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      throw new Error('Unauthorized');
    }

    const { user_id }: CreateWalletRequest = await req.json();
    
    // Verify the user_id matches the authenticated user
    if (user_id !== user.id) {
      throw new Error('User ID mismatch');
    }

    // Check if user already has a wallet
    const { data: existingWallet, error: checkError } = await supabase
      .from('solana_wallets')
      .select('public_key')
      .eq('user_id', user_id)
      .eq('is_active', true)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      throw new Error(`Error checking existing wallet: ${checkError.message}`);
    }

    if (existingWallet) {
      return new Response(
        JSON.stringify({
          success: true,
          public_key: existingWallet.public_key,
          message: 'Wallet already exists'
        } as CreateWalletResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    // Generate new wallet
    const { publicKey, encryptedPrivateKey } = generateSolanaWallet();

    // Store wallet in database
    const { error: insertError } = await supabase
      .from('solana_wallets')
      .insert({
        user_id,
        public_key: publicKey,
        encrypted_private_key: encryptedPrivateKey,
        wallet_type: 'user',
        is_active: true
      });

    if (insertError) {
      throw new Error(`Error creating wallet: ${insertError.message}`);
    }

    console.log(`✅ Created Solana wallet for user ${user_id}: ${publicKey}`);

    return new Response(
      JSON.stringify({
        success: true,
        public_key: publicKey,
        message: 'Wallet created successfully'
      } as CreateWalletResponse),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('❌ Error in createUserWallet:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      } as CreateWalletResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
