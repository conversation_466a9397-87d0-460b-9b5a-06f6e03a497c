import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { Connection, PublicKey } from 'npm:@solana/web3.js@1.95.2';
import { PythHttpClient, getPythProgramKeyForCluster } from 'npm:@pythnetwork/client@2.21.0';
import { Connection, PublicKey, LAMPORTS_PER_SOL } from 'npm:@solana/web3.js@1.95.2';

interface ValidateGameEntryRequest {
  user_id: string;
  wager_amount: number; // Amount in USD
  game_mode: 'classic' | 'warfare' | 'speed';
}

interface ValidateGameEntryResponse {
  success: boolean;
  can_join: boolean;
  user_balance_sol?: number;
  user_balance_usd?: number;
  required_usd?: number;
  message?: string;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Solana connection (devnet for testing)
const connection = new Connection('https://api.devnet.solana.com', 'confirmed');

// Pyth Network configuration
const SOLANA_NETWORK = Deno.env.get('SOLANA_NETWORK') || 'devnet';
const SOL_USD_PRICE_FEED_ID = 'ef0d8b6fda2ceba41da15d4095d1da392a0d2f8ed0c6c7bc0f4cfac8c280b56d'; // SOL/USD price feed
const FALLBACK_API_URL = 'https://price-api.crypto.com/price/v1/token-price/solana';

// Initialize Pyth client
let pythClient: PythHttpClient | null = null;

async function initializePythClient() {
  try {
    const cluster = SOLANA_NETWORK === 'mainnet-beta' ? 'mainnet-beta' : 'devnet';
    const solanaRpcUrl = Deno.env.get('SOLANA_RPC_URL') ||
      (SOLANA_NETWORK === 'mainnet-beta' ? 'https://api.mainnet-beta.solana.com' : 'https://api.devnet.solana.com');

    pythClient = new PythHttpClient(
      new Connection(solanaRpcUrl),
      getPythProgramKeyForCluster(cluster as any)
    );
    console.log(`🐍 Pyth client initialized for ${cluster}`);
  } catch (error) {
    console.warn('⚠️ Failed to initialize Pyth client, will use fallback:', error);
    pythClient = null;
  }
}

/**
 * Get real-time SOL price using Pyth Network with Crypto.com fallback
 */
async function getSolanaPrice(): Promise<number> {
  try {
    // Try Pyth Network first
    const pythPrice = await getPriceFromPyth();
    if (pythPrice !== null) {
      console.log(`📊 SOL price from Pyth: $${pythPrice.toFixed(2)}`);
      return pythPrice;
    }
  } catch (error) {
    console.warn('⚠️ Pyth price fetch failed, falling back to Crypto.com:', error);
  }

  try {
    // Fallback to Crypto.com API
    const fallbackPrice = await getPriceFromCryptoCom();
    if (fallbackPrice !== null) {
      console.log(`📊 SOL price from Crypto.com (fallback): $${fallbackPrice.toFixed(2)}`);
      return fallbackPrice;
    }
  } catch (error) {
    console.error('❌ Both Pyth and Crypto.com price fetch failed:', error);
  }

  // Final fallback
  return 200;
}

async function getPriceFromPyth(): Promise<number | null> {
  if (!pythClient) {
    await initializePythClient();
    if (!pythClient) {
      throw new Error('Pyth client not initialized');
    }
  }

  const priceFeeds = await pythClient.getAssetPricesFromAccounts([
    new PublicKey(SOL_USD_PRICE_FEED_ID)
  ]);

  if (priceFeeds.length === 0) {
    throw new Error('No price feeds returned from Pyth');
  }

  const solPriceFeed = priceFeeds[0];
  if (!solPriceFeed || !solPriceFeed.price) {
    throw new Error('Invalid SOL price feed data from Pyth');
  }

  const price = solPriceFeed.price.price;
  const expo = solPriceFeed.price.expo;
  const actualPrice = price * Math.pow(10, expo);

  if (actualPrice < 1 || actualPrice > 10000) {
    throw new Error(`Unreasonable SOL price from Pyth: $${actualPrice}`);
  }

  return actualPrice;
}

async function getPriceFromCryptoCom(): Promise<number | null> {
  const response = await fetch(FALLBACK_API_URL, {
    method: 'GET',
    headers: {
      'accept': '*/*',
      'accept-language': 'en-US,en;q=0.9',
      'origin': 'https://crypto.com',
      'referer': 'https://crypto.com/',
      'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    }
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  const price = data.usd_price;

  if (isNaN(price) || price <= 0) {
    throw new Error('Invalid price data received from Crypto.com');
  }

  return price;
}

/**
 * Convert SOL to USD using real-time price from Pyth Network
 */
async function solToUsd(solAmount: number): Promise<number> {
  const price = await getSolanaPrice();
  return solAmount * price;
}

/**
 * Convert USD to SOL using real-time price from Pyth Network
 */
async function usdToSol(usdAmount: number): Promise<number> {
  const price = await getSolanaPrice();
  return usdAmount / price;
}

/**
 * Get minimum balance requirements based on game mode
 */
async function getMinimumBalance(gameMode: string, wagerAmount: number): Promise<number> {
  // Base requirement: wager amount + buffer for transaction fees
  const baseFee = 0.01; // 0.01 SOL for transaction fees
  const wagerInSol = await usdToSol(wagerAmount);

  switch (gameMode) {
    case 'warfare':
      return wagerInSol + baseFee + 0.02; // Extra buffer for warfare mode
    case 'classic':
    case 'speed':
    default:
      return wagerInSol + baseFee;
  }
}

Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    const { user_id, wager_amount, game_mode }: ValidateGameEntryRequest = await req.json();
    
    if (!user_id || !wager_amount || !game_mode) {
      throw new Error('Missing required parameters');
    }

    if (wager_amount <= 0) {
      throw new Error('Invalid wager amount');
    }

    if (!['classic', 'warfare', 'speed'].includes(game_mode)) {
      throw new Error('Invalid game mode');
    }

    // Get user's wallet
    const { data: userWallet, error: walletError } = await supabase
      .from('solana_wallets')
      .select('public_key')
      .eq('user_id', user_id)
      .eq('is_active', true)
      .single();

    if (walletError || !userWallet) {
      return new Response(
        JSON.stringify({
          success: false,
          can_join: false,
          message: 'No wallet found. Please create a wallet first.',
          error: 'Wallet not found'
        } as ValidateGameEntryResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    // Check actual wallet balance on-chain
    const publicKey = new PublicKey(userWallet.public_key);
    const balanceLamports = await connection.getBalance(publicKey);
    const balanceSol = balanceLamports / LAMPORTS_PER_SOL;
    const balanceUsd = await solToUsd(balanceSol);

    // Calculate minimum required balance
    const requiredSol = await getMinimumBalance(game_mode, wager_amount);
    const requiredUsd = await solToUsd(requiredSol);

    const canJoin = balanceSol >= requiredSol;

    let message = '';
    if (canJoin) {
      message = `Balance sufficient. You have ${balanceSol.toFixed(6)} SOL ($${balanceUsd.toFixed(2)})`;
    } else {
      const shortfall = requiredSol - balanceSol;
      const shortfallUsd = await solToUsd(shortfall);
      message = `Insufficient balance. You need ${shortfall.toFixed(6)} more SOL ($${shortfallUsd.toFixed(2)})`;
    }

    console.log(`Balance validation for ${user_id}: ${balanceSol.toFixed(6)} SOL, required: ${requiredSol.toFixed(6)} SOL, can join: ${canJoin}`);

    return new Response(
      JSON.stringify({
        success: true,
        can_join: canJoin,
        user_balance_sol: balanceSol,
        user_balance_usd: balanceUsd,
        required_usd: requiredUsd,
        message
      } as ValidateGameEntryResponse),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('❌ Error validating game entry:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        can_join: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      } as ValidateGameEntryResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
