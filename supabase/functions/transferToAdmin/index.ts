import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { Connection, PublicKey, Transaction, SystemProgram, Keypair, LAMPORTS_PER_SOL, sendAndConfirmTransaction } from 'npm:@solana/web3.js@1.95.2';
import { decode } from 'npm:bs58@5.0.0';
import { AES, enc } from 'npm:crypto-js@4.1.1';

interface TransferToAdminRequest {
  room_id: string;
}

interface TransferToAdminResponse {
  success: boolean;
  transaction_signature?: string;
  transaction_id?: string;
  amount_transferred?: number;
  message?: string;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const encryptionKey = Deno.env.get('WALLET_ENCRYPTION_KEY') || 'default-key-change-in-production';
const solanaRpcUrl = Deno.env.get('SOLANA_RPC_URL') || 'https://api.devnet.solana.com';

const supabase = createClient(supabaseUrl, supabaseServiceKey);
const connection = new Connection(solanaRpcUrl, 'confirmed');

/**
 * Decrypt a private key for transaction signing
 */
function decryptPrivateKey(encryptedPrivateKey: string): string {
  const bytes = AES.decrypt(encryptedPrivateKey, encryptionKey);
  return bytes.toString(enc.Utf8);
}

/**
 * Convert lamports to SOL
 */
function lamportsToSol(lamports: number): number {
  return lamports / LAMPORTS_PER_SOL;
}

/**
 * Create and send a SOL transfer transaction
 */
async function transferSol(
  fromKeypair: Keypair,
  toPublicKey: PublicKey,
  lamports: number
): Promise<string> {
  const transaction = new Transaction().add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPublicKey,
      lamports,
    })
  );

  const signature = await sendAndConfirmTransaction(
    connection,
    transaction,
    [fromKeypair],
    {
      commitment: 'confirmed',
      maxRetries: 3,
    }
  );

  return signature;
}

Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    // Verify this is called with service role key (server-side only)
    const token = authHeader.replace('Bearer ', '');
    if (token !== supabaseServiceKey) {
      throw new Error('Unauthorized - Service role required');
    }

    const { room_id }: TransferToAdminRequest = await req.json();
    
    if (!room_id) {
      throw new Error('Room ID is required');
    }

    // Get room's wallet
    const { data: roomWallet, error: roomWalletError } = await supabase
      .from('room_wallets')
      .select('public_key, encrypted_private_key, current_balance')
      .eq('room_id', room_id)
      .eq('is_active', true)
      .single();

    if (roomWalletError || !roomWallet) {
      throw new Error('Room wallet not found');
    }

    // Get admin wallet
    const { data: adminWallet, error: adminWalletError } = await supabase
      .from('admin_wallet')
      .select('public_key')
      .eq('is_active', true)
      .single();

    if (adminWalletError || !adminWallet) {
      throw new Error('Admin wallet not found');
    }

    // Decrypt room's private key
    const decryptedPrivateKey = decryptPrivateKey(roomWallet.encrypted_private_key);
    const roomKeypair = Keypair.fromSecretKey(decode(decryptedPrivateKey));
    const adminPublicKey = new PublicKey(adminWallet.public_key);

    // Check room's actual balance on-chain
    const roomBalance = await connection.getBalance(roomKeypair.publicKey);
    const estimatedFee = 5000; // Estimated transaction fee in lamports

    if (roomBalance <= estimatedFee) {
      // No meaningful balance to transfer
      return new Response(
        JSON.stringify({
          success: true,
          amount_transferred: 0,
          message: 'No balance to transfer'
        } as TransferToAdminResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );
    }

    // Transfer all remaining balance minus fees
    const transferAmount = roomBalance - estimatedFee;
    const transferAmountSol = lamportsToSol(transferAmount);

    // Create transaction record (pending)
    const { data: transactionRecord, error: transactionError } = await supabase
      .from('transactions')
      .insert({
        user_id: null, // System transaction
        type: 'admin_transfer',
        amount: transferAmountSol,
        from_wallet: roomWallet.public_key,
        to_wallet: adminWallet.public_key,
        room_id,
        status: 'pending',
        description: `Transfer remaining ${transferAmountSol} SOL from destroyed room ${room_id} to admin`
      })
      .select('id')
      .single();

    if (transactionError || !transactionRecord) {
      throw new Error('Failed to create transaction record');
    }

    try {
      // Execute the transfer
      const signature = await transferSol(
        roomKeypair,
        adminPublicKey,
        transferAmount
      );

      // Update transaction record with signature and confirmed status
      const { error: updateError } = await supabase
        .from('transactions')
        .update({
          solana_signature: signature,
          status: 'confirmed',
          confirmed_at: new Date().toISOString()
        })
        .eq('id', transactionRecord.id);

      if (updateError) {
        console.error('Failed to update transaction record:', updateError);
      }

      // Update admin wallet balance
      const { error: adminUpdateError } = await supabase
        .from('admin_wallet')
        .update({
          total_balance: supabase.sql`total_balance + ${transferAmountSol}`,
          total_fees_collected: supabase.sql`total_fees_collected + ${transferAmountSol}`
        })
        .eq('is_active', true);

      if (adminUpdateError) {
        console.error('Failed to update admin wallet balance:', adminUpdateError);
      }

      // Mark room wallet as destroyed
      const { error: roomDestroyError } = await supabase
        .from('room_wallets')
        .update({
          is_active: false,
          destroyed_at: new Date().toISOString(),
          current_balance: 0
        })
        .eq('room_id', room_id);

      if (roomDestroyError) {
        console.error('Failed to mark room wallet as destroyed:', roomDestroyError);
      }

      console.log(`✅ Admin transfer completed: ${transferAmountSol} SOL from room ${room_id} to admin`);
      console.log(`Transaction signature: ${signature}`);

      return new Response(
        JSON.stringify({
          success: true,
          transaction_signature: signature,
          transaction_id: transactionRecord.id,
          amount_transferred: transferAmountSol,
          message: 'Admin transfer completed successfully'
        } as TransferToAdminResponse),
        {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        }
      );

    } catch (transferError) {
      // Update transaction record to failed status
      await supabase
        .from('transactions')
        .update({ status: 'failed' })
        .eq('id', transactionRecord.id);

      throw transferError;
    }

  } catch (error) {
    console.error('❌ Error in transferToAdmin:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      } as TransferToAdminResponse),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});