
[functions.createUserWallet]
enabled = true
verify_jwt = true
import_map = "./functions/createUserWallet/deno.json"
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/createUserWallet/index.ts"
# Specifies static files to be bundled with the function. Supports glob patterns.
# For example, if you want to serve static HTML pages in your function:
# static_files = [ "./functions/createUserWallet/*.html" ]

[functions.createRoomWallet]
enabled = true
verify_jwt = true
import_map = "./functions/createRoomWallet/deno.json"
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/createRoomWallet/index.ts"
# Specifies static files to be bundled with the function. Supports glob patterns.
# For example, if you want to serve static HTML pages in your function:
# static_files = [ "./functions/createRoomWallet/*.html" ]

[functions.transferToRoom]
enabled = true
verify_jwt = true
import_map = "./functions/transferToRoom/deno.json"
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/transferToRoom/index.ts"
# Specifies static files to be bundled with the function. Supports glob patterns.
# For example, if you want to serve static HTML pages in your function:
# static_files = [ "./functions/transferToRoom/*.html" ]

[functions.cashout]
enabled = true
verify_jwt = true
import_map = "./functions/cashout/deno.json"
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/cashout/index.ts"
# Specifies static files to be bundled with the function. Supports glob patterns.
# For example, if you want to serve static HTML pages in your function:
# static_files = [ "./functions/cashout/*.html" ]

[functions.roomToAdmin]
enabled = true
verify_jwt = true
import_map = "./functions/roomToAdmin/deno.json"
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/roomToAdmin/index.ts"
# Specifies static files to be bundled with the function. Supports glob patterns.
# For example, if you want to serve static HTML pages in your function:
# static_files = [ "./functions/roomToAdmin/*.html" ]
