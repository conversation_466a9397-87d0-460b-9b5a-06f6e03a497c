-- SnakePit Database Schema for Supabase
-- Copy and paste this into your Supabase SQL Editor

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    solana_balance DECIMAL(18,9) DEFAULT 0.000000000,
    total_games_played INTEGER DEFAULT 0,
    total_wins INTEGER DEFAULT 0,
    total_earnings DECIMAL(18,9) DEFAULT 0.000000000,
    audio_muted BOOLEAN DEFAULT false,
    audio_volume DECIMAL(3,2) DEFAULT 0.60,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create solana_wallets table
CREATE TABLE IF NOT EXISTS solana_wallets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) UNIQUE NOT NULL,
    public_key VARCHAR(44) UNIQUE NOT NULL,
    encrypted_private_key TEXT NOT NULL,
    wallet_type VARCHAR(20) DEFAULT 'user' CHECK (wallet_type IN ('user', 'room', 'admin')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create room_wallets table
CREATE TABLE IF NOT EXISTS room_wallets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    room_id VARCHAR(100) UNIQUE NOT NULL,
    public_key VARCHAR(44) UNIQUE NOT NULL,
    encrypted_private_key TEXT NOT NULL,
    current_balance DECIMAL(18,9) DEFAULT 0.000000000,
    total_wagered DECIMAL(18,9) DEFAULT 0.000000000,
    total_paid_out DECIMAL(18,9) DEFAULT 0.000000000,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    destroyed_at TIMESTAMP WITH TIME ZONE
);

-- Create admin_wallet table
CREATE TABLE IF NOT EXISTS admin_wallet (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    public_key VARCHAR(44) UNIQUE NOT NULL,
    encrypted_private_key TEXT NOT NULL,
    total_balance DECIMAL(18,9) DEFAULT 0.000000000,
    total_fees_collected DECIMAL(18,9) DEFAULT 0.000000000,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create game_sessions table
CREATE TABLE IF NOT EXISTS game_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) NOT NULL,
    game_mode VARCHAR(20) NOT NULL CHECK (game_mode IN ('classic', 'warfare')),
    wager_amount DECIMAL(10,2) NOT NULL,
    final_score INTEGER NOT NULL,
    final_length INTEGER NOT NULL,
    final_cash DECIMAL(10,2) NOT NULL,
    duration_seconds INTEGER NOT NULL,
    cashed_out BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create leaderboards table
CREATE TABLE IF NOT EXISTS leaderboards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) NOT NULL,
    username VARCHAR(50) NOT NULL,
    game_mode VARCHAR(20) NOT NULL CHECK (game_mode IN ('classic', 'warfare')),
    high_score INTEGER NOT NULL,
    high_cash DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, game_mode)
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES user_profiles(id) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'wager', 'cashout', 'room_transfer', 'admin_transfer')),
    amount DECIMAL(18,9) NOT NULL,
    solana_signature VARCHAR(88),
    from_wallet VARCHAR(44),
    to_wallet VARCHAR(44),
    room_id VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'failed')),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_game_sessions_user_id ON game_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_created_at ON game_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_leaderboards_game_mode_score ON leaderboards(game_mode, high_score DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_transactions_signature ON transactions(solana_signature);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_room_id ON transactions(room_id);
CREATE INDEX IF NOT EXISTS idx_solana_wallets_user_id ON solana_wallets(user_id);
CREATE INDEX IF NOT EXISTS idx_solana_wallets_public_key ON solana_wallets(public_key);
CREATE INDEX IF NOT EXISTS idx_room_wallets_room_id ON room_wallets(room_id);
CREATE INDEX IF NOT EXISTS idx_room_wallets_public_key ON room_wallets(public_key);
CREATE INDEX IF NOT EXISTS idx_admin_wallet_public_key ON admin_wallet(public_key);

-- Enable Row Level Security (RLS)
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE leaderboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE solana_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_wallet ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_profiles
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for game_sessions
CREATE POLICY "Users can view their own game sessions" ON game_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own game sessions" ON game_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for leaderboards (public read, user can update their own)
CREATE POLICY "Anyone can view leaderboards" ON leaderboards
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own leaderboard entry" ON leaderboards
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own leaderboard entry" ON leaderboards
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for transactions
CREATE POLICY "Users can view their own transactions" ON transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own transactions" ON transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for solana_wallets
CREATE POLICY "Users can view their own wallet" ON solana_wallets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own wallet" ON solana_wallets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own wallet" ON solana_wallets
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for room_wallets (service role access only)
CREATE POLICY "Service role can manage room wallets" ON room_wallets
    FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for admin_wallet (service role access only)
CREATE POLICY "Service role can manage admin wallet" ON admin_wallet
    FOR ALL USING (auth.role() = 'service_role');
