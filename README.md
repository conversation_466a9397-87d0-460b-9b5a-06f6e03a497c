# SnakePit - Multiplayer Snake Arena

A real-time multiplayer snake game with Solana blockchain integration, featuring classic and warfare game modes.

## 🎮 Game Features

- **Real-time Multiplayer**: Up to 100 players per room using Socket.IO
- **Two Game Modes**: Classic snake and Warfare mode with weapons
- **Solana Integration**: SOL-based wagering and rewards system
- **Modern UI**: React-based interface with neon theme
- **Cross-platform**: Web-based, works on desktop and mobile

## 📁 Project Structure

```
snakepit/
├── docs/                    # 📚 Documentation
│   ├── setup/              # Setup and configuration guides
│   ├── guides/             # User and developer guides
│   └── integration/        # Integration documentation
├── src/                    # 🎨 React frontend source
├── server/                 # 🖥️ Node.js backend
├── public/                 # 🌐 Public assets
├── database/               # 🗄️ Database schemas
├── scripts/                # 🔧 Deployment scripts
├── supabase/               # ☁️ Supabase configuration
├── tests/                  # 🧪 Test files
└── tools/                  # 🛠️ Utility tools and scripts
```

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- npm or yarn

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd snakepit

# Install dependencies
npm install

# Install server dependencies
npm run install-server
```

### Development
```bash
# Start both server and client
npm run dev

# Or start separately:
npm run server    # Start server only
npm start         # Start client only
```

### Production
```bash
# Build for production
npm run build

# Start production server
npm run server
```

## 📖 Documentation

- **[Setup Guides](./docs/setup/)** - Installation and configuration
- **[User Guides](./docs/guides/)** - Gameplay and features
- **[Integration Docs](./docs/integration/)** - Solana and Supabase setup
- **[API Documentation](./server/)** - Backend API reference

## 🧪 Testing

```bash
# Run React tests
npm test

# Run game logic tests
node tests/test-filename.js

# View HTML test files
open tests/test.html
```

## 🛠️ Tools

- **[HTML Tools](./tools/html/)** - Standalone utilities and debug pages
- **[Scripts](./tools/scripts/)** - Utility scripts and batch files

## 🌐 Environment Configuration

Create a `.env` file in the root directory:
```env
REACT_APP_SERVER_URL=http://localhost:3005
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_key
```

## 🎯 Game Modes

### Classic Mode
- Traditional snake gameplay
- Collect food to grow
- Avoid other snakes
- Cash-based progression

### Warfare Mode
- Combat-enabled snake battles
- Weapons and powerups
- Projectile physics
- Strategic gameplay

## 🔗 Technology Stack

- **Frontend**: React, TypeScript, Socket.IO Client
- **Backend**: Node.js, Express, Socket.IO
- **Database**: Supabase (PostgreSQL)
- **Blockchain**: Solana Web3.js
- **Styling**: CSS3 with neon theme

## 📄 License

[Add your license information here]

## 🤝 Contributing

[Add contributing guidelines here]
