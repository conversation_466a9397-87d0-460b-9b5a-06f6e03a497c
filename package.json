{"name": "snakepit-react", "version": "0.1.0", "private": true, "dependencies": {"@pixi/core": "^7.4.2", "@pixi/filter-glow": "^5.2.1", "@pythnetwork/client": "^2.22.1", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.26", "@solana/web3.js": "^1.98.2", "@supabase/supabase-js": "^2.49.8", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/three": "^0.178.1", "bs58": "^6.0.0", "crypto-js": "^4.2.0", "dotenv": "^17.2.1", "lucide-react": "^0.512.0", "node-fetch": "^2.7.0", "pixi.js": "^7.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "socket.io-client": "^4.8.1", "swiper": "^11.2.8", "three": "^0.178.0", "three-stdlib": "^2.36.0", "twgl.js": "^5.5.4", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@craco/craco": "^7.1.0", "@types/crypto-js": "^4.2.2", "buffer": "^6.0.3", "concurrently": "^8.2.2", "cors": "^2.8.5", "crypto-browserify": "^3.12.1", "express": "^4.21.2", "nodemon": "^3.1.10", "path-browserify": "^1.0.1", "socket.io": "^4.8.1", "stream-browserify": "^3.0.0", "util": "^0.12.5"}, "scripts": {"start": "set NODE_NO_WARNINGS=1&& craco start", "start-lan": "set HOST=0.0.0.0&& set NODE_NO_WARNINGS=1&& craco start", "build": "set NODE_NO_WARNINGS=1&& craco build", "test": "set NODE_NO_WARNINGS=1&& craco test", "eject": "react-scripts eject", "server": "set NODE_NO_WARNINGS=1&& nodemon server/index.js", "dev": "concurrently \"npm run server\" \"npm start\"", "dev-lan": "concurrently \"npm run server\" \"npm run start-lan\"", "install-server": "npm install express socket.io cors nodemon concurrently"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3005", "overrides": {"@solana/buffer-layout": {"source-map-loader": "^4.0.1"}, "superstruct": {"source-map-loader": "^4.0.1"}}}